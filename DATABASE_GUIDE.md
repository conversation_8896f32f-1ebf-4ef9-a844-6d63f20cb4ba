# 🗄️ دليل قاعدة البيانات - Instagram Extractor Professional v4.0

## 🚀 **نظام قاعدة البيانات الشامل**

تم تطوير نظام قاعدة بيانات متكامل وشامل باستخدام SQLite مع جميع الميزات المطلوبة لحفظ وإدارة جميع بيانات المشروع.

---

## 📊 **هيكل قاعدة البيانات**

### 🗂️ **الجداول الرئيسية:**

#### 1. **👤 جدول المستخدمين (users)**
```sql
- id: معرف المستخدم (مفتاح أساسي)
- username: اسم المستخدم (فريد)
- email: البريد الإلكتروني (فريد)
- password_hash: كلمة المرور المشفرة
- full_name: الا<PERSON><PERSON> الكامل
- role: ا<PERSON><PERSON><PERSON><PERSON> (admin, user, viewer)
- avatar_url: رابط الصورة الشخصية
- is_active: نشط أم لا
- is_verified: موثق أم لا
- last_login: آخر تسجيل دخول
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

#### 2. **📱 جدول حسابات Instagram (instagram_accounts)**
```sql
- id: معرف الحساب (مفتاح أساسي)
- user_id: معرف المستخدم المالك
- name: اسم الحساب
- username: اسم المستخدم في Instagram (فريد)
- email: البريد الإلكتروني
- password_encrypted: كلمة المرور المشفرة
- cookies_data: بيانات الكوكيز
- session_data: بيانات الجلسة
- is_active: نشط أم لا
- is_verified: موثق أم لا
- is_business: حساب تجاري أم لا
- follower_count: عدد المتابعين
- following_count: عدد المتابَعين
- post_count: عدد المنشورات
- bio: النبذة الشخصية
- profile_pic_url: رابط الصورة الشخصية
- last_used: آخر استخدام
- status: الحالة (active, suspended, banned, error)
- notes: ملاحظات
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

#### 3. **🔍 جدول عمليات الاستخراج (extractions)**
```sql
- id: معرف العملية (مفتاح أساسي)
- user_id: معرف المستخدم
- account_id: معرف الحساب المستخدم
- post_url: رابط المنشور
- post_id: معرف المنشور
- extraction_type: نوع الاستخراج (likes, comments, followers)
- status: الحالة (pending, running, completed, failed, cancelled)
- total_count: العدد الإجمالي
- extracted_count: العدد المستخرج
- progress_percentage: نسبة التقدم
- start_time, end_time: أوقات البداية والنهاية
- duration_seconds: مدة العملية بالثواني
- error_message: رسالة الخطأ (إن وجدت)
- extraction_settings: إعدادات الاستخراج (JSON)
- metadata: معلومات إضافية (JSON)
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

#### 4. **👥 جدول المستخدمين المستخرجين (extracted_users)**
```sql
- id: معرف المستخدم المستخرج (مفتاح أساسي)
- extraction_id: معرف عملية الاستخراج
- username: اسم المستخدم
- full_name: الاسم الكامل
- profile_pic_url: رابط الصورة الشخصية
- profile_url: رابط الملف الشخصي
- is_verified: موثق أم لا
- is_private: خاص أم لا
- follower_count: عدد المتابعين
- following_count: عدد المتابَعين
- post_count: عدد المنشورات
- bio: النبذة الشخصية
- threads_url: رابط Threads
- facebook_url: رابط Facebook
- external_url: رابط خارجي
- category: الفئة (personal, business, creator)
- location: الموقع
- contact_info: معلومات الاتصال (JSON)
- extracted_at: تاريخ الاستخراج
```

#### 5. **🔔 جدول الإشعارات (notifications)**
```sql
- id: معرف الإشعار (مفتاح أساسي)
- user_id: معرف المستخدم
- title: عنوان الإشعار
- message: نص الإشعار
- type: النوع (success, error, warning, info)
- category: الفئة (extraction, account, system, security)
- is_read: مقروء أم لا
- is_global: إشعار عام أم خاص
- action_url: رابط الإجراء
- metadata: معلومات إضافية (JSON)
- expires_at: تاريخ انتهاء الصلاحية
- created_at: تاريخ الإنشاء
```

#### 6. **📋 جدول سجل النشاط (activity_logs)**
```sql
- id: معرف السجل (مفتاح أساسي)
- user_id: معرف المستخدم
- action: الإجراء (login, logout, extract, add_account, etc.)
- entity_type: نوع الكيان (user, account, extraction)
- entity_id: معرف الكيان
- description: الوصف
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- metadata: معلومات إضافية (JSON)
- created_at: تاريخ الإنشاء
```

#### 7. **⚙️ جدول الإعدادات (settings)**
```sql
- id: معرف الإعداد (مفتاح أساسي)
- user_id: معرف المستخدم (NULL للإعدادات العامة)
- category: الفئة (general, extraction, notifications, security)
- key: المفتاح
- value: القيمة
- data_type: نوع البيانات (string, integer, boolean, json)
- is_global: إعداد عام أم خاص
- description: الوصف
- created_at, updated_at: تواريخ الإنشاء والتحديث
```

#### 8. **🔐 جدول الجلسات (sessions)**
```sql
- id: معرف الجلسة (مفتاح أساسي)
- user_id: معرف المستخدم
- session_token: رمز الجلسة (فريد)
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- is_active: نشطة أم لا
- last_activity: آخر نشاط
- expires_at: تاريخ انتهاء الصلاحية
- created_at: تاريخ الإنشاء
```

#### 9. **📊 جدول الإحصائيات (statistics)**
```sql
- id: معرف الإحصائية (مفتاح أساسي)
- user_id: معرف المستخدم
- date: التاريخ
- metric_name: اسم المقياس
- metric_value: قيمة المقياس
- metadata: معلومات إضافية (JSON)
- created_at: تاريخ الإنشاء
```

#### 10. **📁 جدول الملفات المصدرة (exported_files)**
```sql
- id: معرف الملف (مفتاح أساسي)
- user_id: معرف المستخدم
- extraction_id: معرف عملية الاستخراج
- filename: اسم الملف
- file_path: مسار الملف
- file_type: نوع الملف (csv, excel, json, pdf)
- file_size: حجم الملف بالبايت
- download_count: عدد مرات التحميل
- is_available: متاح أم لا
- expires_at: تاريخ انتهاء الصلاحية
- created_at: تاريخ الإنشاء
```

---

## 🔧 **مدير قاعدة البيانات (DatabaseManager)**

### 📋 **الوظائف الرئيسية:**

#### 👤 **إدارة المستخدمين:**
```python
- create_user(): إنشاء مستخدم جديد
- authenticate_user(): التحقق من صحة بيانات المستخدم
- get_user_by_id(): الحصول على مستخدم بالمعرف
```

#### 📱 **إدارة حسابات Instagram:**
```python
- add_instagram_account(): إضافة حساب Instagram
- get_user_accounts(): الحصول على حسابات المستخدم
- get_all_accounts(): الحصول على جميع الحسابات
- update_account_status(): تحديث حالة الحساب
```

#### 🔍 **إدارة عمليات الاستخراج:**
```python
- create_extraction(): إنشاء عملية استخراج جديدة
- update_extraction_progress(): تحديث تقدم العملية
- get_user_extractions(): الحصول على عمليات المستخدم
- get_all_extractions(): الحصول على جميع العمليات
```

#### 👥 **إدارة المستخدمين المستخرجين:**
```python
- add_extracted_user(): إضافة مستخدم مستخرج
- get_extraction_users(): الحصول على مستخدمي عملية معينة
```

#### 🔔 **إدارة الإشعارات:**
```python
- create_notification(): إنشاء إشعار جديد
- get_user_notifications(): الحصول على إشعارات المستخدم
- mark_notification_read(): تحديد إشعار كمقروء
```

#### 📊 **إدارة الإحصائيات:**
```python
- get_dashboard_stats(): الحصول على إحصائيات لوحة التحكم
- log_activity(): تسجيل نشاط المستخدم
- get_recent_activity(): الحصول على النشاط الأخير
```

#### 🛠️ **دوال مساعدة:**
```python
- cleanup_old_data(): تنظيف البيانات القديمة
- get_database_info(): الحصول على معلومات قاعدة البيانات
```

---

## 🌐 **خادم قاعدة البيانات (database_server.py)**

### 📡 **API Endpoints:**

#### 🔍 **العامة:**
```
GET  /api/test - اختبار الاتصال
GET  /api/dashboard/stats - إحصائيات لوحة التحكم
```

#### 📱 **الحسابات:**
```
GET  /api/accounts - جلب جميع الحسابات
POST /api/accounts - إضافة حساب جديد
PUT  /api/accounts/{id} - تحديث حساب
```

#### 🔍 **الاستخراجات:**
```
GET  /api/extractions - جلب عمليات الاستخراج
POST /api/extractions - بدء عملية استخراج جديدة
GET  /api/extractions/{id}/users - جلب المستخدمين المستخرجين
```

#### 🔔 **الإشعارات:**
```
GET  /api/notifications - جلب الإشعارات
POST /api/notifications/{id}/read - تحديد إشعار كمقروء
```

#### 📋 **النشاط:**
```
GET  /api/activity - جلب النشاط الأخير
```

---

## 🚀 **كيفية الاستخدام:**

### 1. **تهيئة قاعدة البيانات:**
```bash
cd backend
python init_database.py
```

### 2. **تشغيل خادم قاعدة البيانات:**
```bash
python database_server.py
```

### 3. **تشغيل النظام الكامل:**
```bash
start_database_system.bat
```

---

## 📊 **البيانات التجريبية:**

### 👤 **المستخدمين:**
- **admin**: مدير النظام (<EMAIL>)
- **developer**: المطور العربي (<EMAIL>)

### 📱 **حسابات Instagram:**
- **business_account**: حساب العمل الرئيسي
- **personal_verified**: حساب شخصي موثق
- **creative_content**: حساب المحتوى الإبداعي

### 🔍 **عمليات الاستخراج:**
- **4 عمليات مكتملة** مع أكثر من 1000 مستخدم مستخرج
- **إحصائيات متنوعة** للاختبار والعرض

### 🔔 **الإشعارات:**
- **إشعارات ترحيب** ونصائح للاستخدام
- **إشعارات العمليات** والأنشطة
- **تصنيفات متنوعة** (نجاح، معلومات، تحذير، خطأ)

---

## 🔒 **الأمان والخصوصية:**

### 🛡️ **ميزات الأمان:**
- **تشفير كلمات المرور** باستخدام SHA256
- **حماية من SQL Injection** مع استعلامات محضرة
- **تسجيل جميع العمليات** في سجل النشاط
- **إدارة الجلسات الآمنة** مع انتهاء صلاحية
- **تشفير بيانات الحسابات** الحساسة

### 🔐 **إدارة الصلاحيات:**
- **أدوار المستخدمين** (admin, user, viewer)
- **صلاحيات متدرجة** حسب الدور
- **تتبع الوصول** والعمليات

---

## 📈 **تحسينات الأداء:**

### ⚡ **الفهارس:**
```sql
- فهارس على أسماء المستخدمين
- فهارس على معرفات المستخدمين
- فهارس على حالات الاستخراج
- فهارس على تواريخ الإنشاء
- فهارس على رموز الجلسات
```

### 🗄️ **تحسينات قاعدة البيانات:**
- **استعلامات محسنة** للأداء السريع
- **تنظيف البيانات القديمة** تلقائياً
- **ضغط البيانات** لتوفير المساحة
- **نسخ احتياطي** دوري

---

## 🛠️ **الصيانة والإدارة:**

### 🔧 **أدوات الصيانة:**
```python
# تنظيف البيانات القديمة (أكثر من 30 يوم)
db.cleanup_old_data(30)

# الحصول على معلومات قاعدة البيانات
info = db.get_database_info()

# إعادة تهيئة قاعدة البيانات
python init_database.py
```

### 📊 **مراقبة الأداء:**
- **حجم قاعدة البيانات**: مراقبة نمو الملف
- **عدد السجلات**: تتبع نمو البيانات
- **سرعة الاستعلامات**: قياس أوقات الاستجابة
- **استخدام الذاكرة**: مراقبة استهلاك الموارد

---

## 🎉 **النتيجة النهائية:**

### ✅ **تم تحقيق:**
- **🗄️ قاعدة بيانات شاملة** مع 10 جداول رئيسية
- **🔧 مدير قاعدة بيانات متكامل** مع جميع العمليات
- **🌐 خادم API متقدم** مع endpoints شاملة
- **📊 إحصائيات حقيقية** من قاعدة البيانات
- **🔔 نظام إشعارات متكامل** مع قاعدة البيانات
- **📋 سجل نشاط شامل** لجميع العمليات
- **🔒 أمان متقدم** مع تشفير وحماية
- **⚡ أداء محسن** مع فهارس وتحسينات
- **🛠️ أدوات صيانة** وإدارة متقدمة
- **📊 بيانات تجريبية** للاختبار والعرض

**الآن لديك نظام قاعدة بيانات احترافي وشامل يحفظ كل شيء! 🗄️🚀**
