# 🚀 دليل البدء السريع - Instagram Likes Extractor

## ✅ الحالة الحالية للمشروع

### ما يعمل الآن:
- ✅ **الواجهة الأمامية**: React + TailwindCSS تعمل بشكل مثالي
- ✅ **Python Scripts**: نظام استخراج البيانات جاهز مع نظام تسجيل شامل
- ✅ **نظام التصدير**: CSV, JSON يعملان (Excel يحتاج pandas)
- ✅ **التصميم**: واجهة عربية احترافية مع الوضع الليلي
- ✅ **نظام التسجيل**: logger.py, error_tracker.py, monitor_errors.py
- ✅ **مشكلة TailwindCSS**: تم حلها بالكامل
- ⚠️ **Tauri Backend**: يحتاج تثبيت Rust

### ما تم اختباره:
- ✅ تشغيل الواجهة الأمامية على http://localhost:1421
- ✅ تصدير البيانات التجريبية بصيغ CSV و JSON
- ✅ التحقق من صحة روابط إنستجرام
- ✅ نظام المكونات React يعمل بشكل صحيح
- ✅ حل مشكلة TailwindCSS PostCSS نهائياً

## 🏃‍♂️ تشغيل سريع

### الطريقة الأسهل (Windows):
```bash
# انقر مرتين على الملف
run.bat
```

### الطريقة الأسهل (Linux/Mac):
```bash
./run.sh
```

### تشغيل يدوي:
```bash
# تشغيل الواجهة الأمامية فقط
npm run dev

# أو تشغيل التطبيق كاملاً (يحتاج Rust)
npm run tauri dev
```

## 📱 كيفية الاستخدام

### 1. افتح التطبيق
- سيفتح المتصفح تلقائياً على http://localhost:1420
- ستظهر واجهة عربية احترافية

### 2. أدخل رابط المنشور
- انسخ رابط أي منشور من إنستجرام
- مثال: `https://www.instagram.com/p/ABC123/`
- الصق الرابط في الحقل المخصص

### 3. ابدأ الاستخراج
- انقر على زر "ابدأ الاستخراج"
- انتظر حتى اكتمال العملية

### 4. عرض النتائج
- ستظهر النتائج في جدول تفاعلي
- يمكنك البحث والتصفية
- عرض صور البروفايل والمعلومات

### 5. تصدير البيانات
- اختر صيغة التصدير (CSV, Excel, JSON)
- ستحفظ الملفات في مجلد `exports/`

## 🔧 المتطلبات

### الأساسية (متوفرة):
- ✅ Python 3.11.8
- ✅ Node.js
- ✅ تبعيات Python مثبتة

### للتطبيق الكامل:
- ⚠️ Rust (غير مثبت حالياً)

## 🛠️ تثبيت Rust (اختياري)

### Windows:
```bash
# استخدم PowerShell كمدير
winget install Rustlang.Rust

# أو حمل من الموقع الرسمي
# https://rustup.rs/
```

### Linux/Mac:
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

## 📁 هيكل المشروع

```
instagram_scraper/
├── 🌐 src/                    # React frontend (يعمل)
│   ├── components/            # مكونات React
│   ├── App.tsx               # التطبيق الرئيسي
│   └── main.tsx              # نقطة البداية
├── 🦀 src-tauri/             # Tauri backend (يحتاج Rust)
├── 🐍 scripts/               # Python scripts (يعمل)
│   ├── instagram_extractor.py # مستخرج البيانات
│   └── data_exporter.py      # نظام التصدير
├── 📤 exports/               # ملفات التصدير
├── 🚀 run.bat               # تشغيل Windows
├── 🚀 run.sh                # تشغيل Linux/Mac
└── 🧪 test_extractor.py     # اختبار النظام
```

## 🧪 اختبار النظام

```bash
# تشغيل الاختبارات
python test_extractor.py

# سيختبر:
# - التحقق من الروابط
# - تصدير البيانات
# - إنشاء ملفات تجريبية
```

## 📝 نظام التسجيل والمراقبة

### ملفات التسجيل:
- `logs/instagram_extractor_YYYYMMDD.log` - لوج عام للاستخراج
- `logs/errors_YYYYMMDD.log` - أخطاء فقط
- `logs/error_tracker.json` - تتبع الأخطاء التلقائي
- `logs/known_issues.md` - المشاكل المعروفة والحلول
- `logs/startup_log.md` - سجل بدء التشغيل
- `logs/current_session.log` - سجل الجلسة الحالية

### مراقبة الأخطاء التلقائية:
```bash
# تشغيل مراقب الأخطاء (اختياري)
python monitor_errors.py

# سيراقب مجلد logs/ ويعرض الأخطاء فور حدوثها
```

### اختبار نظام التسجيل:
```bash
# اختبار نظام التسجيل
python scripts/logger.py

# اختبار تتبع الأخطاء
python scripts/error_tracker.py
```

## 🚨 ملاحظات مهمة

### الاستخدام المسؤول:
- استخدم التطبيق وفقاً لشروط خدمة إنستجرام
- لا تفرط في الاستخدام لتجنب الحظر
- احترم خصوصية المستخدمين

### الحماية من الحظر:
- يستخدم undetected-chromedriver
- تأخير عشوائي بين العمليات
- User-Agent متغير
- تمرير طبيعي

## 🐛 حل المشاكل

### المشروع لا يعمل؟
```bash
# تأكد من التبعيات
python --version
node --version

# أعد تثبيت التبعيات
npm install
python -m pip install -r requirements.txt
```

### خطأ في Tauri؟
```bash
# ثبت Rust أولاً
# ثم أعد المحاولة
npm run tauri dev
```

### خطأ في Python؟
```bash
# تأكد من المسار
cd instagram_scraper
python test_extractor.py
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. شغل `test_extractor.py` للتأكد من عمل Python
3. تأكد من عمل الواجهة على http://localhost:1420
4. راجع ملف README.md للتفاصيل الكاملة

---

**🎉 التطبيق جاهز للاستخدام! استمتع باستخراج بيانات إنستجرام بطريقة احترافية وآمنة.**
