# Instagram Likes Extractor 📸

تطبيق مكتبي متقدم لاستخراج قائمة المعجبين من منشورات إنستجرام مع معلومات مفصلة عن كل حساب.

## ✨ المميزات

- 🔍 **استخراج شامل**: استخراج جميع المعجبين من أي منشور إنستجرام
- 🖼️ **صور عالية الجودة**: تحميل صور البروفايل بأفضل جودة متاحة
- 🔗 **ربط الحسابات**: العثور على حسابات Threads و Facebook المرتبطة
- 📊 **معلومات مفصلة**: عدد المتابعين، المتابَعين، المنشورات، والبايو
- 📤 **تصدير متعدد**: CSV, Excel, JSON
- 🌙 **الوضع الليلي**: واجهة مريحة للعينين
- 🛡️ **حماية من الحظر**: تقنيات متقدمة لتجنب حظر إنستجرام
- 🎯 **واجهة عربية**: دعم كامل للغة العربية

## 🛠️ التقنيات المستخدمة

- **الواجهة**: React.js + TypeScript + TailwindCSS
- **التطبيق المكتبي**: Tauri (Rust)
- **استخراج البيانات**: Python + Selenium + undetected-chromedriver
- **قاعدة البيانات**: JSON محلي
- **التصدير**: pandas + openpyxl

## 📋 المتطلبات

### النظام
- Windows 10/11, macOS 10.15+, أو Linux
- Chrome/Chromium مثبت
- Python 3.8+
- Node.js 16+
- Rust 1.70+

### التبعيات
```bash
# Python packages
pip install -r requirements.txt

# Node.js packages
npm install
```

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd instagram_scraper
```

### 2. تثبيت Rust (إذا لم يكن مثبتاً)
```bash
# Windows
winget install Rustlang.Rust

# macOS/Linux
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 3. تثبيت التبعيات
```bash
# Python dependencies
pip install -r requirements.txt

# Node.js dependencies
npm install
```

### 4. تشغيل التطبيق
```bash
# Development mode
npm run tauri dev

# Build for production
npm run tauri build
```

## 📖 طريقة الاستخدام

### 1. تشغيل التطبيق
- افتح التطبيق
- أدخل رابط منشور إنستجرام في الحقل المخصص
- انقر على "ابدأ الاستخراج"

### 2. انتظار النتائج
- سيفتح المتصفح تلقائياً
- قد تحتاج لتسجيل الدخول إلى إنستجرام
- انتظر حتى اكتمال عملية الاستخراج

### 3. عرض وتصدير النتائج
- ستظهر النتائج في جدول تفاعلي
- يمكنك البحث والتصفية
- اختر صيغة التصدير المناسبة (CSV, Excel, JSON)

## 🔧 الإعدادات المتقدمة

### تخصيص عملية الاستخراج
يمكنك تعديل الإعدادات في `scripts/instagram_extractor.py`:

```python
# عدد المحاولات القصوى للتمرير
max_scrolls = 50

# التأخير بين العمليات (ثواني)
delay_range = (1, 3)

# تفعيل/تعطيل تحسين البيانات
enhance_data = False
```

### إعدادات الحماية من الحظر
- استخدام User-Agent عشوائي
- تأخير عشوائي بين العمليات
- إخفاء خصائص الأتمتة
- تمرير طبيعي ومتدرج

## 📁 هيكل المشروع

```
instagram_scraper/
├── src/                    # React frontend
│   ├── components/         # React components
│   ├── types.ts           # TypeScript types
│   ├── App.tsx            # Main app component
│   └── main.tsx           # Entry point
├── src-tauri/             # Tauri backend
│   ├── src/
│   │   └── lib.rs         # Rust commands
│   └── Cargo.toml         # Rust dependencies
├── scripts/               # Python scripts
│   ├── instagram_extractor.py
│   └── data_exporter.py
├── exports/               # Exported files
└── requirements.txt       # Python dependencies
```

## 🚨 تحذيرات مهمة

### الاستخدام المسؤول
- استخدم التطبيق بمسؤولية وفقاً لشروط خدمة إنستجرام
- لا تفرط في الاستخدام لتجنب الحظر
- احترم خصوصية المستخدمين

### الحماية من الحظر
- لا تستخدم التطبيق بشكل مكثف
- خذ فترات راحة بين الاستخدامات
- استخدم حسابات مختلفة إذا لزم الأمر

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في تشغيل Python
```bash
# تأكد من تثبيت Python
python --version

# تثبيت التبعيات
pip install -r requirements.txt
```

#### خطأ في Chrome Driver
```bash
# تحديث undetected-chromedriver
pip install --upgrade undetected-chromedriver
```

#### خطأ في Tauri
```bash
# تأكد من تثبيت Rust
rustc --version

# إعادة بناء المشروع
npm run tauri build
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:

- افتح Issue في GitHub
- راسلنا عبر البريد الإلكتروني
- انضم إلى مجتمع Discord

## 🙏 شكر خاص

- فريق Tauri لإطار العمل الرائع
- مطوري Selenium و undetected-chromedriver
- مجتمع React.js و TailwindCSS

---

**تنبيه**: هذا التطبيق مخصص للأغراض التعليمية والبحثية. يرجى استخدامه بمسؤولية واحترام شروط الخدمة.
