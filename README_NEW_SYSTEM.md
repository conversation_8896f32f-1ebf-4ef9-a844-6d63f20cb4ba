# 🚀 Instagram Likes Extractor v2.0 - النظام المنفصل

## 📋 نظرة عامة

تم إعادة تصميم Instagram Likes Extractor ليكون نظاماً منفصلاً تماماً مع:
- **🔧 Backend**: FastAPI + SQLite + Python
- **🌐 Frontend**: React + TypeScript + TailwindCSS
- **👥 لوحة تحكم**: إدارة حسابات Instagram
- **🔐 أمان**: تشفير كلمات المرور وحماية البيانات

---

## 🏗️ هيكل المشروع الجديد

```
instagram_scraper/
├── 🔧 backend/                 # FastAPI Backend
│   ├── main.py                 # API الرئيسي
│   ├── database.py             # قاعدة البيانات
│   ├── models.py               # نماذج Pydantic
│   ├── security.py             # الأمان والتشفير
│   ├── instagram_service.py    # خدمة الاستخراج
│   └── requirements.txt        # متطلبات Python
│
├── 🌐 frontend/                # React Frontend
│   ├── src/
│   │   ├── components/         # مكونات React
│   │   ├── services/           # خدمات API
│   │   └── types.ts            # أنواع TypeScript
│   ├── package.json
│   └── vite.config.ts
│
├── 🗄️ database/                # قاعدة البيانات
│   └── instagram_accounts.db   # SQLite Database
│
├── 📁 shared/                  # ملفات مشتركة
│   └── utils/                  # أدوات مساعدة
│
├── 🚀 start_backend.py         # تشغيل Backend
├── 🌐 start_frontend.bat       # تشغيل Frontend
└── 🎯 start_full_system.bat    # تشغيل النظام الكامل
```

---

## 🚀 التشغيل السريع

### 1. تشغيل النظام الكامل (الأسهل)
```bash
# Windows
start_full_system.bat
```

### 2. تشغيل منفصل

#### Backend (Port 8000):
```bash
python start_backend.py
```

#### Frontend (Port 3000):
```bash
# Windows
start_frontend.bat

# أو يدوياً
cd frontend
npm install
npm run dev
```

---

## 🆕 الميزات الجديدة

### 👥 لوحة تحكم الحسابات
- **إضافة حسابات Instagram**: اسم مستخدم وكلمة مرور
- **تشفير آمن**: جميع كلمات المرور مشفرة
- **إدارة شاملة**: تعديل، حذف، تفعيل/إلغاء تفعيل
- **معلومات تفصيلية**: بريد إلكتروني، هاتف، ملاحظات

### 🔧 Backend API متقدم
- **FastAPI**: API سريع ومتطور
- **SQLite Database**: قاعدة بيانات محلية
- **نظام أمان**: تشفير وحماية البيانات
- **WebSocket**: تحديثات مباشرة (قريباً)

### 🌐 Frontend محسن
- **React + TypeScript**: تطوير متقدم
- **TailwindCSS**: تصميم احترافي
- **حالة الاتصال**: عرض حالة Backend
- **وضع المحاكاة**: يعمل بدون Backend

### 📊 نظام تتبع متقدم
- **9 خطوات واضحة**: من البداية للنهاية
- **تحديثات مباشرة**: تتبع التقدم لحظة بلحظة
- **سجل العمليات**: timestamps مفصلة
- **إحصائيات شاملة**: للنظام والحسابات

---

## 🔐 الأمان والحماية

### تشفير البيانات
- **bcrypt**: تشفير كلمات المرور
- **JWT**: رموز الوصول الآمنة
- **HTTPS**: اتصال آمن (في الإنتاج)

### حماية الحسابات
- **تقنيات متقدمة**: لتجنب حظر Instagram
- **معدل محدود**: للطلبات والاستخراج
- **مراقبة الأخطاء**: تتبع وتسجيل شامل

---

## 📖 دليل الاستخدام

### 1. إعداد الحسابات
1. افتح التطبيق على `http://localhost:3000`
2. انتقل إلى "👥 إدارة الحسابات"
3. اضغط "+ إضافة حساب جديد"
4. أدخل البيانات:
   - **اسم الحساب**: للعرض (مثل: حساب العمل)
   - **اسم المستخدم**: username في Instagram
   - **كلمة المرور**: كلمة مرور Instagram
   - **بيانات إضافية**: بريد إلكتروني، هاتف، ملاحظات

### 2. استخراج المعجبين
1. انتقل إلى "🔍 استخراج المعجبين"
2. تأكد من اختيار حساب من لوحة التحكم
3. أدخل رابط منشور Instagram
4. اضغط "ابدأ الاستخراج"
5. تابع التقدم المباشر
6. احصل على النتائج وصدرها

### 3. تصدير البيانات
- **CSV**: للاستخدام في Excel
- **JSON**: للمطورين والتطبيقات
- **Excel**: ملف Excel مباشر

---

## 🔧 API Documentation

### Backend API Endpoints

#### الحسابات
- `GET /api/accounts` - قائمة الحسابات
- `POST /api/accounts` - إضافة حساب جديد
- `PUT /api/accounts/{id}` - تحديث حساب
- `DELETE /api/accounts/{id}` - حذف حساب

#### الاستخراج
- `POST /api/extract` - بدء مهمة استخراج
- `GET /api/jobs` - قائمة المهام
- `GET /api/jobs/{id}` - تفاصيل مهمة
- `GET /api/jobs/{id}/users` - المستخدمين المستخرجين

#### التصدير
- `POST /api/export` - تصدير البيانات
- `GET /exports/{filename}` - تحميل الملف

#### الإحصائيات
- `GET /api/stats/system` - إحصائيات النظام

### API Docs
- **Swagger UI**: `http://localhost:8000/api/docs`
- **ReDoc**: `http://localhost:8000/api/redoc`

---

## 🗄️ قاعدة البيانات

### الجداول
1. **instagram_accounts**: حسابات Instagram
2. **extraction_jobs**: مهام الاستخراج
3. **extracted_users**: المستخدمين المستخرجين

### النسخ الاحتياطي
```bash
# نسخ احتياطي
cp database/instagram_accounts.db backup/

# استعادة
cp backup/instagram_accounts.db database/
```

---

## 🔧 التطوير والتخصيص

### إعداد بيئة التطوير

#### Backend
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

#### Frontend
```bash
cd frontend
npm install
npm run dev
```

### متغيرات البيئة
إنشاء ملف `.env` في مجلد `backend`:
```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///./database/instagram_accounts.db
ACCESS_TOKEN_EXPIRE_MINUTES=30
INSTAGRAM_LOGIN_TIMEOUT=30
MAX_EXTRACTION_USERS=1000
```

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

#### Backend لا يعمل
```bash
# تحقق من Python
python --version

# تثبيت المتطلبات
pip install -r backend/requirements.txt

# تشغيل يدوي
cd backend
python main.py
```

#### Frontend لا يعمل
```bash
# تحقق من Node.js
node --version

# تثبيت المتطلبات
cd frontend
npm install

# تشغيل يدوي
npm run dev
```

#### مشاكل قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
cd backend
python -c "from database import init_database; init_database()"
```

---

## 📊 الإحصائيات والمراقبة

### ملفات السجل
- `logs/current_session.log` - سجل الجلسة الحالية
- `logs/error_tracker.log` - تتبع الأخطاء
- `logs/extraction_jobs.log` - سجل مهام الاستخراج

### مراقبة الأداء
- **CPU Usage**: مراقبة استخدام المعالج
- **Memory Usage**: مراقبة استخدام الذاكرة
- **API Response Time**: زمن استجابة API

---

## 🔮 التحديثات المستقبلية

### قريباً
- **WebSocket**: تحديثات مباشرة للتقدم
- **Multi-threading**: استخراج متوازي
- **Advanced Filters**: تصفية متقدمة للنتائج
- **Batch Processing**: معالجة دفعية للمنشورات

### مستقبلاً
- **Docker Support**: حاويات Docker
- **Cloud Deployment**: نشر سحابي
- **Mobile App**: تطبيق جوال
- **Analytics Dashboard**: لوحة تحليلات متقدمة

---

## 📞 الدعم والمساعدة

### الحصول على المساعدة
1. **مراجعة الوثائق**: هذا الملف
2. **فحص السجلات**: مجلد `logs/`
3. **API Docs**: `http://localhost:8000/api/docs`
4. **GitHub Issues**: للمشاكل التقنية

### المساهمة
- **Fork** المشروع
- **إنشاء branch** للميزة الجديدة
- **Commit** التغييرات
- **إنشاء Pull Request**

---

## ✅ الخلاصة

**Instagram Likes Extractor v2.0** يوفر:
- ✅ **نظام منفصل**: Backend و Frontend مستقلان
- ✅ **لوحة تحكم**: إدارة شاملة للحسابات
- ✅ **أمان متقدم**: تشفير وحماية البيانات
- ✅ **واجهة احترافية**: تصميم عصري ومتجاوب
- ✅ **تتبع مباشر**: مراقبة العمليات لحظة بلحظة
- ✅ **تصدير متعدد**: CSV, JSON, Excel
- ✅ **توثيق شامل**: API docs ودليل المستخدم

**للبدء**: شغل `start_full_system.bat` واستمتع بالنظام الجديد! 🚀
