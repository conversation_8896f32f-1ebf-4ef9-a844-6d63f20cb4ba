"""
قاعدة البيانات - إدارة حسابات Instagram
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# إنشاء مجلد قاعدة البيانات
DATABASE_DIR = os.path.join(os.path.dirname(__file__), "..", "database")
os.makedirs(DATABASE_DIR, exist_ok=True)

# مسار قاعدة البيانات
DATABASE_URL = f"sqlite:///{os.path.join(DATABASE_DIR, 'instagram_accounts.db')}"

# إنشاء محرك قاعدة البيانات
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# قاعدة النماذج
Base = declarative_base()

class InstagramAccount(Base):
    """نموذج حساب Instagram مع دعم 2FA"""
    __tablename__ = "instagram_accounts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="اسم الحساب للعرض")
    username = Column(String(100), unique=True, nullable=False, comment="اسم المستخدم")
    password_hash = Column(String(255), nullable=True, comment="كلمة المرور المشفرة")
    email = Column(String(255), nullable=True, comment="البريد الإلكتروني")
    phone = Column(String(20), nullable=True, comment="رقم الهاتف")

    # 2FA Support
    has_2fa = Column(Boolean, default=False, comment="هل يستخدم المصادقة الثنائية")
    two_fa_secret = Column(String(255), nullable=True, comment="TOTP secret مشفر")
    two_fa_backup_codes = Column(Text, nullable=True, comment="backup codes مشفرة")
    two_fa_method = Column(String(20), nullable=True, comment="طريقة 2FA: app, sms, email")

    # Account Status
    is_active = Column(Boolean, default=True, comment="هل الحساب نشط")
    is_verified = Column(Boolean, default=False, comment="هل الحساب موثق")
    is_locked = Column(Boolean, default=False, comment="هل الحساب مقفل")
    login_attempts = Column(Integer, default=0, comment="عدد محاولات تسجيل الدخول")
    last_login_attempt = Column(DateTime, nullable=True, comment="آخر محاولة تسجيل دخول")

    # Timestamps
    last_used = Column(DateTime, nullable=True, comment="آخر استخدام")
    created_at = Column(DateTime, default=datetime.utcnow, comment="تاريخ الإنشاء")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="تاريخ التحديث")

    # Additional Info
    notes = Column(Text, nullable=True, comment="ملاحظات")
    profile_pic_url = Column(String(500), nullable=True, comment="رابط صورة البروفايل")
    follower_count = Column(Integer, default=0, comment="عدد المتابعين")
    following_count = Column(Integer, default=0, comment="عدد المتابعين")

    def __repr__(self):
        return f"<InstagramAccount(username='{self.username}', name='{self.name}', has_2fa={self.has_2fa})>"

class ExtractionJob(Base):
    """نموذج مهمة الاستخراج"""
    __tablename__ = "extraction_jobs"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, nullable=False, comment="معرف الحساب المستخدم")
    post_url = Column(String(500), nullable=False, comment="رابط المنشور")
    status = Column(String(50), default="pending", comment="حالة المهمة")
    total_likes = Column(Integer, default=0, comment="إجمالي المعجبين")
    extracted_count = Column(Integer, default=0, comment="عدد المستخرجين")
    started_at = Column(DateTime, nullable=True, comment="وقت البدء")
    completed_at = Column(DateTime, nullable=True, comment="وقت الانتهاء")
    error_message = Column(Text, nullable=True, comment="رسالة الخطأ")
    created_at = Column(DateTime, default=datetime.utcnow, comment="تاريخ الإنشاء")

    def __repr__(self):
        return f"<ExtractionJob(id={self.id}, status='{self.status}')>"

class ExtractedUser(Base):
    """نموذج المستخدم المستخرج"""
    __tablename__ = "extracted_users"

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, nullable=False, comment="معرف المهمة")
    username = Column(String(100), nullable=False, comment="اسم المستخدم")
    full_name = Column(String(200), nullable=True, comment="الاسم الكامل")
    profile_pic_url = Column(String(500), nullable=True, comment="رابط صورة البروفايل")
    profile_url = Column(String(500), nullable=True, comment="رابط البروفايل")
    is_verified = Column(Boolean, default=False, comment="هل الحساب موثق")
    follower_count = Column(Integer, nullable=True, comment="عدد المتابعين")
    following_count = Column(Integer, nullable=True, comment="عدد المتابعين")
    post_count = Column(Integer, nullable=True, comment="عدد المنشورات")
    bio = Column(Text, nullable=True, comment="البايو")
    threads_url = Column(String(500), nullable=True, comment="رابط Threads")
    facebook_url = Column(String(500), nullable=True, comment="رابط Facebook")
    extracted_at = Column(DateTime, default=datetime.utcnow, comment="تاريخ الاستخراج")

    def __repr__(self):
        return f"<ExtractedUser(username='{self.username}')>"

# إنشاء الجداول
def create_tables():
    """إنشاء جميع الجداول"""
    Base.metadata.create_all(bind=engine)

# الحصول على جلسة قاعدة البيانات
def get_db():
    """الحصول على جلسة قاعدة البيانات"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# تهيئة قاعدة البيانات
def init_database():
    """تهيئة قاعدة البيانات"""
    create_tables()
    print(f"✅ تم إنشاء قاعدة البيانات: {DATABASE_URL}")

if __name__ == "__main__":
    init_database()
