"""
إدارة قاعدة البيانات - Instagram Extractor Professional
مدير قاعدة البيانات الشامل مع جميع العمليات المطلوبة
"""

import sqlite3
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import os
from pathlib import Path

class DatabaseManager:
    def __init__(self, db_path: str = "database/instagram_extractor.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
    
    def ensure_database_directory(self):
        """التأكد من وجود مجلد قاعدة البيانات"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir:
            Path(db_dir).mkdir(parents=True, exist_ok=True)
    
    def get_connection(self) -> sqlite3.Connection:
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        conn.execute("PRAGMA foreign_keys = ON")  # تفعيل المفاتيح الخارجية
        return conn
    
    def init_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
        
        if os.path.exists(schema_path):
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            
            with self.get_connection() as conn:
                conn.executescript(schema_sql)
                conn.commit()
                print("✅ تم تهيئة قاعدة البيانات بنجاح")
        else:
            print("❌ ملف schema.sql غير موجود")
    
    # ==================== إدارة المستخدمين ====================
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str = None, role: str = 'user') -> int:
        """إنشاء مستخدم جديد"""
        password_hash = self._hash_password(password)
        
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            """, (username, email, password_hash, full_name, role))
            
            user_id = cursor.lastrowid
            
            # تسجيل النشاط
            self.log_activity(user_id, 'user_created', 'user', user_id, 
                            f'تم إنشاء مستخدم جديد: {username}')
            
            conn.commit()
            return user_id
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """التحقق من صحة بيانات المستخدم"""
        password_hash = self._hash_password(password)
        
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM users 
                WHERE (username = ? OR email = ?) AND password_hash = ? AND is_active = 1
            """, (username, username, password_hash))
            
            user = cursor.fetchone()
            if user:
                # تحديث آخر تسجيل دخول
                conn.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                """, (user['id'],))
                
                # تسجيل النشاط
                self.log_activity(user['id'], 'login', 'user', user['id'], 
                                f'تسجيل دخول: {username}')
                
                conn.commit()
                return dict(user)
            
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """الحصول على مستخدم بالمعرف"""
        with self.get_connection() as conn:
            cursor = conn.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            return dict(user) if user else None
    
    # ==================== إدارة حسابات Instagram ====================
    
    def add_instagram_account(self, user_id: int, name: str, username: str, 
                            email: str = None, **kwargs) -> int:
        """إضافة حساب Instagram جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO instagram_accounts 
                (user_id, name, username, email, password_encrypted, cookies_data, 
                 session_data, is_verified, is_business, bio, profile_pic_url, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, name, username, email,
                kwargs.get('password_encrypted'),
                kwargs.get('cookies_data'),
                kwargs.get('session_data'),
                kwargs.get('is_verified', False),
                kwargs.get('is_business', False),
                kwargs.get('bio'),
                kwargs.get('profile_pic_url'),
                kwargs.get('notes')
            ))
            
            account_id = cursor.lastrowid
            
            # تسجيل النشاط
            self.log_activity(user_id, 'account_added', 'account', account_id,
                            f'تم إضافة حساب Instagram: @{username}')
            
            conn.commit()
            return account_id
    
    def get_user_accounts(self, user_id: int) -> List[Dict]:
        """الحصول على حسابات المستخدم"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM instagram_accounts 
                WHERE user_id = ? 
                ORDER BY created_at DESC
            """, (user_id,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_all_accounts(self) -> List[Dict]:
        """الحصول على جميع الحسابات"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT ia.*, u.username as owner_username 
                FROM instagram_accounts ia
                LEFT JOIN users u ON ia.user_id = u.id
                ORDER BY ia.created_at DESC
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def update_account_status(self, account_id: int, status: str, 
                            follower_count: int = None, following_count: int = None,
                            post_count: int = None):
        """تحديث حالة الحساب"""
        with self.get_connection() as conn:
            update_fields = ["status = ?", "last_used = CURRENT_TIMESTAMP", "updated_at = CURRENT_TIMESTAMP"]
            params = [status]
            
            if follower_count is not None:
                update_fields.append("follower_count = ?")
                params.append(follower_count)
            
            if following_count is not None:
                update_fields.append("following_count = ?")
                params.append(following_count)
            
            if post_count is not None:
                update_fields.append("post_count = ?")
                params.append(post_count)
            
            params.append(account_id)
            
            conn.execute(f"""
                UPDATE instagram_accounts 
                SET {', '.join(update_fields)}
                WHERE id = ?
            """, params)
            
            conn.commit()
    
    # ==================== إدارة عمليات الاستخراج ====================
    
    def create_extraction(self, user_id: int, account_id: int, post_url: str,
                         extraction_type: str = 'likes', **kwargs) -> int:
        """إنشاء عملية استخراج جديدة"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO extractions 
                (user_id, account_id, post_url, post_id, extraction_type, 
                 extraction_settings, metadata, start_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                user_id, account_id, post_url,
                kwargs.get('post_id'),
                extraction_type,
                json.dumps(kwargs.get('settings', {})),
                json.dumps(kwargs.get('metadata', {}))
            ))
            
            extraction_id = cursor.lastrowid
            
            # تسجيل النشاط
            self.log_activity(user_id, 'extraction_started', 'extraction', extraction_id,
                            f'بدء عملية استخراج: {post_url}')
            
            conn.commit()
            return extraction_id
    
    def update_extraction_progress(self, extraction_id: int, status: str = None,
                                 extracted_count: int = None, total_count: int = None,
                                 progress_percentage: float = None, error_message: str = None):
        """تحديث تقدم عملية الاستخراج"""
        with self.get_connection() as conn:
            update_fields = ["updated_at = CURRENT_TIMESTAMP"]
            params = []
            
            if status:
                update_fields.append("status = ?")
                params.append(status)
                
                if status == 'completed':
                    update_fields.append("end_time = CURRENT_TIMESTAMP")
                elif status == 'failed' and error_message:
                    update_fields.append("error_message = ?")
                    params.append(error_message)
            
            if extracted_count is not None:
                update_fields.append("extracted_count = ?")
                params.append(extracted_count)
            
            if total_count is not None:
                update_fields.append("total_count = ?")
                params.append(total_count)
            
            if progress_percentage is not None:
                update_fields.append("progress_percentage = ?")
                params.append(progress_percentage)
            
            params.append(extraction_id)
            
            conn.execute(f"""
                UPDATE extractions 
                SET {', '.join(update_fields)}
                WHERE id = ?
            """, params)
            
            conn.commit()
    
    def get_user_extractions(self, user_id: int, limit: int = 50) -> List[Dict]:
        """الحصول على عمليات استخراج المستخدم"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT e.*, ia.username as account_username, ia.name as account_name
                FROM extractions e
                LEFT JOIN instagram_accounts ia ON e.account_id = ia.id
                WHERE e.user_id = ?
                ORDER BY e.created_at DESC
                LIMIT ?
            """, (user_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_all_extractions(self, limit: int = 100) -> List[Dict]:
        """الحصول على جميع عمليات الاستخراج"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT e.*, ia.username as account_username, u.username as user_username
                FROM extractions e
                LEFT JOIN instagram_accounts ia ON e.account_id = ia.id
                LEFT JOIN users u ON e.user_id = u.id
                ORDER BY e.created_at DESC
                LIMIT ?
            """, (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    # ==================== إدارة المستخدمين المستخرجين ====================
    
    def add_extracted_user(self, extraction_id: int, user_data: Dict) -> int:
        """إضافة مستخدم مستخرج"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO extracted_users 
                (extraction_id, username, full_name, profile_pic_url, profile_url,
                 is_verified, is_private, follower_count, following_count, post_count,
                 bio, threads_url, facebook_url, external_url, category, location,
                 contact_info)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                extraction_id,
                user_data.get('username'),
                user_data.get('full_name'),
                user_data.get('profile_pic_url'),
                user_data.get('profile_url'),
                user_data.get('is_verified', False),
                user_data.get('is_private', False),
                user_data.get('follower_count'),
                user_data.get('following_count'),
                user_data.get('post_count'),
                user_data.get('bio'),
                user_data.get('threads_url'),
                user_data.get('facebook_url'),
                user_data.get('external_url'),
                user_data.get('category'),
                user_data.get('location'),
                json.dumps(user_data.get('contact_info', {}))
            ))
            
            conn.commit()
            return cursor.lastrowid
    
    def get_extraction_users(self, extraction_id: int) -> List[Dict]:
        """الحصول على المستخدمين المستخرجين من عملية معينة"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM extracted_users 
                WHERE extraction_id = ?
                ORDER BY extracted_at DESC
            """, (extraction_id,))
            
            users = []
            for row in cursor.fetchall():
                user = dict(row)
                if user['contact_info']:
                    user['contact_info'] = json.loads(user['contact_info'])
                users.append(user)
            
            return users
    
    # ==================== إدارة الإشعارات ====================
    
    def create_notification(self, user_id: int, title: str, message: str,
                          notification_type: str = 'info', category: str = None,
                          action_url: str = None, is_global: bool = False) -> int:
        """إنشاء إشعار جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO notifications 
                (user_id, title, message, type, category, action_url, is_global)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user_id, title, message, notification_type, category, action_url, is_global))
            
            conn.commit()
            return cursor.lastrowid
    
    def get_user_notifications(self, user_id: int, unread_only: bool = False) -> List[Dict]:
        """الحصول على إشعارات المستخدم"""
        with self.get_connection() as conn:
            query = """
                SELECT * FROM notifications 
                WHERE (user_id = ? OR is_global = 1)
            """
            params = [user_id]
            
            if unread_only:
                query += " AND is_read = 0"
            
            query += " ORDER BY created_at DESC LIMIT 50"
            
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def mark_notification_read(self, notification_id: int, user_id: int = None):
        """تحديد إشعار كمقروء"""
        with self.get_connection() as conn:
            query = "UPDATE notifications SET is_read = 1 WHERE id = ?"
            params = [notification_id]
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            conn.execute(query, params)
            conn.commit()
    
    # ==================== إدارة الإحصائيات ====================
    
    def get_dashboard_stats(self, user_id: int = None) -> Dict:
        """الحصول على إحصائيات لوحة التحكم"""
        with self.get_connection() as conn:
            stats = {}
            
            # إحصائيات الحسابات
            if user_id:
                cursor = conn.execute("""
                    SELECT COUNT(*) as total, SUM(is_active) as active 
                    FROM instagram_accounts WHERE user_id = ?
                """, (user_id,))
            else:
                cursor = conn.execute("""
                    SELECT COUNT(*) as total, SUM(is_active) as active 
                    FROM instagram_accounts
                """)
            
            account_stats = cursor.fetchone()
            stats['total_accounts'] = account_stats['total']
            stats['active_accounts'] = account_stats['active']
            
            # إحصائيات الاستخراجات
            if user_id:
                cursor = conn.execute("""
                    SELECT COUNT(*) as total, SUM(extracted_count) as total_extracted
                    FROM extractions WHERE user_id = ?
                """, (user_id,))
            else:
                cursor = conn.execute("""
                    SELECT COUNT(*) as total, SUM(extracted_count) as total_extracted
                    FROM extractions
                """)
            
            extraction_stats = cursor.fetchone()
            stats['total_extractions'] = extraction_stats['total']
            stats['total_extracted_users'] = extraction_stats['total_extracted'] or 0
            
            # استخراجات اليوم
            today = datetime.now().date()
            if user_id:
                cursor = conn.execute("""
                    SELECT COUNT(*) as today_extractions
                    FROM extractions 
                    WHERE user_id = ? AND DATE(created_at) = ?
                """, (user_id, today))
            else:
                cursor = conn.execute("""
                    SELECT COUNT(*) as today_extractions
                    FROM extractions 
                    WHERE DATE(created_at) = ?
                """, (today,))
            
            stats['today_extractions'] = cursor.fetchone()['today_extractions']
            
            # معدل النجاح
            if user_id:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                    FROM extractions WHERE user_id = ?
                """, (user_id,))
            else:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                    FROM extractions
                """)
            
            success_stats = cursor.fetchone()
            if success_stats['total'] > 0:
                stats['success_rate'] = round((success_stats['completed'] / success_stats['total']) * 100, 1)
            else:
                stats['success_rate'] = 0
            
            return stats
    
    # ==================== إدارة النشاط ====================
    
    def log_activity(self, user_id: int, action: str, entity_type: str = None,
                    entity_id: int = None, description: str = None,
                    ip_address: str = None, user_agent: str = None, metadata: Dict = None):
        """تسجيل نشاط المستخدم"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO activity_logs 
                (user_id, action, entity_type, entity_id, description, 
                 ip_address, user_agent, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, action, entity_type, entity_id, description,
                ip_address, user_agent, json.dumps(metadata) if metadata else None
            ))
            
            conn.commit()
    
    def get_recent_activity(self, user_id: int = None, limit: int = 20) -> List[Dict]:
        """الحصول على النشاط الأخير"""
        with self.get_connection() as conn:
            if user_id:
                cursor = conn.execute("""
                    SELECT al.*, u.username 
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    WHERE al.user_id = ?
                    ORDER BY al.created_at DESC
                    LIMIT ?
                """, (user_id, limit))
            else:
                cursor = conn.execute("""
                    SELECT al.*, u.username 
                    FROM activity_logs al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.created_at DESC
                    LIMIT ?
                """, (limit,))
            
            activities = []
            for row in cursor.fetchall():
                activity = dict(row)
                if activity['metadata']:
                    activity['metadata'] = json.loads(activity['metadata'])
                activities.append(activity)
            
            return activities
    
    # ==================== دوال مساعدة ====================
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def cleanup_old_data(self, days: int = 30):
        """تنظيف البيانات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with self.get_connection() as conn:
            # حذف الجلسات المنتهية الصلاحية
            conn.execute("""
                DELETE FROM sessions 
                WHERE expires_at < ? OR last_activity < ?
            """, (datetime.now(), cutoff_date))
            
            # حذف الإشعارات القديمة المقروءة
            conn.execute("""
                DELETE FROM notifications 
                WHERE is_read = 1 AND created_at < ?
            """, (cutoff_date,))
            
            # حذف سجل النشاط القديم
            conn.execute("""
                DELETE FROM activity_logs 
                WHERE created_at < ?
            """, (cutoff_date,))
            
            conn.commit()
            print(f"✅ تم تنظيف البيانات الأقدم من {days} يوم")
    
    def get_database_info(self) -> Dict:
        """الحصول على معلومات قاعدة البيانات"""
        with self.get_connection() as conn:
            info = {}
            
            # حجم قاعدة البيانات
            if os.path.exists(self.db_path):
                info['file_size'] = os.path.getsize(self.db_path)
            
            # عدد الجداول
            cursor = conn.execute("""
                SELECT COUNT(*) as table_count 
                FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            info['table_count'] = cursor.fetchone()['table_count']
            
            # إحصائيات الجداول
            tables = ['users', 'instagram_accounts', 'extractions', 'extracted_users', 
                     'notifications', 'activity_logs', 'sessions']
            
            for table in tables:
                cursor = conn.execute(f"SELECT COUNT(*) as count FROM {table}")
                info[f'{table}_count'] = cursor.fetchone()['count']
            
            return info
