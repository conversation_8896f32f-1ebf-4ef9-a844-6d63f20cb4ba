-- قاعدة بيانات Instagram Extractor Professional
-- إن<PERSON><PERSON><PERSON> جداول قاعدة البيانات الشاملة

-- جدول المستخدمين (Users)
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user', -- admin, user, viewer
    avatar_url VARCHAR(255),
    is_active BOOLEAN DEFAULT 1,
    is_verified BOOLEAN DEFAULT 0,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- جدول حسابات Instagram (Instagram Accounts)
CREATE TABLE IF NOT EXISTS instagram_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    name VARCHAR(100) NOT NULL,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100),
    password_encrypted TEXT, -- مشفر
    cookies_data TEXT, -- بيانات الكوكيز
    session_data TEXT, -- بيانات الجلسة
    is_active BOOLEAN DEFAULT 1,
    is_verified BOOLEAN DEFAULT 0,
    is_business BOOLEAN DEFAULT 0,
    follower_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    post_count INTEGER DEFAULT 0,
    bio TEXT,
    profile_pic_url VARCHAR(255),
    last_used DATETIME,
    status VARCHAR(20) DEFAULT 'active', -- active, suspended, banned, error
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول عمليات الاستخراج (Extractions)
CREATE TABLE IF NOT EXISTS extractions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    account_id INTEGER,
    post_url VARCHAR(500) NOT NULL,
    post_id VARCHAR(100),
    extraction_type VARCHAR(20) DEFAULT 'likes', -- likes, comments, followers
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    total_count INTEGER DEFAULT 0,
    extracted_count INTEGER DEFAULT 0,
    progress_percentage REAL DEFAULT 0.0,
    start_time DATETIME,
    end_time DATETIME,
    duration_seconds INTEGER,
    error_message TEXT,
    extraction_settings JSON, -- إعدادات الاستخراج
    metadata JSON, -- معلومات إضافية
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES instagram_accounts(id) ON DELETE SET NULL
);

-- جدول المستخدمين المستخرجين (Extracted Users)
CREATE TABLE IF NOT EXISTS extracted_users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    extraction_id INTEGER,
    username VARCHAR(50) NOT NULL,
    full_name VARCHAR(100),
    profile_pic_url VARCHAR(255),
    profile_url VARCHAR(255),
    is_verified BOOLEAN DEFAULT 0,
    is_private BOOLEAN DEFAULT 0,
    follower_count INTEGER,
    following_count INTEGER,
    post_count INTEGER,
    bio TEXT,
    threads_url VARCHAR(255),
    facebook_url VARCHAR(255),
    external_url VARCHAR(255),
    category VARCHAR(50), -- personal, business, creator
    location VARCHAR(100),
    contact_info JSON, -- معلومات الاتصال
    extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (extraction_id) REFERENCES extractions(id) ON DELETE CASCADE
);

-- جدول سجل العمليات (Activity Logs)
CREATE TABLE IF NOT EXISTS activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(50) NOT NULL, -- login, logout, extract, add_account, etc.
    entity_type VARCHAR(20), -- user, account, extraction
    entity_id INTEGER,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول الإعدادات (Settings)
CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    category VARCHAR(50) NOT NULL, -- general, extraction, notifications, security
    key VARCHAR(100) NOT NULL,
    value TEXT,
    data_type VARCHAR(20) DEFAULT 'string', -- string, integer, boolean, json
    is_global BOOLEAN DEFAULT 0, -- إعداد عام أم خاص بالمستخدم
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, category, key)
);

-- جدول الإشعارات (Notifications)
CREATE TABLE IF NOT EXISTS notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info', -- success, error, warning, info
    category VARCHAR(50), -- extraction, account, system, security
    is_read BOOLEAN DEFAULT 0,
    is_global BOOLEAN DEFAULT 0, -- إشعار عام أم خاص
    action_url VARCHAR(255), -- رابط للإجراء
    metadata JSON,
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الجلسات (Sessions)
CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT 1,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الملفات المصدرة (Exported Files)
CREATE TABLE IF NOT EXISTS exported_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    extraction_id INTEGER,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_type VARCHAR(20), -- csv, excel, json, pdf
    file_size INTEGER, -- بالبايت
    download_count INTEGER DEFAULT 0,
    is_available BOOLEAN DEFAULT 1,
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (extraction_id) REFERENCES extractions(id) ON DELETE CASCADE
);

-- جدول الإحصائيات (Statistics)
CREATE TABLE IF NOT EXISTS statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    date DATE NOT NULL,
    metric_name VARCHAR(50) NOT NULL, -- extractions_count, users_extracted, etc.
    metric_value INTEGER DEFAULT 0,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, date, metric_name)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_username ON instagram_accounts(username);
CREATE INDEX IF NOT EXISTS idx_instagram_accounts_user_id ON instagram_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_extractions_user_id ON extractions(user_id);
CREATE INDEX IF NOT EXISTS idx_extractions_account_id ON extractions(account_id);
CREATE INDEX IF NOT EXISTS idx_extractions_status ON extractions(status);
CREATE INDEX IF NOT EXISTS idx_extracted_users_extraction_id ON extracted_users(extraction_id);
CREATE INDEX IF NOT EXISTS idx_extracted_users_username ON extracted_users(username);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_statistics_user_date ON statistics(user_id, date);

-- إدراج بيانات افتراضية
INSERT OR IGNORE INTO users (id, username, email, password_hash, full_name, role, is_active) VALUES
(1, 'admin', '<EMAIL>', 'hashed_password_here', 'مدير النظام', 'admin', 1),
(2, 'developer', '<EMAIL>', 'hashed_password_here', 'المطور العربي', 'admin', 1);

INSERT OR IGNORE INTO settings (user_id, category, key, value, data_type, is_global, description) VALUES
(NULL, 'general', 'app_name', 'Instagram Extractor Professional', 'string', 1, 'اسم التطبيق'),
(NULL, 'general', 'app_version', '4.0', 'string', 1, 'إصدار التطبيق'),
(NULL, 'general', 'max_extractions_per_day', '50', 'integer', 1, 'الحد الأقصى للاستخراجات يومياً'),
(NULL, 'extraction', 'default_delay', '2000', 'integer', 1, 'التأخير الافتراضي بالميلي ثانية'),
(NULL, 'extraction', 'max_users_per_extraction', '1000', 'integer', 1, 'الحد الأقصى للمستخدمين في الاستخراج الواحد'),
(NULL, 'notifications', 'enable_email', 'false', 'boolean', 1, 'تفعيل الإشعارات بالإيميل'),
(NULL, 'security', 'session_timeout', '86400', 'integer', 1, 'انتهاء الجلسة بالثواني (24 ساعة)');
