#!/usr/bin/env python3
"""
خادم قاعدة البيانات - Instagram Extractor Professional
خادم محدث يستخدم قاعدة البيانات الشاملة
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import time
from datetime import datetime
import threading
import uuid
import os
import sys

# إضافة مجلد database إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
from database_manager import DatabaseManager

app = Flask(__name__)
CORS(app)

# تهيئة مدير قاعدة البيانات
db_manager = DatabaseManager()

# متغيرات عامة
current_user_id = 1  # افتراضي للتطوير
extraction_jobs = {}

@app.route('/api/test', methods=['GET'])
def test_connection():
    """اختبار الاتصال"""
    return jsonify({
        'status': 'success',
        'message': 'الخادم يعمل بنجاح مع قاعدة البيانات',
        'timestamp': datetime.now().isoformat(),
        'database_info': db_manager.get_database_info()
    })

@app.route('/api/accounts', methods=['GET'])
def get_accounts():
    """الحصول على جميع الحسابات"""
    try:
        accounts = db_manager.get_all_accounts()
        return jsonify({
            'status': 'success',
            'accounts': accounts,
            'count': len(accounts)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب الحسابات: {str(e)}'
        }), 500

@app.route('/api/accounts', methods=['POST'])
def add_account():
    """إضافة حساب Instagram جديد"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        required_fields = ['name', 'username']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'الحقل {field} مطلوب'
                }), 400
        
        # إضافة الحساب
        account_id = db_manager.add_instagram_account(
            user_id=current_user_id,
            name=data['name'],
            username=data['username'],
            email=data.get('email'),
            password_encrypted=data.get('password'),
            is_verified=data.get('is_verified', False),
            is_business=data.get('is_business', False),
            bio=data.get('bio'),
            notes=data.get('notes')
        )
        
        # إنشاء إشعار
        db_manager.create_notification(
            user_id=current_user_id,
            title='تم إضافة حساب جديد',
            message=f'تم إضافة حساب @{data["username"]} بنجاح',
            notification_type='success',
            category='account'
        )
        
        return jsonify({
            'status': 'success',
            'message': 'تم إضافة الحساب بنجاح',
            'account_id': account_id
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في إضافة الحساب: {str(e)}'
        }), 500

@app.route('/api/accounts/<int:account_id>', methods=['PUT'])
def update_account(account_id):
    """تحديث حساب Instagram"""
    try:
        data = request.get_json()
        
        db_manager.update_account_status(
            account_id=account_id,
            status=data.get('status', 'active'),
            follower_count=data.get('follower_count'),
            following_count=data.get('following_count'),
            post_count=data.get('post_count')
        )
        
        return jsonify({
            'status': 'success',
            'message': 'تم تحديث الحساب بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في تحديث الحساب: {str(e)}'
        }), 500

@app.route('/api/extractions', methods=['GET'])
def get_extractions():
    """الحصول على عمليات الاستخراج"""
    try:
        extractions = db_manager.get_all_extractions(limit=100)
        return jsonify({
            'status': 'success',
            'extractions': extractions,
            'count': len(extractions)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب الاستخراجات: {str(e)}'
        }), 500

@app.route('/api/extractions', methods=['POST'])
def start_extraction():
    """بدء عملية استخراج جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        if 'account_id' not in data or 'post_url' not in data:
            return jsonify({
                'status': 'error',
                'message': 'معرف الحساب ورابط المنشور مطلوبان'
            }), 400
        
        # إنشاء عملية الاستخراج
        extraction_id = db_manager.create_extraction(
            user_id=current_user_id,
            account_id=data['account_id'],
            post_url=data['post_url'],
            extraction_type=data.get('extraction_type', 'likes'),
            post_id=data.get('post_id'),
            settings=data.get('settings', {}),
            metadata=data.get('metadata', {})
        )
        
        # بدء عملية الاستخراج في خيط منفصل
        job_id = str(uuid.uuid4())
        extraction_jobs[job_id] = {
            'id': extraction_id,
            'status': 'running',
            'progress': 0,
            'start_time': datetime.now()
        }
        
        # محاكاة عملية الاستخراج
        thread = threading.Thread(
            target=simulate_extraction,
            args=(extraction_id, job_id, data['post_url'])
        )
        thread.start()
        
        # إنشاء إشعار
        db_manager.create_notification(
            user_id=current_user_id,
            title='بدء عملية استخراج',
            message=f'تم بدء استخراج المعجبين من المنشور',
            notification_type='info',
            category='extraction'
        )
        
        return jsonify({
            'status': 'success',
            'message': 'تم بدء عملية الاستخراج',
            'extraction_id': extraction_id,
            'job_id': job_id
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في بدء الاستخراج: {str(e)}'
        }), 500

@app.route('/api/extractions/<int:extraction_id>/users', methods=['GET'])
def get_extraction_users(extraction_id):
    """الحصول على المستخدمين المستخرجين"""
    try:
        users = db_manager.get_extraction_users(extraction_id)
        return jsonify({
            'status': 'success',
            'users': users,
            'count': len(users)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب المستخدمين: {str(e)}'
        }), 500

@app.route('/api/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """الحصول على إحصائيات لوحة التحكم"""
    try:
        stats = db_manager.get_dashboard_stats(current_user_id)
        return jsonify({
            'status': 'success',
            'stats': stats
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب الإحصائيات: {str(e)}'
        }), 500

@app.route('/api/notifications', methods=['GET'])
def get_notifications():
    """الحصول على الإشعارات"""
    try:
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        notifications = db_manager.get_user_notifications(current_user_id, unread_only)
        
        return jsonify({
            'status': 'success',
            'notifications': notifications,
            'count': len(notifications)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب الإشعارات: {str(e)}'
        }), 500

@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
def mark_notification_read(notification_id):
    """تحديد إشعار كمقروء"""
    try:
        db_manager.mark_notification_read(notification_id, current_user_id)
        return jsonify({
            'status': 'success',
            'message': 'تم تحديد الإشعار كمقروء'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في تحديث الإشعار: {str(e)}'
        }), 500

@app.route('/api/activity', methods=['GET'])
def get_activity():
    """الحصول على النشاط الأخير"""
    try:
        limit = int(request.args.get('limit', 20))
        activities = db_manager.get_recent_activity(current_user_id, limit)
        
        return jsonify({
            'status': 'success',
            'activities': activities,
            'count': len(activities)
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في جلب النشاط: {str(e)}'
        }), 500

def simulate_extraction(extraction_id, job_id, post_url):
    """محاكاة عملية الاستخراج"""
    try:
        # تحديث حالة البداية
        db_manager.update_extraction_progress(
            extraction_id, 
            status='running', 
            progress_percentage=0
        )
        
        # محاكاة مراحل الاستخراج
        stages = [
            {'progress': 10, 'message': 'فتح المتصفح...'},
            {'progress': 20, 'message': 'الانتقال إلى المنشور...'},
            {'progress': 30, 'message': 'تسجيل الدخول...'},
            {'progress': 40, 'message': 'فتح قائمة المعجبين...'},
            {'progress': 60, 'message': 'استخراج البيانات...'},
            {'progress': 80, 'message': 'معالجة المعلومات...'},
            {'progress': 100, 'message': 'اكتمال الاستخراج...'}
        ]
        
        total_users = 150  # عدد افتراضي
        db_manager.update_extraction_progress(
            extraction_id,
            total_count=total_users
        )
        
        for i, stage in enumerate(stages):
            time.sleep(2)  # محاكاة الوقت
            
            # تحديث التقدم
            db_manager.update_extraction_progress(
                extraction_id,
                progress_percentage=stage['progress'],
                extracted_count=int((stage['progress'] / 100) * total_users)
            )
            
            # تحديث حالة المهمة
            if job_id in extraction_jobs:
                extraction_jobs[job_id]['progress'] = stage['progress']
                extraction_jobs[job_id]['message'] = stage['message']
        
        # إضافة مستخدمين تجريبيين
        sample_users = [
            {
                'username': f'user_{i}',
                'full_name': f'مستخدم تجريبي {i}',
                'is_verified': i % 5 == 0,
                'follower_count': 1000 + (i * 100),
                'following_count': 500 + (i * 50),
                'post_count': 20 + (i * 2),
                'bio': f'حساب تجريبي رقم {i}'
            }
            for i in range(1, total_users + 1)
        ]
        
        for user_data in sample_users:
            db_manager.add_extracted_user(extraction_id, user_data)
        
        # تحديث حالة الاكتمال
        db_manager.update_extraction_progress(
            extraction_id,
            status='completed',
            extracted_count=total_users,
            progress_percentage=100
        )
        
        # إنشاء إشعار الاكتمال
        db_manager.create_notification(
            user_id=current_user_id,
            title='اكتمال عملية الاستخراج',
            message=f'تم استخراج {total_users} مستخدم بنجاح',
            notification_type='success',
            category='extraction'
        )
        
        # تحديث حالة المهمة
        if job_id in extraction_jobs:
            extraction_jobs[job_id]['status'] = 'completed'
            extraction_jobs[job_id]['progress'] = 100
        
    except Exception as e:
        # تحديث حالة الخطأ
        db_manager.update_extraction_progress(
            extraction_id,
            status='failed',
            error_message=str(e)
        )
        
        # إنشاء إشعار الخطأ
        db_manager.create_notification(
            user_id=current_user_id,
            title='فشل في عملية الاستخراج',
            message=f'حدث خطأ: {str(e)}',
            notification_type='error',
            category='extraction'
        )
        
        if job_id in extraction_jobs:
            extraction_jobs[job_id]['status'] = 'failed'
            extraction_jobs[job_id]['error'] = str(e)

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم قاعدة البيانات...")
    print("📊 تهيئة قاعدة البيانات...")
    
    # تنظيف البيانات القديمة
    db_manager.cleanup_old_data(30)
    
    print("✅ الخادم جاهز على http://localhost:8000")
    app.run(host='0.0.0.0', port=8000, debug=True)
