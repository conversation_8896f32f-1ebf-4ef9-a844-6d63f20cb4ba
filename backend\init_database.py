#!/usr/bin/env python3
"""
تهيئة قاعدة البيانات - Instagram Extractor Professional
سكريبت لتهيئة قاعدة البيانات وإدراج البيانات الأولية
"""

import os
import sys
from datetime import datetime, timedelta
import random

# إضافة مجلد database إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'database'))
from database_manager import DatabaseManager

def init_sample_data():
    """إدراج بيانات تجريبية للاختبار"""
    print("📊 إدراج البيانات التجريبية...")

    db = DatabaseManager()

    # إنشاء مستخدمين تجريبيين
    try:
        admin_id = db.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            full_name='مدير النظام',
            role='admin'
        )
        print(f"✅ تم إنشاء المدير: ID {admin_id}")
    except:
        print("ℹ️ المدير موجود مسبقاً")
        admin_id = 1

    try:
        user_id = db.create_user(
            username='developer',
            email='<EMAIL>',
            password='dev123',
            full_name='المطور العربي',
            role='user'
        )
        print(f"✅ تم إنشاء المطور: ID {user_id}")
    except:
        print("ℹ️ المطور موجود مسبقاً")
        user_id = 2

    # إضافة حسابات Instagram تجريبية
    sample_accounts = [
        {
            'name': 'حساب العمل الرئيسي',
            'username': 'business_account',
            'email': '<EMAIL>',
            'is_verified': False,
            'is_business': True,
            'follower_count': 15000,
            'following_count': 500,
            'post_count': 120,
            'bio': 'حساب العمل الرئيسي للشركة 🏢',
            'notes': 'حساب مهم للعمليات التجارية'
        },
        {
            'name': 'حساب شخصي موثق',
            'username': 'personal_verified',
            'email': '<EMAIL>',
            'is_verified': True,
            'is_business': False,
            'follower_count': 85000,
            'following_count': 300,
            'post_count': 450,
            'bio': 'حساب شخصي موثق ⭐ | مؤثر رقمي',
            'notes': 'حساب موثق بمتابعين كثيرين'
        },
        {
            'name': 'حساب المحتوى الإبداعي',
            'username': 'creative_content',
            'email': '<EMAIL>',
            'is_verified': False,
            'is_business': True,
            'follower_count': 32000,
            'following_count': 800,
            'post_count': 280,
            'bio': 'صانع محتوى إبداعي 🎨 | مصور فوتوغرافي',
            'notes': 'متخصص في المحتوى الإبداعي'
        }
    ]

    account_ids = []
    for account_data in sample_accounts:
        try:
            account_id = db.add_instagram_account(
                user_id=admin_id,
                **account_data
            )
            account_ids.append(account_id)
            print(f"✅ تم إضافة حساب: @{account_data['username']} (ID: {account_id})")
        except Exception as e:
            print(f"⚠️ خطأ في إضافة حساب @{account_data['username']}: {e}")

    # إضافة عمليات استخراج تجريبية
    sample_extractions = [
        {
            'post_url': 'https://www.instagram.com/p/ABC123/',
            'post_id': 'ABC123',
            'extraction_type': 'likes',
            'total_count': 245,
            'extracted_count': 245,
            'status': 'completed'
        },
        {
            'post_url': 'https://www.instagram.com/p/DEF456/',
            'post_id': 'DEF456',
            'extraction_type': 'likes',
            'total_count': 189,
            'extracted_count': 189,
            'status': 'completed'
        },
        {
            'post_url': 'https://www.instagram.com/p/GHI789/',
            'post_id': 'GHI789',
            'extraction_type': 'likes',
            'total_count': 312,
            'extracted_count': 298,
            'status': 'completed'
        },
        {
            'post_url': 'https://www.instagram.com/p/JKL012/',
            'post_id': 'JKL012',
            'extraction_type': 'likes',
            'total_count': 156,
            'extracted_count': 156,
            'status': 'completed'
        }
    ]

    extraction_ids = []
    for i, extraction_data in enumerate(sample_extractions):
        try:
            account_id = account_ids[i % len(account_ids)] if account_ids else 1

            extraction_id = db.create_extraction(
                user_id=admin_id,
                account_id=account_id,
                post_url=extraction_data['post_url'],
                extraction_type=extraction_data['extraction_type'],
                post_id=extraction_data['post_id']
            )

            # تحديث حالة الاستخراج
            db.update_extraction_progress(
                extraction_id,
                status=extraction_data['status'],
                total_count=extraction_data['total_count'],
                extracted_count=extraction_data['extracted_count'],
                progress_percentage=100.0
            )

            extraction_ids.append(extraction_id)
            print(f"✅ تم إضافة استخراج: {extraction_data['post_id']} (ID: {extraction_id})")

            # إضافة مستخدمين مستخرجين تجريبيين
            for j in range(extraction_data['extracted_count']):
                user_data = {
                    'username': f'extracted_user_{extraction_id}_{j+1}',
                    'full_name': f'مستخدم مستخرج {j+1}',
                    'is_verified': j % 10 == 0,  # كل 10 مستخدمين واحد موثق
                    'is_private': j % 5 == 0,    # كل 5 مستخدمين واحد خاص
                    'follower_count': random.randint(100, 50000),
                    'following_count': random.randint(50, 2000),
                    'post_count': random.randint(5, 500),
                    'bio': f'حساب تجريبي مستخرج رقم {j+1}',
                    'category': random.choice(['personal', 'business', 'creator'])
                }

                db.add_extracted_user(extraction_id, user_data)

            print(f"  📊 تم إضافة {extraction_data['extracted_count']} مستخدم مستخرج")

        except Exception as e:
            print(f"⚠️ خطأ في إضافة استخراج {extraction_data['post_id']}: {e}")

    # إضافة إشعارات تجريبية
    sample_notifications = [
        {
            'title': 'مرحباً بك في النظام',
            'message': 'تم تهيئة النظام بنجاح وإضافة البيانات التجريبية',
            'type': 'success',
            'category': 'system'
        },
        {
            'title': 'تم إضافة حسابات جديدة',
            'message': 'تم إضافة 3 حسابات Instagram للاختبار',
            'type': 'info',
            'category': 'account'
        },
        {
            'title': 'اكتمال عمليات الاستخراج',
            'message': 'تم إكمال جميع عمليات الاستخراج التجريبية بنجاح',
            'type': 'success',
            'category': 'extraction'
        },
        {
            'title': 'نصيحة للاستخدام',
            'message': 'استخدم النظام الذكي لكشف الحسابات المفتوحة في المتصفح',
            'type': 'info',
            'category': 'system'
        }
    ]

    for notification_data in sample_notifications:
        try:
            notification_id = db.create_notification(
                user_id=admin_id,
                title=notification_data['title'],
                message=notification_data['message'],
                notification_type=notification_data['type'],
                category=notification_data['category']
            )
            print(f"✅ تم إضافة إشعار: {notification_data['title']} (ID: {notification_id})")
        except Exception as e:
            print(f"⚠️ خطأ في إضافة إشعار: {e}")

    # إضافة إحصائيات تجريبية
    today = datetime.now().date()
    for i in range(7):  # آخر 7 أيام
        date = today - timedelta(days=i)

        # إحصائيات يومية
        daily_stats = [
            ('extractions_count', random.randint(5, 15)),
            ('users_extracted', random.randint(500, 2000)),
            ('accounts_added', random.randint(0, 3)),
            ('success_rate', random.randint(85, 98))
        ]

        for metric_name, metric_value in daily_stats:
            try:
                with db.get_connection() as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO statistics
                        (user_id, date, metric_name, metric_value)
                        VALUES (?, ?, ?, ?)
                    """, (admin_id, date, metric_name, metric_value))
                    conn.commit()
            except Exception as e:
                print(f"⚠️ خطأ في إضافة إحصائية {metric_name}: {e}")

    print("✅ تم إدراج جميع البيانات التجريبية بنجاح")

def show_database_info():
    """عرض معلومات قاعدة البيانات"""
    print("\n📊 معلومات قاعدة البيانات:")
    print("=" * 50)

    db = DatabaseManager()
    info = db.get_database_info()

    print(f"📁 حجم الملف: {info.get('file_size', 0):,} بايت")
    print(f"🗂️ عدد الجداول: {info.get('table_count', 0)}")
    print()

    tables_info = [
        ('المستخدمين', 'users_count'),
        ('حسابات Instagram', 'instagram_accounts_count'),
        ('عمليات الاستخراج', 'extractions_count'),
        ('المستخدمين المستخرجين', 'extracted_users_count'),
        ('الإشعارات', 'notifications_count'),
        ('سجل النشاط', 'activity_logs_count'),
        ('الجلسات', 'sessions_count')
    ]

    for table_name, key in tables_info:
        count = info.get(key, 0)
        print(f"📋 {table_name}: {count:,} سجل")

    # عرض الإحصائيات
    stats = db.get_dashboard_stats()
    print(f"\n📊 إحصائيات سريعة:")
    print(f"👥 إجمالي الحسابات: {stats.get('total_accounts', 0)}")
    print(f"✅ الحسابات النشطة: {stats.get('active_accounts', 0)}")
    print(f"🔍 إجمالي الاستخراجات: {stats.get('total_extractions', 0)}")
    print(f"👤 إجمالي المستخدمين المستخرجين: {stats.get('total_extracted_users', 0):,}")
    print(f"📈 معدل النجاح: {stats.get('success_rate', 0)}%")

def main():
    """الدالة الرئيسية"""
    print("🚀 تهيئة قاعدة البيانات - Instagram Extractor Professional")
    print("=" * 60)

    try:
        # تهيئة قاعدة البيانات
        print("📊 إنشاء قاعدة البيانات...")
        db = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")

        # إدراج البيانات التجريبية
        init_sample_data()

        # عرض معلومات قاعدة البيانات
        show_database_info()

        print("\n🎉 تم إكمال تهيئة قاعدة البيانات بنجاح!")
        print("🌐 يمكنك الآن تشغيل الخادم باستخدام: python database_server.py")

    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return 1

    return 0

if __name__ == '__main__':
    exit(main())
