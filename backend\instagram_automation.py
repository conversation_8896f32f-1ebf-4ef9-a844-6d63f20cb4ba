#!/usr/bin/env python3
"""
نظام الأتمتة المتقدم لـ Instagram
- فتح Instagram تلقائياً
- استخراج الكوكيز
- حفظ الحساب تلقائياً
- تصفح آمن مع استخراج المعجبين
"""

import asyncio
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Optional
import sqlite3
import hashlib
import random

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.action_chains import ActionChains
    import undetected_chromedriver as uc
    SELENIUM_AVAILABLE = True
except ImportError:
    print("⚠️ يرجى تثبيت selenium و undetected_chromedriver")
    print("pip install selenium undetected_chromedriver")
    SELENIUM_AVAILABLE = False

class InstagramAutomation:
    def __init__(self):
        self.driver = None
        self.cookies_dir = "../cookies"
        self.db_path = "../database/instagram_accounts.db"
        self.current_account = None
        self.extraction_overlay_injected = False

        # إنشاء مجلد الكوكيز
        os.makedirs(self.cookies_dir, exist_ok=True)

        # إعدادات الأمان
        self.safety_settings = {
            "min_delay": 2,  # أقل تأخير بين العمليات (ثانية)
            "max_delay": 5,  # أكبر تأخير بين العمليات (ثانية)
            "scroll_delay": 1.5,  # تأخير التمرير
            "click_delay": 0.8,  # تأخير النقر
            "max_likes_per_session": 50,  # أقصى عدد معجبين في الجلسة
            "session_break": 300,  # استراحة بين الجلسات (5 دقائق)
        }

    def setup_driver(self, headless=False):
        """إعداد متصفح Chrome مع الحماية من الاكتشاف"""
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium غير متوفر")
            return False

        try:
            options = uc.ChromeOptions()

            # إعدادات الأمان والخصوصية
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # إعدادات إضافية للحماية
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-plugins")
            options.add_argument("--disable-images")  # تسريع التحميل
            options.add_argument("--disable-javascript")  # تقليل البصمة

            if headless:
                options.add_argument("--headless")

            # إنشاء المتصفح
            self.driver = uc.Chrome(options=options)

            # إخفاء خصائص الأتمتة
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            print("✅ تم إعداد المتصفح بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد المتصفح: {e}")
            return False

    async def open_instagram_login(self):
        """فتح صفحة تسجيل الدخول في Instagram"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False

            print("🔐 فتح صفحة تسجيل الدخول...")
            self.driver.get("https://www.instagram.com/accounts/login/")

            # انتظار تحميل الصفحة
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )

            print("✅ تم فتح صفحة تسجيل الدخول")
            return True

        except Exception as e:
            print(f"❌ خطأ في فتح Instagram: {e}")
            return False

    async def wait_for_login_completion(self, timeout=300):
        """انتظار اكتمال تسجيل الدخول"""
        print("⏳ انتظار اكتمال تسجيل الدخول...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                current_url = self.driver.current_url

                # التحقق من نجاح تسجيل الدخول
                if "instagram.com" in current_url and "/accounts/login" not in current_url:
                    print("✅ تم تسجيل الدخول بنجاح!")
                    return True

                await asyncio.sleep(2)

            except Exception as e:
                print(f"⚠️ خطأ أثناء انتظار تسجيل الدخول: {e}")
                await asyncio.sleep(2)

        print("⏰ انتهت مهلة انتظار تسجيل الدخول")
        return False

    async def extract_cookies(self):
        """استخراج الكوكيز من المتصفح"""
        try:
            cookies = self.driver.get_cookies()

            # إنشاء معرف فريد للحساب
            account_id = hashlib.md5(str(time.time()).encode()).hexdigest()[:8]

            # حفظ الكوكيز
            cookies_file = os.path.join(self.cookies_dir, f"account_{account_id}.json")
            with open(cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)

            print(f"✅ تم حفظ الكوكيز: {cookies_file}")
            return account_id, cookies_file

        except Exception as e:
            print(f"❌ خطأ في استخراج الكوكيز: {e}")
            return None, None

    async def get_account_info(self):
        """استخراج معلومات الحساب"""
        try:
            # الانتقال للملف الشخصي
            profile_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/']")
            profile_link = None

            for link in profile_links:
                href = link.get_attribute('href')
                if href and href.count('/') == 4 and not any(x in href for x in ['explore', 'accounts', 'direct']):
                    profile_link = href
                    break

            if profile_link:
                self.driver.get(profile_link)
                await asyncio.sleep(3)

                # استخراج اسم المستخدم
                username = profile_link.split('/')[-2] if profile_link.endswith('/') else profile_link.split('/')[-1]

                # استخراج الاسم الكامل
                try:
                    name_element = self.driver.find_element(By.CSS_SELECTOR, "h2")
                    full_name = name_element.text
                except:
                    full_name = username

                # التحقق من التوثيق
                is_verified = len(self.driver.find_elements(By.CSS_SELECTOR, "[aria-label*='Verified']")) > 0

                return {
                    "username": username,
                    "name": full_name,
                    "is_verified": is_verified,
                    "profile_url": profile_link
                }

        except Exception as e:
            print(f"❌ خطأ في استخراج معلومات الحساب: {e}")

        return None

    async def save_account_to_db(self, account_info, cookies_file):
        """حفظ الحساب في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء الجدول إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS instagram_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT,
                    phone TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    is_verified BOOLEAN DEFAULT 0,
                    cookies_file TEXT,
                    last_used DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT
                )
            ''')

            # إدراج الحساب
            cursor.execute('''
                INSERT OR REPLACE INTO instagram_accounts
                (name, username, is_verified, cookies_file, last_used, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                account_info['name'],
                account_info['username'],
                account_info['is_verified'],
                cookies_file,
                datetime.now().isoformat(),
                f"تم إضافة الحساب تلقائياً في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ))

            conn.commit()
            account_id = cursor.lastrowid
            conn.close()

            print(f"✅ تم حفظ الحساب في قاعدة البيانات: {account_info['username']}")
            return account_id

        except Exception as e:
            print(f"❌ خطأ في حفظ الحساب: {e}")
            return None

    async def inject_extraction_overlay(self):
        """حقن واجهة الاستخراج في صفحة Instagram"""
        if self.extraction_overlay_injected:
            return

        try:
            # JavaScript لحقن واجهة الاستخراج
            overlay_script = """
            // إنشاء واجهة الاستخراج
            if (!document.getElementById('instagram-extractor-overlay')) {
                // إنشاء الأنماط
                const style = document.createElement('style');
                style.textContent = `
                    .extractor-btn {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 20px;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: bold;
                        z-index: 9999;
                        opacity: 0;
                        transition: opacity 0.3s;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                    }
                    .extractor-btn:hover {
                        transform: scale(1.05);
                        box-shadow: 0 4px 15px rgba(0,0,0,0.4);
                    }
                    .post-container:hover .extractor-btn {
                        opacity: 1;
                    }
                `;
                document.head.appendChild(style);

                // إضافة أزرار الاستخراج للمنشورات
                function addExtractionButtons() {
                    const posts = document.querySelectorAll('article');
                    posts.forEach(post => {
                        if (!post.querySelector('.extractor-btn')) {
                            post.style.position = 'relative';
                            post.classList.add('post-container');

                            const btn = document.createElement('button');
                            btn.className = 'extractor-btn';
                            btn.textContent = '🔍 استخراج';
                            btn.onclick = function(e) {
                                e.preventDefault();
                                e.stopPropagation();

                                // العثور على رابط المنشور
                                const postLink = post.querySelector('a[href*="/p/"], a[href*="/reel/"]');
                                if (postLink) {
                                    const postUrl = postLink.href;
                                    window.postMessage({
                                        type: 'EXTRACT_LIKES',
                                        url: postUrl
                                    }, '*');
                                }
                            };

                            post.appendChild(btn);
                        }
                    });
                }

                // تشغيل الدالة عند التحميل وعند التغيير
                addExtractionButtons();

                // مراقبة التغييرات في الصفحة
                const observer = new MutationObserver(addExtractionButtons);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // إضافة معرف للتأكد من عدم التكرار
                const overlay = document.createElement('div');
                overlay.id = 'instagram-extractor-overlay';
                overlay.style.display = 'none';
                document.body.appendChild(overlay);
            }
            """

            self.driver.execute_script(overlay_script)
            self.extraction_overlay_injected = True
            print("✅ تم حقن واجهة الاستخراج")

        except Exception as e:
            print(f"❌ خطأ في حقن واجهة الاستخراج: {e}")

    async def listen_for_extraction_requests(self):
        """الاستماع لطلبات الاستخراج"""
        try:
            # JavaScript للاستماع للرسائل
            listener_script = """
            window.addEventListener('message', function(event) {
                if (event.data.type === 'EXTRACT_LIKES') {
                    // إرسال الطلب للخادم
                    fetch('/api/extract-likes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            url: event.data.url,
                            timestamp: Date.now()
                        })
                    }).then(response => response.json())
                      .then(data => {
                          console.log('تم إرسال طلب الاستخراج:', data);
                      });
                }
            });
            """

            self.driver.execute_script(listener_script)
            print("✅ تم تفعيل الاستماع لطلبات الاستخراج")

        except Exception as e:
            print(f"❌ خطأ في تفعيل الاستماع: {e}")

    async def safe_extract_likes(self, post_url):
        """استخراج المعجبين بطريقة آمنة"""
        try:
            print(f"🔍 بدء استخراج المعجبين من: {post_url}")

            # الانتقال للمنشور
            self.driver.get(post_url)
            await asyncio.sleep(random.uniform(2, 4))

            # البحث عن زر المعجبين
            likes_button = None
            try:
                # محاولة العثور على زر المعجبين
                likes_selectors = [
                    "a[href*='/liked_by/']",
                    "button[aria-label*='like']",
                    "span:contains('likes')"
                ]

                for selector in likes_selectors:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        likes_button = elements[0]
                        break

            except Exception as e:
                print(f"⚠️ لم يتم العثور على زر المعجبين: {e}")
                return []

            if not likes_button:
                print("⚠️ لا يمكن الوصول لقائمة المعجبين")
                return []

            # النقر على زر المعجبين
            ActionChains(self.driver).move_to_element(likes_button).click().perform()
            await asyncio.sleep(random.uniform(2, 3))

            # استخراج المعجبين
            likes_data = []
            scroll_count = 0
            max_scrolls = 10  # حد أقصى للتمرير

            while scroll_count < max_scrolls:
                # العثور على المعجبين
                user_elements = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/']")

                for element in user_elements:
                    try:
                        href = element.get_attribute('href')
                        if href and '/' in href:
                            username = href.split('/')[-2] if href.endswith('/') else href.split('/')[-1]

                            # تجنب الروابط غير المرغوبة
                            if username and not any(x in username for x in ['explore', 'accounts', 'direct', 'p']):
                                if username not in [user['username'] for user in likes_data]:
                                    likes_data.append({
                                        'username': username,
                                        'profile_url': href,
                                        'extracted_at': datetime.now().isoformat()
                                    })
                    except:
                        continue

                # التمرير لأسفل
                self.driver.execute_script("arguments[0].scrollTop += 300",
                                         self.driver.find_element(By.CSS_SELECTOR, "div[role='dialog']"))
                await asyncio.sleep(random.uniform(1, 2))
                scroll_count += 1

                # التحقق من الحد الأقصى
                if len(likes_data) >= self.safety_settings['max_likes_per_session']:
                    break

            print(f"✅ تم استخراج {len(likes_data)} معجب")
            return likes_data

        except Exception as e:
            print(f"❌ خطأ في استخراج المعجبين: {e}")
            return []

    async def close_instagram_tab(self):
        """إغلاق تبويب Instagram تلقائياً"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                print("✅ تم إغلاق Instagram تلقائياً")

        except Exception as e:
            print(f"❌ خطأ في إغلاق Instagram: {e}")

    async def full_automation_workflow(self):
        """سير العمل الكامل للأتمتة"""
        try:
            print("🚀 بدء سير العمل الكامل...")

            # 1. فتح Instagram
            if not await self.open_instagram_login():
                return False

            # 2. انتظار تسجيل الدخول
            if not await self.wait_for_login_completion():
                return False

            # 3. استخراج الكوكيز
            account_id, cookies_file = await self.extract_cookies()
            if not account_id:
                return False

            # 4. استخراج معلومات الحساب
            account_info = await self.get_account_info()
            if not account_info:
                return False

            # 5. حفظ الحساب في قاعدة البيانات
            db_account_id = await self.save_account_to_db(account_info, cookies_file)
            if not db_account_id:
                return False

            # 6. حقن واجهة الاستخراج
            await self.inject_extraction_overlay()

            # 7. تفعيل الاستماع للطلبات
            await self.listen_for_extraction_requests()

            print("✅ تم إكمال سير العمل بنجاح!")
            print(f"📊 الحساب: {account_info['username']}")
            print(f"🆔 معرف قاعدة البيانات: {db_account_id}")

            # الاحتفاظ بالمتصفح مفتوحاً للتصفح
            print("🌐 يمكنك الآن تصفح Instagram واستخراج المعجبين...")

            return True

        except Exception as e:
            print(f"❌ خطأ في سير العمل: {e}")
            return False

# مثال للاستخدام
async def main():
    automation = InstagramAutomation()
    await automation.full_automation_workflow()

if __name__ == "__main__":
    asyncio.run(main())
