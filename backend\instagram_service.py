"""
خدمة استخراج Instagram
"""

import asyncio
import time
import random
from typing import List, Dict, Optional, Callable
from datetime import datetime
from sqlalchemy.orm import Session

# استيراد مكتبات Instagram
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    import undetected_chromedriver as uc
    from bs4 import BeautifulSoup
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium غير متوفر - سيتم استخدام وضع المحاكاة")

from database import InstagramAccount, ExtractionJob, ExtractedUser
from security import verify_password

class InstagramExtractorService:
    """خدمة استخراج المعجبين من Instagram"""
    
    def __init__(self):
        self.driver = None
        self.is_logged_in = False
        self.current_account = None
        self.progress_callback = None
        
    async def set_progress_callback(self, callback: Callable):
        """تعيين دالة تحديث التقدم"""
        self.progress_callback = callback
    
    async def update_progress(self, step: int, total: int, message: str):
        """تحديث التقدم"""
        if self.progress_callback:
            percentage = (step / total) * 100
            await self.progress_callback({
                "current_step": step,
                "total_steps": total,
                "message": message,
                "percentage": percentage
            })
    
    async def initialize_driver(self):
        """تهيئة متصفح Chrome"""
        if not SELENIUM_AVAILABLE:
            raise Exception("Selenium غير متوفر")
        
        await self.update_progress(1, 9, "تهيئة المتصفح...")
        
        try:
            options = Options()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # استخدام undetected-chromedriver
            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            await asyncio.sleep(2)
            return True
            
        except Exception as e:
            raise Exception(f"فشل في تهيئة المتصفح: {str(e)}")
    
    async def login_to_instagram(self, username: str, password: str):
        """تسجيل الدخول إلى Instagram"""
        await self.update_progress(2, 9, "تسجيل الدخول إلى Instagram...")
        
        try:
            # الانتقال إلى صفحة تسجيل الدخول
            self.driver.get("https://www.instagram.com/accounts/login/")
            await asyncio.sleep(random.uniform(3, 5))
            
            # العثور على حقول تسجيل الدخول
            username_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = self.driver.find_element(By.NAME, "password")
            
            # إدخال البيانات
            username_field.clear()
            username_field.send_keys(username)
            await asyncio.sleep(random.uniform(1, 2))
            
            password_field.clear()
            password_field.send_keys(password)
            await asyncio.sleep(random.uniform(1, 2))
            
            # النقر على زر تسجيل الدخول
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # انتظار تحميل الصفحة
            await asyncio.sleep(random.uniform(5, 8))
            
            # التحقق من نجاح تسجيل الدخول
            if "instagram.com/accounts/login" not in self.driver.current_url:
                self.is_logged_in = True
                return True
            else:
                raise Exception("فشل في تسجيل الدخول - تحقق من البيانات")
                
        except Exception as e:
            raise Exception(f"خطأ في تسجيل الدخول: {str(e)}")
    
    async def navigate_to_post(self, post_url: str):
        """الانتقال إلى المنشور"""
        await self.update_progress(3, 9, "الانتقال إلى المنشور...")
        
        try:
            self.driver.get(post_url)
            await asyncio.sleep(random.uniform(3, 5))
            
            # التحقق من تحميل المنشور
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "article"))
            )
            
            return True
            
        except Exception as e:
            raise Exception(f"فشل في تحميل المنشور: {str(e)}")
    
    async def find_likes_button(self):
        """العثور على زر المعجبين"""
        await self.update_progress(4, 9, "البحث عن زر المعجبين...")
        
        try:
            # البحث عن زر المعجبين
            likes_selectors = [
                "a[href*='/liked_by/']",
                "button[aria-label*='like']",
                "span:contains('likes')",
                "a:contains('others')"
            ]
            
            likes_button = None
            for selector in likes_selectors:
                try:
                    if 'contains' in selector:
                        # استخدام XPath للبحث بالنص
                        xpath = f"//*[contains(text(), 'like')]"
                        likes_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        likes_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if likes_button:
                        break
                except:
                    continue
            
            if not likes_button:
                raise Exception("لم يتم العثور على زر المعجبين")
            
            return likes_button
            
        except Exception as e:
            raise Exception(f"فشل في العثور على زر المعجبين: {str(e)}")
    
    async def open_likes_modal(self, likes_button):
        """فتح نافذة المعجبين"""
        await self.update_progress(5, 9, "فتح قائمة المعجبين...")
        
        try:
            likes_button.click()
            await asyncio.sleep(random.uniform(2, 4))
            
            # انتظار تحميل النافذة
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[role='dialog']"))
            )
            
            return True
            
        except Exception as e:
            raise Exception(f"فشل في فتح قائمة المعجبين: {str(e)}")
    
    async def extract_users_from_modal(self, max_users: int = 1000):
        """استخراج المستخدمين من النافذة"""
        await self.update_progress(6, 9, "بدء استخراج المعجبين...")
        
        extracted_users = []
        seen_usernames = set()
        scroll_attempts = 0
        max_scroll_attempts = 50
        
        try:
            while len(extracted_users) < max_users and scroll_attempts < max_scroll_attempts:
                await self.update_progress(
                    6, 9, 
                    f"استخراج المعجبين... ({len(extracted_users)}/{max_users})"
                )
                
                # الحصول على HTML الحالي
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                
                # البحث عن عناصر المستخدمين
                user_elements = soup.find_all('div', {'role': 'button'}) + \
                               soup.find_all('a', href=True)
                
                for element in user_elements:
                    try:
                        # استخراج اسم المستخدم
                        username = None
                        if element.get('href') and '/' in element.get('href'):
                            username = element.get('href').strip('/').split('/')[-1]
                        elif element.text:
                            username = element.text.strip()
                        
                        if username and username not in seen_usernames and len(username) > 2:
                            seen_usernames.add(username)
                            
                            # استخراج معلومات إضافية
                            user_data = await self.extract_user_details(element, username)
                            extracted_users.append(user_data)
                            
                            if len(extracted_users) >= max_users:
                                break
                                
                    except Exception as e:
                        continue
                
                # تمرير القائمة لأسفل
                self.driver.execute_script(
                    "arguments[0].scrollTop = arguments[0].scrollHeight",
                    self.driver.find_element(By.CSS_SELECTOR, "[role='dialog']")
                )
                
                await asyncio.sleep(random.uniform(1, 3))
                scroll_attempts += 1
            
            await self.update_progress(7, 9, f"تم استخراج {len(extracted_users)} مستخدم")
            return extracted_users
            
        except Exception as e:
            raise Exception(f"خطأ في استخراج المستخدمين: {str(e)}")
    
    async def extract_user_details(self, element, username: str) -> Dict:
        """استخراج تفاصيل المستخدم"""
        try:
            # البيانات الأساسية
            user_data = {
                "username": username,
                "full_name": None,
                "profile_pic_url": None,
                "profile_url": f"https://www.instagram.com/{username}/",
                "is_verified": False,
                "follower_count": None,
                "following_count": None,
                "post_count": None,
                "bio": None,
                "threads_url": f"https://www.threads.net/@{username}",
                "facebook_url": None,
                "extracted_at": datetime.utcnow().isoformat()
            }
            
            # محاولة استخراج الاسم الكامل
            name_element = element.find('div') or element.find('span')
            if name_element and name_element.text:
                user_data["full_name"] = name_element.text.strip()
            
            # محاولة استخراج صورة البروفايل
            img_element = element.find('img')
            if img_element and img_element.get('src'):
                user_data["profile_pic_url"] = img_element.get('src')
            
            # التحقق من التوثيق
            if element.find('svg') or 'verified' in str(element):
                user_data["is_verified"] = True
            
            return user_data
            
        except Exception:
            # إرجاع البيانات الأساسية في حالة الخطأ
            return {
                "username": username,
                "full_name": None,
                "profile_pic_url": f"https://via.placeholder.com/150x150/e1306c/ffffff?text={username[0].upper()}",
                "profile_url": f"https://www.instagram.com/{username}/",
                "is_verified": False,
                "follower_count": None,
                "following_count": None,
                "post_count": None,
                "bio": None,
                "threads_url": f"https://www.threads.net/@{username}",
                "facebook_url": None,
                "extracted_at": datetime.utcnow().isoformat()
            }
    
    async def cleanup(self):
        """تنظيف الموارد"""
        await self.update_progress(9, 9, "إنهاء العملية...")
        
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        
        self.driver = None
        self.is_logged_in = False
        self.current_account = None
    
    async def extract_likes(
        self, 
        account: InstagramAccount, 
        post_url: str, 
        password: str,
        max_users: int = 1000
    ) -> List[Dict]:
        """الدالة الرئيسية لاستخراج المعجبين"""
        
        try:
            # التحقق من كلمة المرور
            if not verify_password(password, account.password_hash):
                raise Exception("كلمة المرور غير صحيحة")
            
            # تهيئة المتصفح
            await self.initialize_driver()
            
            # تسجيل الدخول
            await self.login_to_instagram(account.username, password)
            
            # الانتقال إلى المنشور
            await self.navigate_to_post(post_url)
            
            # العثور على زر المعجبين
            likes_button = await self.find_likes_button()
            
            # فتح قائمة المعجبين
            await self.open_likes_modal(likes_button)
            
            # استخراج المستخدمين
            users = await self.extract_users_from_modal(max_users)
            
            await self.update_progress(8, 9, "تحسين البيانات...")
            await asyncio.sleep(2)
            
            return users
            
        except Exception as e:
            raise e
        finally:
            await self.cleanup()

# دالة محاكاة للاختبار
async def simulate_extraction(post_url: str, progress_callback=None) -> List[Dict]:
    """محاكاة عملية الاستخراج للاختبار"""
    
    if progress_callback:
        await progress_callback({
            "current_step": 1,
            "total_steps": 9,
            "message": "بدء المحاكاة...",
            "percentage": 11
        })
    
    # محاكاة الخطوات
    steps = [
        "تهيئة المتصفح...",
        "تسجيل الدخول...",
        "الانتقال إلى المنشور...",
        "البحث عن المعجبين...",
        "فتح قائمة المعجبين...",
        "استخراج البيانات...",
        "تحسين النتائج...",
        "إنهاء العملية..."
    ]
    
    for i, step in enumerate(steps, 2):
        if progress_callback:
            await progress_callback({
                "current_step": i,
                "total_steps": 9,
                "message": step,
                "percentage": (i / 9) * 100
            })
        await asyncio.sleep(random.uniform(1, 3))
    
    # إرجاع بيانات تجريبية
    return [
        {
            "username": "user_example_1",
            "full_name": "مستخدم تجريبي 1",
            "profile_pic_url": "https://via.placeholder.com/150x150/e1306c/ffffff?text=U1",
            "profile_url": "https://www.instagram.com/user_example_1/",
            "is_verified": False,
            "follower_count": 1250,
            "following_count": 890,
            "post_count": 45,
            "bio": "مستخدم تجريبي للاختبار 📸",
            "threads_url": "https://www.threads.net/@user_example_1",
            "facebook_url": None,
            "extracted_at": datetime.utcnow().isoformat()
        },
        {
            "username": "verified_user_2",
            "full_name": "مستخدم موثق",
            "profile_pic_url": "https://via.placeholder.com/150x150/833ab4/ffffff?text=V2",
            "profile_url": "https://www.instagram.com/verified_user_2/",
            "is_verified": True,
            "follower_count": 125000,
            "following_count": 450,
            "post_count": 320,
            "bio": "حساب موثق ⭐ | مؤثر رقمي",
            "threads_url": "https://www.threads.net/@verified_user_2",
            "facebook_url": "https://www.facebook.com/verified_user_2",
            "extracted_at": datetime.utcnow().isoformat()
        }
    ]
