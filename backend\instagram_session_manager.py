#!/usr/bin/env python3
"""
مدير جلسات Instagram المتقدم - استخراج الكوكيز والربط التلقائي
"""

import json
import os
import sqlite3
import time
from datetime import datetime, timed<PERSON>ta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import undetected_chromedriver as uc
import requests
from urllib.parse import urlparse
import base64
from cryptography.fernet import Fernet

class InstagramSessionManager:
    def __init__(self, db_path="../database/instagram_accounts.db"):
        self.db_path = db_path
        self.driver = None
        self.session_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.session_key)
        
    def _get_or_create_encryption_key(self):
        """إنشاء أو جلب مفتاح التشفير"""
        key_file = "session_encryption.key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def detect_open_instagram_sessions(self):
        """كشف جلسات Instagram المفتوحة في المتصفحات"""
        detected_sessions = []
        
        # كشف Chrome المفتوح
        chrome_sessions = self._detect_chrome_sessions()
        detected_sessions.extend(chrome_sessions)
        
        # كشف Firefox المفتوح
        firefox_sessions = self._detect_firefox_sessions()
        detected_sessions.extend(firefox_sessions)
        
        # كشف Edge المفتوح
        edge_sessions = self._detect_edge_sessions()
        detected_sessions.extend(edge_sessions)
        
        return detected_sessions
    
    def _detect_chrome_sessions(self):
        """كشف جلسات Chrome المفتوحة"""
        sessions = []
        
        try:
            # محاولة الاتصال بـ Chrome المفتوح
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            driver = webdriver.Chrome(options=chrome_options)
            
            # التحقق من وجود تبويبات Instagram
            for handle in driver.window_handles:
                driver.switch_to.window(handle)
                current_url = driver.current_url
                
                if "instagram.com" in current_url:
                    session_info = self._extract_session_info(driver)
                    if session_info:
                        session_info['browser'] = 'Chrome'
                        session_info['window_handle'] = handle
                        sessions.append(session_info)
            
            driver.quit()
            
        except Exception as e:
            print(f"لا توجد جلسات Chrome مفتوحة: {e}")
        
        return sessions
    
    def _detect_firefox_sessions(self):
        """كشف جلسات Firefox المفتوحة"""
        sessions = []
        
        try:
            # محاولة الاتصال بـ Firefox المفتوح
            from selenium.webdriver.firefox.options import Options as FirefoxOptions
            from selenium.webdriver.firefox.service import Service
            
            firefox_options = FirefoxOptions()
            firefox_options.add_argument("--marionette-port=2828")
            
            driver = webdriver.Firefox(options=firefox_options)
            
            # التحقق من وجود تبويبات Instagram
            for handle in driver.window_handles:
                driver.switch_to.window(handle)
                current_url = driver.current_url
                
                if "instagram.com" in current_url:
                    session_info = self._extract_session_info(driver)
                    if session_info:
                        session_info['browser'] = 'Firefox'
                        session_info['window_handle'] = handle
                        sessions.append(session_info)
            
            driver.quit()
            
        except Exception as e:
            print(f"لا توجد جلسات Firefox مفتوحة: {e}")
        
        return sessions
    
    def _detect_edge_sessions(self):
        """كشف جلسات Edge المفتوحة"""
        sessions = []
        
        try:
            # محاولة الاتصال بـ Edge المفتوح
            from selenium.webdriver.edge.options import Options as EdgeOptions
            
            edge_options = EdgeOptions()
            edge_options.add_experimental_option("debuggerAddress", "127.0.0.1:9223")
            
            driver = webdriver.Edge(options=edge_options)
            
            # التحقق من وجود تبويبات Instagram
            for handle in driver.window_handles:
                driver.switch_to.window(handle)
                current_url = driver.current_url
                
                if "instagram.com" in current_url:
                    session_info = self._extract_session_info(driver)
                    if session_info:
                        session_info['browser'] = 'Edge'
                        session_info['window_handle'] = handle
                        sessions.append(session_info)
            
            driver.quit()
            
        except Exception as e:
            print(f"لا توجد جلسات Edge مفتوحة: {e}")
        
        return sessions
    
    def _extract_session_info(self, driver):
        """استخراج معلومات الجلسة من المتصفح"""
        try:
            # استخراج الكوكيز
            cookies = driver.get_cookies()
            
            # استخراج معلومات المستخدم
            user_info = self._get_user_info_from_page(driver)
            
            # استخراج localStorage
            local_storage = driver.execute_script("return window.localStorage;")
            
            # استخراج sessionStorage
            session_storage = driver.execute_script("return window.sessionStorage;")
            
            # التحقق من صحة الجلسة
            if self._validate_session(cookies, user_info):
                return {
                    'username': user_info.get('username'),
                    'user_id': user_info.get('user_id'),
                    'full_name': user_info.get('full_name'),
                    'profile_pic_url': user_info.get('profile_pic_url'),
                    'is_verified': user_info.get('is_verified', False),
                    'follower_count': user_info.get('follower_count', 0),
                    'following_count': user_info.get('following_count', 0),
                    'cookies': cookies,
                    'local_storage': local_storage,
                    'session_storage': session_storage,
                    'current_url': driver.current_url,
                    'detected_at': datetime.now().isoformat(),
                    'session_valid': True
                }
            
        except Exception as e:
            print(f"خطأ في استخراج معلومات الجلسة: {e}")
        
        return None
    
    def _get_user_info_from_page(self, driver):
        """استخراج معلومات المستخدم من الصفحة"""
        user_info = {}
        
        try:
            # محاولة الحصول على معلومات المستخدم من الصفحة الحالية
            current_url = driver.current_url
            
            if "/accounts/edit/" in current_url or "instagram.com/" in current_url:
                # استخراج من JavaScript
                user_data = driver.execute_script("""
                    try {
                        // محاولة الحصول على بيانات المستخدم من window._sharedData
                        if (window._sharedData && window._sharedData.config) {
                            return {
                                username: window._sharedData.config.viewer?.username,
                                user_id: window._sharedData.config.viewer?.id,
                                full_name: window._sharedData.config.viewer?.full_name,
                                profile_pic_url: window._sharedData.config.viewer?.profile_pic_url,
                                is_verified: window._sharedData.config.viewer?.is_verified
                            };
                        }
                        
                        // محاولة أخرى من localStorage
                        const userInfo = localStorage.getItem('user_info');
                        if (userInfo) {
                            return JSON.parse(userInfo);
                        }
                        
                        // محاولة من عناصر الصفحة
                        const usernameElement = document.querySelector('a[href*="/"]');
                        if (usernameElement) {
                            const href = usernameElement.getAttribute('href');
                            const username = href.replace('/', '');
                            return { username: username };
                        }
                        
                        return null;
                    } catch (e) {
                        return null;
                    }
                """)
                
                if user_data:
                    user_info.update(user_data)
                
                # محاولة استخراج من عناصر DOM
                try:
                    # البحث عن اسم المستخدم في الصفحة
                    username_elements = driver.find_elements(By.CSS_SELECTOR, 
                        'a[href*="/"], span[title], h1, h2')
                    
                    for element in username_elements:
                        text = element.get_attribute('textContent') or element.text
                        if text and len(text) > 2 and len(text) < 30 and not user_info.get('username'):
                            user_info['username'] = text.strip()
                            break
                
                except Exception as e:
                    print(f"خطأ في استخراج اسم المستخدم: {e}")
        
        except Exception as e:
            print(f"خطأ في استخراج معلومات المستخدم: {e}")
        
        return user_info
    
    def _validate_session(self, cookies, user_info):
        """التحقق من صحة الجلسة"""
        # التحقق من وجود كوكيز مهمة
        required_cookies = ['sessionid', 'csrftoken']
        cookie_names = [cookie['name'] for cookie in cookies]
        
        for required in required_cookies:
            if required not in cookie_names:
                return False
        
        # التحقق من وجود معلومات المستخدم
        if not user_info.get('username'):
            return False
        
        return True
    
    def save_session_to_database(self, session_info):
        """حفظ معلومات الجلسة في قاعدة البيانات"""
        try:
            # تشفير البيانات الحساسة
            encrypted_cookies = self.cipher.encrypt(
                json.dumps(session_info['cookies']).encode()
            )
            encrypted_local_storage = self.cipher.encrypt(
                json.dumps(session_info['local_storage']).encode()
            )
            encrypted_session_storage = self.cipher.encrypt(
                json.dumps(session_info['session_storage']).encode()
            )
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الحساب
            cursor.execute("""
                SELECT id FROM instagram_accounts WHERE username = ?
            """, (session_info['username'],))
            
            existing_account = cursor.fetchone()
            
            if existing_account:
                # تحديث الحساب الموجود
                cursor.execute("""
                    UPDATE instagram_accounts 
                    SET cookies_data = ?, session_data = ?, local_storage_data = ?,
                        is_active = 1, last_used = CURRENT_TIMESTAMP,
                        follower_count = ?, following_count = ?,
                        profile_pic_url = ?, is_verified = ?,
                        browser_type = ?, session_valid = 1,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE username = ?
                """, (
                    base64.b64encode(encrypted_cookies).decode(),
                    base64.b64encode(encrypted_session_storage).decode(),
                    base64.b64encode(encrypted_local_storage).decode(),
                    session_info.get('follower_count', 0),
                    session_info.get('following_count', 0),
                    session_info.get('profile_pic_url'),
                    session_info.get('is_verified', False),
                    session_info.get('browser', 'Unknown'),
                    session_info['username']
                ))
                
                account_id = existing_account[0]
            else:
                # إضافة حساب جديد
                cursor.execute("""
                    INSERT INTO instagram_accounts 
                    (name, username, cookies_data, session_data, local_storage_data,
                     is_active, follower_count, following_count, profile_pic_url,
                     is_verified, browser_type, session_valid, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (
                    session_info.get('full_name', session_info['username']),
                    session_info['username'],
                    base64.b64encode(encrypted_cookies).decode(),
                    base64.b64encode(encrypted_session_storage).decode(),
                    base64.b64encode(encrypted_local_storage).decode(),
                    session_info.get('follower_count', 0),
                    session_info.get('following_count', 0),
                    session_info.get('profile_pic_url'),
                    session_info.get('is_verified', False),
                    session_info.get('browser', 'Unknown')
                ))
                
                account_id = cursor.lastrowid
            
            conn.commit()
            conn.close()
            
            print(f"✅ تم حفظ جلسة @{session_info['username']} في قاعدة البيانات")
            return account_id
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الجلسة: {e}")
            return None
    
    def load_session_from_database(self, username):
        """تحميل جلسة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT cookies_data, session_data, local_storage_data, browser_type
                FROM instagram_accounts 
                WHERE username = ? AND session_valid = 1
            """, (username,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                # فك تشفير البيانات
                cookies_data = json.loads(
                    self.cipher.decrypt(base64.b64decode(result[0])).decode()
                )
                session_data = json.loads(
                    self.cipher.decrypt(base64.b64decode(result[1])).decode()
                )
                local_storage_data = json.loads(
                    self.cipher.decrypt(base64.b64decode(result[2])).decode()
                )
                
                return {
                    'cookies': cookies_data,
                    'session_storage': session_data,
                    'local_storage': local_storage_data,
                    'browser_type': result[3]
                }
            
        except Exception as e:
            print(f"❌ خطأ في تحميل الجلسة: {e}")
        
        return None
    
    def create_browser_with_session(self, username):
        """إنشاء متصفح مع جلسة محفوظة"""
        session_data = self.load_session_from_database(username)
        
        if not session_data:
            print(f"❌ لا توجد جلسة محفوظة للمستخدم @{username}")
            return None
        
        try:
            # إعداد المتصفح
            chrome_options = uc.ChromeOptions()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            driver = uc.Chrome(options=chrome_options)
            
            # الانتقال إلى Instagram
            driver.get("https://www.instagram.com/")
            time.sleep(3)
            
            # تطبيق الكوكيز
            for cookie in session_data['cookies']:
                try:
                    driver.add_cookie(cookie)
                except Exception as e:
                    print(f"تحذير: لا يمكن إضافة كوكي {cookie.get('name')}: {e}")
            
            # تطبيق localStorage
            for key, value in session_data['local_storage'].items():
                try:
                    driver.execute_script(f"localStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    print(f"تحذير: لا يمكن تطبيق localStorage {key}: {e}")
            
            # تطبيق sessionStorage
            for key, value in session_data['session_storage'].items():
                try:
                    driver.execute_script(f"sessionStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    print(f"تحذير: لا يمكن تطبيق sessionStorage {key}: {e}")
            
            # إعادة تحميل الصفحة لتطبيق الجلسة
            driver.refresh()
            time.sleep(5)
            
            # التحقق من نجاح تسجيل الدخول
            if self._verify_login_success(driver):
                print(f"✅ تم تسجيل الدخول بنجاح للمستخدم @{username}")
                self.driver = driver
                return driver
            else:
                print(f"❌ فشل في تسجيل الدخول للمستخدم @{username}")
                driver.quit()
                return None
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المتصفح: {e}")
            return None
    
    def _verify_login_success(self, driver):
        """التحقق من نجاح تسجيل الدخول"""
        try:
            # البحث عن عناصر تدل على تسجيل الدخول
            login_indicators = [
                "//a[contains(@href, '/accounts/edit/')]",  # رابط تعديل الحساب
                "//button[contains(@aria-label, 'New post')]",  # زر منشور جديد
                "//a[contains(@href, '/direct/')]",  # رابط الرسائل
                "//span[text()='Home']",  # كلمة الرئيسية
            ]
            
            for indicator in login_indicators:
                try:
                    element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, indicator))
                    )
                    if element:
                        return True
                except TimeoutException:
                    continue
            
            # التحقق من عدم وجود صفحة تسجيل الدخول
            current_url = driver.current_url
            if "/accounts/login/" not in current_url:
                return True
            
        except Exception as e:
            print(f"خطأ في التحقق من تسجيل الدخول: {e}")
        
        return False
    
    def get_active_sessions(self):
        """الحصول على الجلسات النشطة من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT username, name, is_verified, follower_count, 
                       following_count, browser_type, last_used
                FROM instagram_accounts 
                WHERE session_valid = 1 AND is_active = 1
                ORDER BY last_used DESC
            """)
            
            sessions = []
            for row in cursor.fetchall():
                sessions.append({
                    'username': row[0],
                    'name': row[1],
                    'is_verified': bool(row[2]),
                    'follower_count': row[3],
                    'following_count': row[4],
                    'browser_type': row[5],
                    'last_used': row[6]
                })
            
            conn.close()
            return sessions
            
        except Exception as e:
            print(f"❌ خطأ في جلب الجلسات النشطة: {e}")
            return []
    
    def cleanup_expired_sessions(self, days=7):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            expiry_date = datetime.now() - timedelta(days=days)
            
            cursor.execute("""
                UPDATE instagram_accounts 
                SET session_valid = 0 
                WHERE last_used < ? OR last_used IS NULL
            """, (expiry_date.isoformat(),))
            
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(f"✅ تم تنظيف {affected_rows} جلسة منتهية الصلاحية")
            return affected_rows
            
        except Exception as e:
            print(f"❌ خطأ في تنظيف الجلسات: {e}")
            return 0

# دالة مساعدة للاستخدام السريع
def detect_and_save_instagram_sessions():
    """كشف وحفظ جلسات Instagram المفتوحة"""
    manager = InstagramSessionManager()
    
    print("🔍 البحث عن جلسات Instagram مفتوحة...")
    sessions = manager.detect_open_instagram_sessions()
    
    if not sessions:
        print("❌ لا توجد جلسات Instagram مفتوحة")
        return []
    
    saved_sessions = []
    for session in sessions:
        print(f"📱 تم العثور على جلسة: @{session['username']} في {session['browser']}")
        account_id = manager.save_session_to_database(session)
        if account_id:
            saved_sessions.append({
                'account_id': account_id,
                'username': session['username'],
                'browser': session['browser']
            })
    
    print(f"✅ تم حفظ {len(saved_sessions)} جلسة بنجاح")
    return saved_sessions

if __name__ == "__main__":
    # اختبار النظام
    detect_and_save_instagram_sessions()
