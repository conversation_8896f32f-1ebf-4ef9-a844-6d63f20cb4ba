"""
Instagram Likes Extractor - Backend API
FastAPI Backend منفصل تماماً عن Frontend
"""

from fastapi import FastAPI, Depends, HTTPException, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import uvicorn
import os
import asyncio
from instagram_automation import InstagramAutomation

# استيراد الوحدات المحلية
from database import get_db, init_database, InstagramAccount, ExtractionJob, ExtractedUser
from models import *
from security import hash_password, verify_password, settings, SecurityUtils
from instagram_service import InstagramExtractorService
from two_factor_auth import two_fa_service

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Instagram Likes Extractor API",
    description="API لاستخراج قائمة المعجبين من منشورات Instagram",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الملفات الثابتة
app.mount("/exports", StaticFiles(directory=settings.EXPORT_DIR), name="exports")

# تهيئة قاعدة البيانات عند بدء التطبيق
@app.on_event("startup")
async def startup_event():
    """تهيئة التطبيق"""
    init_database()
    print("🚀 Instagram Likes Extractor API بدأ العمل!")
    print(f"📊 API Docs: http://localhost:8000/api/docs")
    print(f"🗄️ Database: {settings.DATABASE_URL}")

# ==================== API Routes ====================

# الصفحة الرئيسية
@app.get("/", response_model=MessageResponse)
async def root():
    """الصفحة الرئيسية للـ API"""
    return MessageResponse(
        message="Instagram Likes Extractor API v2.0 - Backend منفصل",
        success=True
    )

# ==================== إدارة حسابات Instagram ====================

@app.post("/api/accounts", response_model=InstagramAccountResponse)
async def create_account(
    account: InstagramAccountCreate,
    db: Session = Depends(get_db)
):
    """إنشاء حساب Instagram جديد"""

    # التحقق من صحة البيانات
    validation = SecurityUtils.validate_instagram_credentials(
        account.username, account.password
    )
    if not validation["valid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=validation["errors"]
        )

    # التحقق من عدم وجود الحساب مسبقاً
    existing_account = db.query(InstagramAccount).filter(
        InstagramAccount.username == account.username
    ).first()

    if existing_account:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم المستخدم موجود مسبقاً"
        )

    # إنشاء الحساب الجديد
    db_account = InstagramAccount(
        name=SecurityUtils.sanitize_input(account.name),
        username=SecurityUtils.sanitize_input(account.username),
        password_hash=hash_password(account.password),
        email=SecurityUtils.sanitize_input(account.email) if account.email else None,
        phone=SecurityUtils.sanitize_input(account.phone) if account.phone else None,
        is_active=account.is_active,
        notes=SecurityUtils.sanitize_input(account.notes) if account.notes else None
    )

    db.add(db_account)
    db.commit()
    db.refresh(db_account)

    return db_account

@app.get("/api/accounts", response_model=List[InstagramAccountResponse])
async def get_accounts(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """الحصول على قائمة حسابات Instagram"""

    query = db.query(InstagramAccount)

    if active_only:
        query = query.filter(InstagramAccount.is_active == True)

    accounts = query.offset(skip).limit(limit).all()
    return accounts

@app.get("/api/accounts/{account_id}", response_model=InstagramAccountResponse)
async def get_account(account_id: int, db: Session = Depends(get_db)):
    """الحصول على حساب Instagram محدد"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    return account

@app.put("/api/accounts/{account_id}", response_model=InstagramAccountResponse)
async def update_account(
    account_id: int,
    account_update: InstagramAccountUpdate,
    db: Session = Depends(get_db)
):
    """تحديث حساب Instagram"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    # تحديث البيانات
    update_data = account_update.dict(exclude_unset=True)

    if "password" in update_data:
        update_data["password_hash"] = hash_password(update_data.pop("password"))

    for field, value in update_data.items():
        if hasattr(account, field):
            setattr(account, field, SecurityUtils.sanitize_input(str(value)) if isinstance(value, str) else value)

    db.commit()
    db.refresh(account)

    return account

@app.delete("/api/accounts/{account_id}", response_model=MessageResponse)
async def delete_account(account_id: int, db: Session = Depends(get_db)):
    """حذف حساب Instagram"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    db.delete(account)
    db.commit()

    return MessageResponse(message="تم حذف الحساب بنجاح")

# ==================== إضافة حساب مكتشف تلقائياً ====================

@app.post("/api/accounts/auto-detected", response_model=InstagramAccountResponse)
async def create_auto_detected_account(
    account_data: dict,
    db: Session = Depends(get_db)
):
    """إضافة حساب تم اكتشافه تلقائياً"""
    try:
        # استخراج البيانات من الطلب
        username = account_data.get('username', '').strip()
        name = account_data.get('name', username)

        if not username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="اسم المستخدم مطلوب"
            )

        # التحقق من عدم وجود الحساب مسبقاً
        existing_account = db.query(InstagramAccount).filter(
            InstagramAccount.username == username
        ).first()

        if existing_account:
            # إذا كان الحساب موجود، نحدث آخر استخدام ونعيده
            existing_account.last_used = datetime.utcnow()
            existing_account.notes = f"تم اكتشافه مرة أخرى في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            db.commit()
            db.refresh(existing_account)
            return existing_account

        # إنشاء حساب جديد للحساب المكتشف تلقائياً
        new_account = InstagramAccount(
            name=name,
            username=username,
            password_hash=hash_password('auto_detected_account'),  # كلمة مرور افتراضية
            email=account_data.get('email'),
            phone=account_data.get('phone'),
            is_verified=account_data.get('is_verified', False),
            is_active=True,
            notes=account_data.get('notes', f"تم اكتشافه تلقائياً في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
            profile_pic_url=account_data.get('profile_pic_url'),
            follower_count=account_data.get('follower_count', 0),
            following_count=account_data.get('following_count', 0),
            last_used=datetime.utcnow()
        )

        db.add(new_account)
        db.commit()
        db.refresh(new_account)

        return new_account

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إضافة الحساب المكتشف: {str(e)}"
        )

# ==================== مهام الاستخراج ====================

@app.post("/api/extract", response_model=ExtractionJobResponse)
async def start_extraction(
    job_request: ExtractionJobCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """بدء مهمة استخراج جديدة"""

    # التحقق من صحة الرابط
    url_validation = SecurityUtils.validate_instagram_url(job_request.post_url)
    if not url_validation["valid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=url_validation["errors"]
        )

    # التحقق من وجود الحساب
    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == job_request.account_id,
        InstagramAccount.is_active == True
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود أو غير نشط"
        )

    # إنشاء مهمة جديدة
    job = ExtractionJob(
        account_id=job_request.account_id,
        post_url=job_request.post_url,
        status="pending"
    )

    db.add(job)
    db.commit()
    db.refresh(job)

    # بدء المهمة في الخلفية
    background_tasks.add_task(run_extraction_job, job.id, db)

    return job

@app.get("/api/jobs", response_model=List[ExtractionJobResponse])
async def get_jobs(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """الحصول على قائمة مهام الاستخراج"""

    query = db.query(ExtractionJob)

    if status_filter:
        query = query.filter(ExtractionJob.status == status_filter)

    jobs = query.order_by(ExtractionJob.created_at.desc()).offset(skip).limit(limit).all()
    return jobs

@app.get("/api/jobs/{job_id}", response_model=ExtractionJobResponse)
async def get_job(job_id: int, db: Session = Depends(get_db)):
    """الحصول على مهمة استخراج محددة"""

    job = db.query(ExtractionJob).filter(ExtractionJob.id == job_id).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    return job

@app.get("/api/jobs/{job_id}/users", response_model=List[ExtractedUserResponse])
async def get_job_users(
    job_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """الحصول على المستخدمين المستخرجين من مهمة محددة"""

    users = db.query(ExtractedUser).filter(
        ExtractedUser.job_id == job_id
    ).offset(skip).limit(limit).all()

    return users

# ==================== التصدير ====================

@app.post("/api/export", response_model=ExportResponse)
async def export_data(
    export_request: ExportRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """تصدير بيانات مهمة الاستخراج"""

    # التحقق من وجود المهمة
    job = db.query(ExtractionJob).filter(
        ExtractionJob.id == export_request.job_id
    ).first()

    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المهمة غير موجودة"
        )

    # إنشاء ملف التصدير
    filename = SecurityUtils.generate_safe_filename(
        f"instagram_likes_{job.id}.{export_request.format}"
    )

    # بدء عملية التصدير في الخلفية
    background_tasks.add_task(
        export_job_data,
        export_request.job_id,
        export_request.format,
        filename,
        db
    )

    return ExportResponse(
        download_url=f"/exports/{filename}",
        filename=filename,
        file_size=0,  # سيتم تحديثه بعد إنشاء الملف
        format=export_request.format
    )

# ==================== الإحصائيات ====================

@app.get("/api/stats/system", response_model=SystemStats)
async def get_system_stats(db: Session = Depends(get_db)):
    """الحصول على إحصائيات النظام"""

    total_accounts = db.query(InstagramAccount).count()
    active_accounts = db.query(InstagramAccount).filter(
        InstagramAccount.is_active == True
    ).count()
    total_jobs = db.query(ExtractionJob).count()
    total_extracted_users = db.query(ExtractedUser).count()

    # مهام اليوم
    from datetime import date
    today = date.today()
    jobs_today = db.query(ExtractionJob).filter(
        ExtractionJob.created_at >= today
    ).count()

    return SystemStats(
        total_accounts=total_accounts,
        active_accounts=active_accounts,
        total_jobs=total_jobs,
        total_extracted_users=total_extracted_users,
        jobs_today=jobs_today
    )

# ==================== الأتمتة المتقدمة ====================

# متغير عام للأتمتة
automation_instance = None

@app.post("/api/automation/start-login")
async def start_automated_login():
    """بدء عملية تسجيل الدخول التلقائي"""
    global automation_instance

    try:
        automation_instance = InstagramAutomation()
        success = await automation_instance.open_instagram_login()

        if success:
            return {
                "success": True,
                "message": "تم فتح Instagram بنجاح",
                "status": "waiting_for_login"
            }
        else:
            return {
                "success": False,
                "message": "فشل في فتح Instagram",
                "error": "browser_setup_failed"
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"خطأ في بدء الأتمتة: {str(e)}",
            "error": "automation_error"
        }

@app.post("/api/automation/complete-workflow")
async def complete_automation_workflow(background_tasks: BackgroundTasks):
    """إكمال سير العمل التلقائي"""
    global automation_instance

    if not automation_instance:
        raise HTTPException(status_code=400, detail="لم يتم بدء عملية الأتمتة")

    try:
        # تشغيل سير العمل في الخلفية
        background_tasks.add_task(automation_instance.full_automation_workflow)

        return {
            "success": True,
            "message": "تم بدء سير العمل التلقائي",
            "status": "processing"
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"خطأ في سير العمل: {str(e)}",
            "error": "workflow_error"
        }

@app.post("/api/automation/extract-likes")
async def extract_likes_from_post(request: dict):
    """استخراج المعجبين من منشور محدد"""
    global automation_instance

    if not automation_instance:
        raise HTTPException(status_code=400, detail="لم يتم بدء عملية الأتمتة")

    try:
        post_url = request.get("url")
        if not post_url:
            raise HTTPException(status_code=400, detail="رابط المنشور مطلوب")

        # استخراج المعجبين
        likes_data = await automation_instance.safe_extract_likes(post_url)

        return {
            "success": True,
            "message": f"تم استخراج {len(likes_data)} معجب",
            "data": likes_data,
            "post_url": post_url,
            "extracted_count": len(likes_data)
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"خطأ في استخراج المعجبين: {str(e)}",
            "error": "extraction_error"
        }

@app.get("/api/automation/status")
async def get_automation_status():
    """الحصول على حالة الأتمتة"""
    global automation_instance

    return {
        "automation_active": automation_instance is not None,
        "browser_active": automation_instance.driver is not None if automation_instance else False,
        "extraction_overlay_injected": automation_instance.extraction_overlay_injected if automation_instance else False
    }

@app.post("/api/automation/close")
async def close_automation():
    """إغلاق الأتمتة"""
    global automation_instance

    if automation_instance:
        await automation_instance.close_instagram_tab()
        automation_instance = None

        return {
            "success": True,
            "message": "تم إغلاق الأتمتة بنجاح"
        }

    return {
        "success": True,
        "message": "لا توجد أتمتة نشطة"
    }

# ==================== المصادقة الثنائية (2FA) ====================

@app.post("/api/accounts/{account_id}/setup-2fa")
async def setup_2fa_for_account(account_id: int, db: Session = Depends(get_db)):
    """إعداد المصادقة الثنائية لحساب"""

    # التحقق من وجود الحساب
    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    if account.has_2fa:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية مفعلة مسبقاً"
        )

    try:
        # إعداد 2FA
        setup_data = two_fa_service.setup_2fa_for_account(account.username)

        # حفظ البيانات المشفرة في قاعدة البيانات (مؤقتاً حتى التحقق)
        account.two_fa_secret = setup_data['encrypted_secret']
        account.two_fa_backup_codes = setup_data['encrypted_backup_codes']
        account.two_fa_method = 'app'
        # لا نفعل has_2fa حتى يتم التحقق

        db.commit()

        return {
            "success": True,
            "secret": setup_data['secret'],  # للعرض مرة واحدة فقط
            "qr_code": setup_data['qr_code'],
            "backup_codes": setup_data['backup_codes']  # للعرض مرة واحدة فقط
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إعداد 2FA: {str(e)}"
        )

@app.post("/api/accounts/{account_id}/verify-2fa-setup")
async def verify_2fa_setup(
    account_id: int,
    verification: dict,
    db: Session = Depends(get_db)
):
    """التحقق من إعداد 2FA وتفعيله"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    if account.has_2fa:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية مفعلة مسبقاً"
        )

    code = verification.get('code')
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رمز التحقق مطلوب"
        )

    try:
        # التحقق من الرمز
        is_valid, _ = two_fa_service.validate_2fa_login(
            account.two_fa_secret,
            account.two_fa_backup_codes,
            code
        )

        if is_valid:
            # تفعيل 2FA
            account.has_2fa = True
            db.commit()

            return {
                "success": True,
                "message": "تم تفعيل المصادقة الثنائية بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="رمز التحقق غير صحيح"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التحقق من 2FA: {str(e)}"
        )

@app.post("/api/accounts/{account_id}/disable-2fa")
async def disable_2fa_for_account(
    account_id: int,
    verification: dict,
    db: Session = Depends(get_db)
):
    """تعطيل المصادقة الثنائية"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    if not account.has_2fa:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية غير مفعلة"
        )

    code = verification.get('code')
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رمز التحقق مطلوب لتعطيل 2FA"
        )

    try:
        # التحقق من الرمز
        is_valid, _ = two_fa_service.validate_2fa_login(
            account.two_fa_secret,
            account.two_fa_backup_codes,
            code
        )

        if is_valid:
            # تعطيل 2FA
            account.has_2fa = False
            account.two_fa_secret = None
            account.two_fa_backup_codes = None
            account.two_fa_method = None
            db.commit()

            return {
                "success": True,
                "message": "تم تعطيل المصادقة الثنائية بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="رمز التحقق غير صحيح"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تعطيل 2FA: {str(e)}"
        )

@app.post("/api/accounts/{account_id}/regenerate-backup-codes")
async def regenerate_backup_codes(
    account_id: int,
    verification: dict,
    db: Session = Depends(get_db)
):
    """إعادة إنشاء backup codes"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    if not account.has_2fa:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية غير مفعلة"
        )

    code = verification.get('code')
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رمز التحقق مطلوب"
        )

    try:
        # التحقق من الرمز
        is_valid, _ = two_fa_service.validate_2fa_login(
            account.two_fa_secret,
            account.two_fa_backup_codes,
            code
        )

        if is_valid:
            # إنشاء backup codes جديدة
            new_codes_data = two_fa_service.regenerate_backup_codes()

            # تحديث قاعدة البيانات
            account.two_fa_backup_codes = new_codes_data['encrypted_backup_codes']
            db.commit()

            return {
                "success": True,
                "message": "تم إنشاء رموز احتياط جديدة",
                "backup_codes": new_codes_data['backup_codes']  # للعرض مرة واحدة فقط
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="رمز التحقق غير صحيح"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء رموز الاحتياط: {str(e)}"
        )

@app.post("/api/accounts/{account_id}/verify-2fa-login")
async def verify_2fa_login(
    account_id: int,
    verification: dict,
    db: Session = Depends(get_db)
):
    """التحقق من 2FA أثناء تسجيل الدخول"""

    account = db.query(InstagramAccount).filter(
        InstagramAccount.id == account_id
    ).first()

    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )

    if not account.has_2fa:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية غير مفعلة"
        )

    code = verification.get('code')
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رمز التحقق مطلوب"
        )

    try:
        # التحقق من الرمز
        is_valid, updated_backup_codes = two_fa_service.validate_2fa_login(
            account.two_fa_secret,
            account.two_fa_backup_codes,
            code
        )

        if is_valid:
            # تحديث backup codes إذا تم استخدام أحدها
            if updated_backup_codes:
                account.two_fa_backup_codes = updated_backup_codes
                db.commit()

            # تحديث آخر استخدام
            account.last_used = datetime.utcnow()
            account.login_attempts = 0
            db.commit()

            return {
                "success": True,
                "message": "تم التحقق من 2FA بنجاح",
                "account_id": account.id,
                "username": account.username
            }
        else:
            # زيادة عدد المحاولات الفاشلة
            account.login_attempts += 1
            account.last_login_attempt = datetime.utcnow()

            # قفل الحساب بعد 5 محاولات فاشلة
            if account.login_attempts >= 5:
                account.is_locked = True

            db.commit()

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="رمز التحقق غير صحيح"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التحقق من 2FA: {str(e)}"
        )

# ==================== وظائف مساعدة ====================

async def run_extraction_job(job_id: int, db: Session):
    """تشغيل مهمة الاستخراج"""
    # سيتم تنفيذها في ملف منفصل
    pass

async def export_job_data(job_id: int, format: str, filename: str, db: Session):
    """تصدير بيانات المهمة"""
    # سيتم تنفيذها في ملف منفصل
    pass

# تشغيل الخادم
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
