#!/usr/bin/env python3
"""
خادم مبسط جداً للنظام الكامل
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import sqlite3
import os
from datetime import datetime
import urllib.parse

# إنشاء مجلد قاعدة البيانات
os.makedirs("../database", exist_ok=True)
DB_PATH = "../database/instagram_accounts.db"

def init_database():
    """إنشاء قاعدة البيانات"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS instagram_accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            username TEXT UNIQUE NOT NULL,
            email TEXT,
            phone TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            last_used DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات")

class APIHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/':
            response = {
                "message": "Instagram Likes Extractor API v2.0 - النظام الكامل",
                "status": "running",
                "backend_connected": True
            }
        elif self.path == '/api/accounts':
            response = self.get_accounts()
        elif self.path == '/api/stats/system':
            response = self.get_system_stats()
        elif self.path == '/api/automation/status':
            response = {
                "automation_active": False,
                "browser_active": False,
                "extraction_overlay_injected": False,
                "mode": "full_backend"
            }
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/api/accounts':
            response = self.create_account(post_data)
        elif self.path == '/api/automation/start-login':
            response = {
                "success": True,
                "message": "Backend متصل - يمكن تفعيل الأتمتة الكاملة",
                "status": "backend_ready"
            }
        elif self.path == '/api/automation/complete-workflow':
            response = {
                "success": True,
                "message": "Backend جاهز للأتمتة الكاملة",
                "status": "ready_for_automation"
            }
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_PUT(self):
        """Handle PUT requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # استخراج ID من المسار
        path_parts = self.path.split('/')
        if len(path_parts) >= 4 and path_parts[2] == 'accounts':
            account_id = int(path_parts[3])
            response = self.update_account(account_id, post_data)
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_DELETE(self):
        """Handle DELETE requests"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        # استخراج ID من المسار
        path_parts = self.path.split('/')
        if len(path_parts) >= 4 and path_parts[2] == 'accounts':
            account_id = int(path_parts[3])
            response = self.delete_account(account_id)
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def get_accounts(self):
        """الحصول على جميع الحسابات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name, username, email, phone, is_active, is_verified, 
                       last_used, created_at, updated_at, notes
                FROM instagram_accounts
                ORDER BY created_at DESC
            ''')
            
            accounts = []
            for row in cursor.fetchall():
                accounts.append({
                    "id": row[0],
                    "name": row[1],
                    "username": row[2],
                    "email": row[3],
                    "phone": row[4],
                    "is_active": bool(row[5]),
                    "is_verified": bool(row[6]),
                    "last_used": row[7],
                    "created_at": row[8],
                    "updated_at": row[9],
                    "notes": row[10]
                })
            
            conn.close()
            return accounts
        except Exception as e:
            return {"error": str(e)}

    def create_account(self, post_data):
        """إنشاء حساب جديد"""
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO instagram_accounts (name, username, email, phone, is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                data.get("name", ""),
                data.get("username", ""),
                data.get("email"),
                data.get("phone"),
                data.get("is_active", True),
                data.get("notes")
            ))
            
            account_id = cursor.lastrowid
            conn.commit()
            
            # إرجاع الحساب المُنشأ
            cursor.execute('''
                SELECT id, name, username, email, phone, is_active, is_verified, 
                       last_used, created_at, updated_at, notes
                FROM instagram_accounts WHERE id = ?
            ''', (account_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            return {
                "id": row[0],
                "name": row[1],
                "username": row[2],
                "email": row[3],
                "phone": row[4],
                "is_active": bool(row[5]),
                "is_verified": bool(row[6]),
                "last_used": row[7],
                "created_at": row[8],
                "updated_at": row[9],
                "notes": row[10]
            }
            
        except Exception as e:
            return {"error": str(e), "success": False}

    def update_account(self, account_id, post_data):
        """تحديث حساب"""
        try:
            data = json.loads(post_data.decode('utf-8'))
            
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE instagram_accounts 
                SET name = ?, username = ?, email = ?, phone = ?, is_active = ?, notes = ?, updated_at = ?
                WHERE id = ?
            ''', (
                data.get("name", ""),
                data.get("username", ""),
                data.get("email"),
                data.get("phone"),
                data.get("is_active", True),
                data.get("notes"),
                datetime.now().isoformat(),
                account_id
            ))
            
            conn.commit()
            conn.close()
            
            return {"message": "تم تحديث الحساب بنجاح", "success": True}
            
        except Exception as e:
            return {"error": str(e), "success": False}

    def delete_account(self, account_id):
        """حذف حساب"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM instagram_accounts WHERE id = ?', (account_id,))
            conn.commit()
            conn.close()
            
            return {"message": "تم حذف الحساب بنجاح", "success": True}
            
        except Exception as e:
            return {"error": str(e), "success": False}

    def get_system_stats(self):
        """إحصائيات النظام"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM instagram_accounts')
            total_accounts = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM instagram_accounts WHERE is_active = 1')
            active_accounts = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                "total_accounts": total_accounts,
                "active_accounts": active_accounts,
                "total_jobs": 0,
                "total_extracted_users": 0,
                "jobs_today": 0
            }
            
        except Exception as e:
            return {"error": str(e)}

def run_server():
    """تشغيل الخادم"""
    init_database()
    
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, APIHandler)
    
    print("=" * 60)
    print("🚀 Instagram Likes Extractor - Full Backend Server")
    print("=" * 60)
    print("✅ Backend جاهز للعمل!")
    print("🌐 API Base: http://localhost:8000")
    print("📊 Accounts: http://localhost:8000/api/accounts")
    print("📈 Stats: http://localhost:8000/api/stats/system")
    print("🔧 Frontend: http://localhost:3001")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
