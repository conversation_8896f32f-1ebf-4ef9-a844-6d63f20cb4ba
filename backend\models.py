"""
نماذج Pydantic للـ API
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List
from datetime import datetime

# نماذج حساب Instagram
class InstagramAccountBase(BaseModel):
    """النموذج الأساسي لحساب Instagram"""
    name: str
    username: str
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True
    notes: Optional[str] = None

class InstagramAccountCreate(InstagramAccountBase):
    """نموذج إنشاء حساب Instagram"""
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        if not v.replace('_', '').replace('.', '').isalnum():
            raise ValueError('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        return v

class InstagramAccountUpdate(BaseModel):
    """نموذج تحديث حساب Instagram"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None
    password: Optional[str] = None

class InstagramAccountResponse(InstagramAccountBase):
    """نموذج استجابة حساب Instagram"""
    id: int
    is_verified: bool
    last_used: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# نماذج مهمة الاستخراج
class ExtractionJobCreate(BaseModel):
    """نموذج إنشاء مهمة استخراج"""
    account_id: int
    post_url: str
    
    @validator('post_url')
    def validate_post_url(cls, v):
        if not v.startswith('https://www.instagram.com/p/'):
            raise ValueError('رابط المنشور غير صحيح')
        return v

class ExtractionJobResponse(BaseModel):
    """نموذج استجابة مهمة الاستخراج"""
    id: int
    account_id: int
    post_url: str
    status: str
    total_likes: int
    extracted_count: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# نماذج المستخدم المستخرج
class ExtractedUserResponse(BaseModel):
    """نموذج استجابة المستخدم المستخرج"""
    id: int
    job_id: int
    username: str
    full_name: Optional[str]
    profile_pic_url: Optional[str]
    profile_url: Optional[str]
    is_verified: bool
    follower_count: Optional[int]
    following_count: Optional[int]
    post_count: Optional[int]
    bio: Optional[str]
    threads_url: Optional[str]
    facebook_url: Optional[str]
    extracted_at: datetime
    
    class Config:
        from_attributes = True

# نماذج الاستجابة العامة
class MessageResponse(BaseModel):
    """نموذج رسالة الاستجابة"""
    message: str
    success: bool = True

class ErrorResponse(BaseModel):
    """نموذج رسالة الخطأ"""
    error: str
    detail: Optional[str] = None
    success: bool = False

class ProgressUpdate(BaseModel):
    """نموذج تحديث التقدم"""
    job_id: int
    current_step: int
    total_steps: int
    status: str
    message: str
    percentage: float

# نماذج الإحصائيات
class AccountStats(BaseModel):
    """إحصائيات الحساب"""
    total_jobs: int
    successful_jobs: int
    failed_jobs: int
    total_extracted_users: int
    last_used: Optional[datetime]

class SystemStats(BaseModel):
    """إحصائيات النظام"""
    total_accounts: int
    active_accounts: int
    total_jobs: int
    total_extracted_users: int
    jobs_today: int

# نماذج التصدير
class ExportRequest(BaseModel):
    """طلب تصدير البيانات"""
    job_id: int
    format: str  # csv, json, excel
    
    @validator('format')
    def validate_format(cls, v):
        if v not in ['csv', 'json', 'excel']:
            raise ValueError('صيغة التصدير غير مدعومة')
        return v

class ExportResponse(BaseModel):
    """استجابة التصدير"""
    download_url: str
    filename: str
    file_size: int
    format: str

# نماذج البحث والتصفية
class SearchFilters(BaseModel):
    """مرشحات البحث"""
    username: Optional[str] = None
    is_verified: Optional[bool] = None
    min_followers: Optional[int] = None
    max_followers: Optional[int] = None
    has_bio: Optional[bool] = None

class PaginationParams(BaseModel):
    """معاملات التصفح"""
    page: int = 1
    size: int = 50
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('رقم الصفحة يجب أن يكون أكبر من 0')
        return v
    
    @validator('size')
    def validate_size(cls, v):
        if v < 1 or v > 1000:
            raise ValueError('حجم الصفحة يجب أن يكون بين 1 و 1000')
        return v

class PaginatedResponse(BaseModel):
    """استجابة مع تصفح"""
    items: List[dict]
    total: int
    page: int
    size: int
    pages: int
