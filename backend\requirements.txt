# Backend API Requirements
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.13.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
httpx==0.25.2
aiofiles==23.2.1

# Instagram Scraping Requirements
selenium>=4.15.0
undetected-chromedriver>=3.5.0
beautifulsoup4>=4.12.0
requests>=2.31.0

# Automation Requirements
asyncio
websockets>=12.0
pandas>=2.1.0
openpyxl>=3.1.0
lxml>=4.9.0
pillow>=10.1.0

# Data Processing
pandas>=2.0.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
colorama>=0.4.6
tqdm>=4.66.0
