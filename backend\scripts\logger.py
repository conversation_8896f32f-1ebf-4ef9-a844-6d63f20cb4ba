#!/usr/bin/env python3
"""
Logger System for Instagram Likes Extractor
نظام تسجيل الأخطاء والأحداث
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional
import json

class InstagramLogger:
    """نظام تسجيل مخصص للتطبيق"""
    
    def __init__(self, name: str = "instagram_extractor", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.ensure_log_dir()
        
        # إعداد المسجل
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # منع التكرار
        if not self.logger.handlers:
            self.setup_handlers()
    
    def ensure_log_dir(self):
        """إنشاء مجلد اللوجات إذا لم يكن موجوداً"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_handlers(self):
        """إعداد معالجات اللوجات"""
        # تنسيق الرسائل
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # معالج الملف (جميع المستويات)
        log_file = os.path.join(self.log_dir, f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # معالج الأخطاء (أخطاء فقط)
        error_file = os.path.join(self.log_dir, f"errors_{datetime.now().strftime('%Y%m%d')}.log")
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        # معالج الكونسول
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # إضافة المعالجات
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, extra_data: Optional[dict] = None):
        """تسجيل رسالة debug"""
        if extra_data:
            message = f"{message} | Data: {json.dumps(extra_data, ensure_ascii=False)}"
        self.logger.debug(message)
    
    def info(self, message: str, extra_data: Optional[dict] = None):
        """تسجيل رسالة معلومات"""
        if extra_data:
            message = f"{message} | Data: {json.dumps(extra_data, ensure_ascii=False)}"
        self.logger.info(message)
    
    def warning(self, message: str, extra_data: Optional[dict] = None):
        """تسجيل تحذير"""
        if extra_data:
            message = f"{message} | Data: {json.dumps(extra_data, ensure_ascii=False)}"
        self.logger.warning(message)
    
    def error(self, message: str, exception: Optional[Exception] = None, extra_data: Optional[dict] = None):
        """تسجيل خطأ"""
        if exception:
            message = f"{message} | Exception: {str(exception)}"
        if extra_data:
            message = f"{message} | Data: {json.dumps(extra_data, ensure_ascii=False)}"
        self.logger.error(message, exc_info=exception is not None)
    
    def critical(self, message: str, exception: Optional[Exception] = None, extra_data: Optional[dict] = None):
        """تسجيل خطأ حرج"""
        if exception:
            message = f"{message} | Exception: {str(exception)}"
        if extra_data:
            message = f"{message} | Data: {json.dumps(extra_data, ensure_ascii=False)}"
        self.logger.critical(message, exc_info=exception is not None)
    
    def log_extraction_start(self, post_url: str):
        """تسجيل بداية عملية الاستخراج"""
        self.info("🚀 بدء عملية استخراج المعجبين", {"post_url": post_url})
    
    def log_extraction_progress(self, current: int, total: int, status: str):
        """تسجيل تقدم الاستخراج"""
        self.info(f"📊 تقدم الاستخراج: {current}/{total}", {
            "current": current,
            "total": total,
            "status": status,
            "percentage": round((current / total) * 100, 2) if total > 0 else 0
        })
    
    def log_extraction_complete(self, total_users: int, duration: float):
        """تسجيل انتهاء عملية الاستخراج"""
        self.info(f"✅ انتهت عملية الاستخراج بنجاح", {
            "total_users": total_users,
            "duration_seconds": round(duration, 2),
            "users_per_second": round(total_users / duration, 2) if duration > 0 else 0
        })
    
    def log_extraction_error(self, error_type: str, error_message: str, exception: Optional[Exception] = None):
        """تسجيل خطأ في الاستخراج"""
        self.error(f"❌ خطأ في الاستخراج: {error_type} - {error_message}", exception, {
            "error_type": error_type,
            "error_message": error_message
        })
    
    def log_export_start(self, format_type: str, total_records: int):
        """تسجيل بداية التصدير"""
        self.info(f"📤 بدء تصدير البيانات بصيغة {format_type}", {
            "format": format_type,
            "total_records": total_records
        })
    
    def log_export_complete(self, format_type: str, filename: str, total_records: int):
        """تسجيل انتهاء التصدير"""
        self.info(f"✅ تم تصدير البيانات بنجاح", {
            "format": format_type,
            "filename": filename,
            "total_records": total_records
        })
    
    def log_export_error(self, format_type: str, error_message: str, exception: Optional[Exception] = None):
        """تسجيل خطأ في التصدير"""
        self.error(f"❌ خطأ في تصدير {format_type}: {error_message}", exception, {
            "format": format_type,
            "error_message": error_message
        })
    
    def log_system_info(self):
        """تسجيل معلومات النظام"""
        import platform
        import psutil
        
        system_info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2)
        }
        
        self.info("💻 معلومات النظام", system_info)
    
    def log_browser_info(self, browser_version: str, driver_version: str):
        """تسجيل معلومات المتصفح"""
        self.info("🌐 معلومات المتصفح", {
            "browser_version": browser_version,
            "driver_version": driver_version
        })

# إنشاء مسجل عام للتطبيق
app_logger = InstagramLogger()

def get_logger(name: str = "instagram_extractor") -> InstagramLogger:
    """الحصول على مسجل"""
    return InstagramLogger(name)

# دوال مساعدة سريعة
def log_info(message: str, extra_data: Optional[dict] = None):
    """تسجيل معلومات سريع"""
    app_logger.info(message, extra_data)

def log_error(message: str, exception: Optional[Exception] = None, extra_data: Optional[dict] = None):
    """تسجيل خطأ سريع"""
    app_logger.error(message, exception, extra_data)

def log_warning(message: str, extra_data: Optional[dict] = None):
    """تسجيل تحذير سريع"""
    app_logger.warning(message, extra_data)

def log_debug(message: str, extra_data: Optional[dict] = None):
    """تسجيل debug سريع"""
    app_logger.debug(message, extra_data)

if __name__ == "__main__":
    # اختبار نظام التسجيل
    logger = get_logger("test")
    
    logger.info("🧪 اختبار نظام التسجيل")
    logger.debug("رسالة debug")
    logger.warning("رسالة تحذير")
    logger.error("رسالة خطأ", Exception("خطأ تجريبي"))
    
    logger.log_extraction_start("https://www.instagram.com/p/test123/")
    logger.log_extraction_progress(50, 100, "جارٍ التمرير")
    logger.log_extraction_complete(100, 45.5)
    
    logger.log_export_start("CSV", 100)
    logger.log_export_complete("CSV", "test.csv", 100)
    
    print("✅ تم اختبار نظام التسجيل بنجاح!")
    print("📁 تحقق من مجلد logs/ للملفات")
