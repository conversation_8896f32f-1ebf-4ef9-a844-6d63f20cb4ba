"""
خدمات الأمان والتشفير
"""

from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعدادات التشفير
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# سياق تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """التحقق من كلمة المرور"""
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """إنشاء رمز الوصول"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """التحقق من صحة الرمز"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def encrypt_sensitive_data(data: str) -> str:
    """تشفير البيانات الحساسة (كلمات المرور)"""
    # استخدام نفس نظام bcrypt لتشفير البيانات الحساسة
    return pwd_context.hash(data)

def decrypt_sensitive_data(encrypted_data: str, original_data: str) -> bool:
    """فك تشفير البيانات الحساسة"""
    # التحقق من البيانات المشفرة
    return pwd_context.verify(original_data, encrypted_data)

class SecurityUtils:
    """أدوات الأمان"""
    
    @staticmethod
    def validate_instagram_credentials(username: str, password: str) -> dict:
        """التحقق من صحة بيانات Instagram"""
        errors = []
        
        # التحقق من اسم المستخدم
        if not username or len(username) < 3:
            errors.append("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
        
        if not username.replace('_', '').replace('.', '').isalnum():
            errors.append("اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط")
        
        # التحقق من كلمة المرور
        if not password or len(password) < 6:
            errors.append("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def validate_instagram_url(url: str) -> dict:
        """التحقق من صحة رابط Instagram"""
        errors = []
        
        if not url:
            errors.append("الرابط مطلوب")
        elif not url.startswith('https://www.instagram.com/p/'):
            errors.append("رابط المنشور غير صحيح")
        elif len(url.split('/')) < 5:
            errors.append("رابط المنشور غير مكتمل")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """تنظيف النص من الأحرف الضارة"""
        if not text:
            return ""
        
        # إزالة الأحرف الخطيرة
        dangerous_chars = ['<', '>', '"', "'", '&', '\n', '\r', '\t']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        return text.strip()
    
    @staticmethod
    def generate_safe_filename(filename: str) -> str:
        """إنشاء اسم ملف آمن"""
        import re
        
        # إزالة الأحرف غير المسموحة
        safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # إضافة timestamp لضمان التفرد
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name, ext = os.path.splitext(safe_filename)
        
        return f"{name}_{timestamp}{ext}"

# إعدادات البيئة
class Settings:
    """إعدادات التطبيق"""
    
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./database/instagram_accounts.db")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # إعدادات Instagram
    INSTAGRAM_LOGIN_TIMEOUT: int = int(os.getenv("INSTAGRAM_LOGIN_TIMEOUT", "30"))
    INSTAGRAM_EXTRACTION_DELAY: float = float(os.getenv("INSTAGRAM_EXTRACTION_DELAY", "2.0"))
    MAX_EXTRACTION_USERS: int = int(os.getenv("MAX_EXTRACTION_USERS", "1000"))
    
    # إعدادات الملفات
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    EXPORT_DIR: str = os.getenv("EXPORT_DIR", "./exports")
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "********"))  # 10MB
    
    # إعدادات CORS
    ALLOWED_ORIGINS: list = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:1421").split(",")
    
    def __init__(self):
        # إنشاء المجلدات المطلوبة
        os.makedirs(self.UPLOAD_DIR, exist_ok=True)
        os.makedirs(self.EXPORT_DIR, exist_ok=True)

# إنشاء مثيل الإعدادات
settings = Settings()

# دالة التحقق من الأذونات
def check_permissions(required_permission: str):
    """ديكوريتر للتحقق من الأذونات"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # هنا يمكن إضافة منطق التحقق من الأذونات
            # حالياً نسمح بجميع العمليات
            return func(*args, **kwargs)
        return wrapper
    return decorator

# دالة تسجيل العمليات الأمنية
def log_security_event(event_type: str, details: dict):
    """تسجيل الأحداث الأمنية"""
    timestamp = datetime.now().isoformat()
    log_entry = {
        "timestamp": timestamp,
        "event_type": event_type,
        "details": details
    }
    
    # يمكن إضافة تسجيل في ملف أو قاعدة بيانات
    print(f"🔒 Security Event: {log_entry}")

if __name__ == "__main__":
    # اختبار وظائف الأمان
    password = "test123"
    hashed = hash_password(password)
    print(f"Original: {password}")
    print(f"Hashed: {hashed}")
    print(f"Verified: {verify_password(password, hashed)}")
    
    # اختبار الرمز المميز
    token = create_access_token({"sub": "testuser"})
    print(f"Token: {token}")
    print(f"Decoded: {verify_token(token)}")
