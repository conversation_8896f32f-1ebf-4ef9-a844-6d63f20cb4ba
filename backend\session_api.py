#!/usr/bin/env python3
"""
API لإدارة جلسات Instagram - الربط التلقائي والكشف الذكي
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import threading
import time
from datetime import datetime
from instagram_session_manager import InstagramSessionManager, detect_and_save_instagram_sessions

app = Flask(__name__)
CORS(app)

# مدير الجلسات العام
session_manager = InstagramSessionManager()

# متغيرات لتتبع العمليات
active_detections = {}
active_browsers = {}

@app.route('/api/sessions/detect', methods=['POST'])
def detect_sessions():
    """كشف جلسات Instagram المفتوحة"""
    try:
        detection_id = f"detection_{int(time.time())}"
        
        # بدء عملية الكشف في خيط منفصل
        def run_detection():
            try:
                active_detections[detection_id] = {
                    'status': 'running',
                    'progress': 0,
                    'message': 'بدء البحث عن جلسات مفتوحة...',
                    'start_time': datetime.now().isoformat()
                }
                
                # تحديث التقدم
                active_detections[detection_id]['progress'] = 25
                active_detections[detection_id]['message'] = 'فحص متصفح Chrome...'
                
                sessions = session_manager.detect_open_instagram_sessions()
                
                active_detections[detection_id]['progress'] = 75
                active_detections[detection_id]['message'] = 'حفظ الجلسات المكتشفة...'
                
                saved_sessions = []
                for session in sessions:
                    account_id = session_manager.save_session_to_database(session)
                    if account_id:
                        saved_sessions.append({
                            'account_id': account_id,
                            'username': session['username'],
                            'browser': session.get('browser', 'Unknown'),
                            'full_name': session.get('full_name'),
                            'is_verified': session.get('is_verified', False),
                            'follower_count': session.get('follower_count', 0),
                            'profile_pic_url': session.get('profile_pic_url')
                        })
                
                active_detections[detection_id] = {
                    'status': 'completed',
                    'progress': 100,
                    'message': f'تم العثور على {len(saved_sessions)} جلسة وحفظها',
                    'sessions': saved_sessions,
                    'end_time': datetime.now().isoformat()
                }
                
            except Exception as e:
                active_detections[detection_id] = {
                    'status': 'failed',
                    'progress': 0,
                    'message': f'خطأ في الكشف: {str(e)}',
                    'error': str(e),
                    'end_time': datetime.now().isoformat()
                }
        
        thread = threading.Thread(target=run_detection)
        thread.start()
        
        return jsonify({
            'success': True,
            'detection_id': detection_id,
            'message': 'تم بدء عملية الكشف'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في بدء الكشف: {str(e)}'
        }), 500

@app.route('/api/sessions/detect/<detection_id>/status', methods=['GET'])
def get_detection_status(detection_id):
    """الحصول على حالة عملية الكشف"""
    if detection_id in active_detections:
        return jsonify({
            'success': True,
            'detection': active_detections[detection_id]
        })
    else:
        return jsonify({
            'success': False,
            'message': 'عملية الكشف غير موجودة'
        }), 404

@app.route('/api/sessions/active', methods=['GET'])
def get_active_sessions():
    """الحصول على الجلسات النشطة"""
    try:
        sessions = session_manager.get_active_sessions()
        return jsonify({
            'success': True,
            'sessions': sessions,
            'count': len(sessions)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب الجلسات: {str(e)}'
        }), 500

@app.route('/api/sessions/connect', methods=['POST'])
def connect_to_session():
    """الاتصال بجلسة محفوظة"""
    try:
        data = request.get_json()
        username = data.get('username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': 'اسم المستخدم مطلوب'
            }), 400
        
        browser_id = f"browser_{username}_{int(time.time())}"
        
        # بدء الاتصال في خيط منفصل
        def connect_browser():
            try:
                active_browsers[browser_id] = {
                    'status': 'connecting',
                    'username': username,
                    'message': 'جاري الاتصال بالجلسة...',
                    'start_time': datetime.now().isoformat()
                }
                
                driver = session_manager.create_browser_with_session(username)
                
                if driver:
                    active_browsers[browser_id] = {
                        'status': 'connected',
                        'username': username,
                        'message': 'تم الاتصال بنجاح',
                        'driver_available': True,
                        'current_url': driver.current_url,
                        'connected_at': datetime.now().isoformat()
                    }
                else:
                    active_browsers[browser_id] = {
                        'status': 'failed',
                        'username': username,
                        'message': 'فشل في الاتصال بالجلسة',
                        'error': 'لا يمكن إنشاء المتصفح',
                        'failed_at': datetime.now().isoformat()
                    }
                    
            except Exception as e:
                active_browsers[browser_id] = {
                    'status': 'failed',
                    'username': username,
                    'message': f'خطأ في الاتصال: {str(e)}',
                    'error': str(e),
                    'failed_at': datetime.now().isoformat()
                }
        
        thread = threading.Thread(target=connect_browser)
        thread.start()
        
        return jsonify({
            'success': True,
            'browser_id': browser_id,
            'message': 'تم بدء عملية الاتصال'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في بدء الاتصال: {str(e)}'
        }), 500

@app.route('/api/sessions/browser/<browser_id>/status', methods=['GET'])
def get_browser_status(browser_id):
    """الحصول على حالة المتصفح"""
    if browser_id in active_browsers:
        return jsonify({
            'success': True,
            'browser': active_browsers[browser_id]
        })
    else:
        return jsonify({
            'success': False,
            'message': 'المتصفح غير موجود'
        }), 404

@app.route('/api/sessions/browser/<browser_id>/navigate', methods=['POST'])
def navigate_browser(browser_id):
    """التنقل في المتصفح"""
    try:
        if browser_id not in active_browsers:
            return jsonify({
                'success': False,
                'message': 'المتصفح غير موجود'
            }), 404
        
        browser_info = active_browsers[browser_id]
        if browser_info['status'] != 'connected':
            return jsonify({
                'success': False,
                'message': 'المتصفح غير متصل'
            }), 400
        
        data = request.get_json()
        url = data.get('url', 'https://www.instagram.com/')
        
        # هنا يمكن إضافة منطق التنقل
        # driver.get(url) - إذا كان لدينا مرجع للـ driver
        
        active_browsers[browser_id]['current_url'] = url
        active_browsers[browser_id]['last_navigation'] = datetime.now().isoformat()
        
        return jsonify({
            'success': True,
            'message': f'تم التنقل إلى {url}',
            'current_url': url
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في التنقل: {str(e)}'
        }), 500

@app.route('/api/sessions/browser/<browser_id>/extract', methods=['POST'])
def extract_from_browser(browser_id):
    """استخراج البيانات من المتصفح"""
    try:
        if browser_id not in active_browsers:
            return jsonify({
                'success': False,
                'message': 'المتصفح غير موجود'
            }), 404
        
        browser_info = active_browsers[browser_id]
        if browser_info['status'] != 'connected':
            return jsonify({
                'success': False,
                'message': 'المتصفح غير متصل'
            }), 400
        
        data = request.get_json()
        post_url = data.get('post_url')
        extraction_type = data.get('extraction_type', 'likes')
        
        if not post_url:
            return jsonify({
                'success': False,
                'message': 'رابط المنشور مطلوب'
            }), 400
        
        # بدء عملية الاستخراج
        extraction_id = f"extraction_{browser_id}_{int(time.time())}"
        
        def run_extraction():
            try:
                # هنا يمكن إضافة منطق الاستخراج الفعلي
                # باستخدام الـ driver المتصل
                
                # محاكاة عملية الاستخراج
                time.sleep(2)
                
                # نتائج تجريبية
                extracted_users = [
                    {
                        'username': f'user_{i}',
                        'full_name': f'مستخدم {i}',
                        'is_verified': i % 5 == 0,
                        'follower_count': 1000 + (i * 100),
                        'profile_pic_url': f'https://example.com/pic_{i}.jpg'
                    }
                    for i in range(1, 51)  # 50 مستخدم تجريبي
                ]
                
                return {
                    'success': True,
                    'extraction_id': extraction_id,
                    'post_url': post_url,
                    'extraction_type': extraction_type,
                    'extracted_count': len(extracted_users),
                    'users': extracted_users,
                    'extracted_at': datetime.now().isoformat()
                }
                
            except Exception as e:
                return {
                    'success': False,
                    'extraction_id': extraction_id,
                    'error': str(e),
                    'failed_at': datetime.now().isoformat()
                }
        
        # تشغيل الاستخراج
        result = run_extraction()
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاستخراج: {str(e)}'
        }), 500

@app.route('/api/sessions/cleanup', methods=['POST'])
def cleanup_sessions():
    """تنظيف الجلسات المنتهية الصلاحية"""
    try:
        data = request.get_json()
        days = data.get('days', 7)
        
        cleaned_count = session_manager.cleanup_expired_sessions(days)
        
        return jsonify({
            'success': True,
            'message': f'تم تنظيف {cleaned_count} جلسة',
            'cleaned_count': cleaned_count
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في التنظيف: {str(e)}'
        }), 500

@app.route('/api/sessions/info', methods=['GET'])
def get_sessions_info():
    """الحصول على معلومات عامة عن الجلسات"""
    try:
        active_sessions = session_manager.get_active_sessions()
        
        info = {
            'total_active_sessions': len(active_sessions),
            'active_detections': len(active_detections),
            'active_browsers': len(active_browsers),
            'sessions': active_sessions,
            'recent_detections': list(active_detections.keys())[-5:],  # آخر 5
            'recent_browsers': list(active_browsers.keys())[-5:]  # آخر 5
        }
        
        return jsonify({
            'success': True,
            'info': info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المعلومات: {str(e)}'
        }), 500

@app.route('/api/sessions/test', methods=['GET'])
def test_session_api():
    """اختبار API الجلسات"""
    return jsonify({
        'success': True,
        'message': 'Session API يعمل بنجاح',
        'timestamp': datetime.now().isoformat(),
        'endpoints': [
            'POST /api/sessions/detect - كشف الجلسات',
            'GET /api/sessions/active - الجلسات النشطة',
            'POST /api/sessions/connect - الاتصال بجلسة',
            'POST /api/sessions/cleanup - تنظيف الجلسات',
            'GET /api/sessions/info - معلومات الجلسات'
        ]
    })

if __name__ == '__main__':
    print("🚀 بدء تشغيل Session API...")
    print("🔍 كشف الجلسات: POST /api/sessions/detect")
    print("📱 الجلسات النشطة: GET /api/sessions/active")
    print("🔗 الاتصال بجلسة: POST /api/sessions/connect")
    print("🧹 تنظيف الجلسات: POST /api/sessions/cleanup")
    print("ℹ️ معلومات الجلسات: GET /api/sessions/info")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=8001, debug=True)
