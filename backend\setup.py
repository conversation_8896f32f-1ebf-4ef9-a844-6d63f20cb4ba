#!/usr/bin/env python3
"""
Setup script for Instagram Likes Extractor
سكريبت إعداد تطبيق استخراج معجبي إنستجرام
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل: {e}")
        print(f"خطأ: {e.stderr}")
        return False

def check_python():
    """التحقق من وجود Python"""
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} متوفر")
            return True
        else:
            print(f"❌ Python 3.8+ مطلوب. الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
            return False
    except:
        print("❌ Python غير مثبت")
        return False

def check_node():
    """التحقق من وجود Node.js"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} متوفر")
            return True
        else:
            print("❌ Node.js غير مثبت")
            return False
    except:
        print("❌ Node.js غير مثبت")
        return False

def check_rust():
    """التحقق من وجود Rust"""
    try:
        result = subprocess.run(['rustc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Rust {version} متوفر")
            return True
        else:
            print("❌ Rust غير مثبت")
            return False
    except:
        print("❌ Rust غير مثبت")
        return False

def install_python_deps():
    """تثبيت تبعيات Python"""
    return run_command("pip install -r requirements.txt", "تثبيت تبعيات Python")

def install_node_deps():
    """تثبيت تبعيات Node.js"""
    return run_command("npm install", "تثبيت تبعيات Node.js")

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['exports', 'logs']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد: {directory}")

def main():
    print("🚀 بدء إعداد Instagram Likes Extractor")
    print("=" * 50)
    
    # التحقق من المتطلبات
    print("\n📋 التحقق من المتطلبات...")
    
    python_ok = check_python()
    node_ok = check_node()
    rust_ok = check_rust()
    
    if not python_ok:
        print("\n❌ يرجى تثبيت Python 3.8+ أولاً")
        print("تحميل من: https://www.python.org/downloads/")
        return False
    
    if not node_ok:
        print("\n❌ يرجى تثبيت Node.js أولاً")
        print("تحميل من: https://nodejs.org/")
        return False
    
    if not rust_ok:
        print("\n❌ يرجى تثبيت Rust أولاً")
        if platform.system() == "Windows":
            print("تشغيل: winget install Rustlang.Rust")
        else:
            print("تشغيل: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh")
        return False
    
    print("\n✅ جميع المتطلبات متوفرة!")
    
    # إنشاء المجلدات
    print("\n📁 إنشاء المجلدات...")
    create_directories()
    
    # تثبيت التبعيات
    print("\n📦 تثبيت التبعيات...")
    
    if not install_python_deps():
        print("❌ فشل في تثبيت تبعيات Python")
        return False
    
    if not install_node_deps():
        print("❌ فشل في تثبيت تبعيات Node.js")
        return False
    
    print("\n🎉 تم الإعداد بنجاح!")
    print("\n📖 للتشغيل:")
    print("   npm run tauri dev")
    print("\n📦 للبناء:")
    print("   npm run tauri build")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
