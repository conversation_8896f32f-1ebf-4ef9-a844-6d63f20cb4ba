#!/usr/bin/env python3
"""
Backend مبسط لتشغيل النظام الكامل
"""

import json
import sqlite3
import os
from datetime import datetime
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import uvicorn

# إنشاء التطبيق
app = FastAPI(
    title="Instagram Likes Extractor API",
    description="Backend API للنظام الكامل",
    version="2.0.0"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إنشاء مجلد قاعدة البيانات
os.makedirs("../database", exist_ok=True)
DB_PATH = "../database/instagram_accounts.db"

# نماذج البيانات
class InstagramAccountCreate(BaseModel):
    name: str
    username: str
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True
    notes: Optional[str] = None

class InstagramAccount(BaseModel):
    id: int
    name: str
    username: str
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    last_used: Optional[str] = None
    created_at: str
    updated_at: str
    notes: Optional[str] = None

class SystemStats(BaseModel):
    total_accounts: int
    active_accounts: int
    total_jobs: int
    total_extracted_users: int
    jobs_today: int

# إنشاء قاعدة البيانات
def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # جدول الحسابات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS instagram_accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            username TEXT UNIQUE NOT NULL,
            email TEXT,
            phone TEXT,
            is_active BOOLEAN DEFAULT 1,
            is_verified BOOLEAN DEFAULT 0,
            cookies_file TEXT,
            last_used DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    # جدول مهام الاستخراج
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS extraction_jobs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_id INTEGER,
            post_url TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            total_likes INTEGER DEFAULT 0,
            extracted_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (account_id) REFERENCES instagram_accounts (id)
        )
    ''')
    
    # جدول المستخدمين المستخرجين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS extracted_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            job_id INTEGER,
            username TEXT NOT NULL,
            profile_url TEXT,
            extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (job_id) REFERENCES extraction_jobs (id)
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ تم إنشاء قاعدة البيانات بنجاح")

# تشغيل إنشاء قاعدة البيانات عند البدء
init_database()

# ==================== API Endpoints ====================

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "Instagram Likes Extractor API v2.0 - النظام الكامل",
        "status": "running",
        "backend_connected": True
    }

@app.get("/api/accounts", response_model=List[InstagramAccount])
async def get_accounts():
    """الحصول على جميع الحسابات"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, name, username, email, phone, is_active, is_verified, 
               last_used, created_at, updated_at, notes
        FROM instagram_accounts
        ORDER BY created_at DESC
    ''')
    
    accounts = []
    for row in cursor.fetchall():
        accounts.append(InstagramAccount(
            id=row[0],
            name=row[1],
            username=row[2],
            email=row[3],
            phone=row[4],
            is_active=bool(row[5]),
            is_verified=bool(row[6]),
            last_used=row[7],
            created_at=row[8],
            updated_at=row[9],
            notes=row[10]
        ))
    
    conn.close()
    return accounts

@app.post("/api/accounts", response_model=InstagramAccount)
async def create_account(account: InstagramAccountCreate):
    """إنشاء حساب جديد"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO instagram_accounts (name, username, email, phone, is_active, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (account.name, account.username, account.email, account.phone, account.is_active, account.notes))
        
        account_id = cursor.lastrowid
        conn.commit()
        
        # إرجاع الحساب المُنشأ
        cursor.execute('''
            SELECT id, name, username, email, phone, is_active, is_verified, 
                   last_used, created_at, updated_at, notes
            FROM instagram_accounts WHERE id = ?
        ''', (account_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        return InstagramAccount(
            id=row[0],
            name=row[1],
            username=row[2],
            email=row[3],
            phone=row[4],
            is_active=bool(row[5]),
            is_verified=bool(row[6]),
            last_used=row[7],
            created_at=row[8],
            updated_at=row[9],
            notes=row[10]
        )
        
    except sqlite3.IntegrityError:
        conn.close()
        raise HTTPException(status_code=400, detail="اسم المستخدم موجود بالفعل")

@app.put("/api/accounts/{account_id}", response_model=InstagramAccount)
async def update_account(account_id: int, account: InstagramAccountCreate):
    """تحديث حساب موجود"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        UPDATE instagram_accounts 
        SET name = ?, username = ?, email = ?, phone = ?, is_active = ?, notes = ?, updated_at = ?
        WHERE id = ?
    ''', (account.name, account.username, account.email, account.phone, 
          account.is_active, account.notes, datetime.now().isoformat(), account_id))
    
    if cursor.rowcount == 0:
        conn.close()
        raise HTTPException(status_code=404, detail="الحساب غير موجود")
    
    conn.commit()
    
    # إرجاع الحساب المُحدث
    cursor.execute('''
        SELECT id, name, username, email, phone, is_active, is_verified, 
               last_used, created_at, updated_at, notes
        FROM instagram_accounts WHERE id = ?
    ''', (account_id,))
    
    row = cursor.fetchone()
    conn.close()
    
    return InstagramAccount(
        id=row[0],
        name=row[1],
        username=row[2],
        email=row[3],
        phone=row[4],
        is_active=bool(row[5]),
        is_verified=bool(row[6]),
        last_used=row[7],
        created_at=row[8],
        updated_at=row[9],
        notes=row[10]
    )

@app.delete("/api/accounts/{account_id}")
async def delete_account(account_id: int):
    """حذف حساب"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('DELETE FROM instagram_accounts WHERE id = ?', (account_id,))
    
    if cursor.rowcount == 0:
        conn.close()
        raise HTTPException(status_code=404, detail="الحساب غير موجود")
    
    conn.commit()
    conn.close()
    
    return {"message": "تم حذف الحساب بنجاح"}

@app.get("/api/stats/system", response_model=SystemStats)
async def get_system_stats():
    """الحصول على إحصائيات النظام"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # عدد الحسابات الإجمالي
    cursor.execute('SELECT COUNT(*) FROM instagram_accounts')
    total_accounts = cursor.fetchone()[0]
    
    # عدد الحسابات النشطة
    cursor.execute('SELECT COUNT(*) FROM instagram_accounts WHERE is_active = 1')
    active_accounts = cursor.fetchone()[0]
    
    # عدد المهام الإجمالي
    cursor.execute('SELECT COUNT(*) FROM extraction_jobs')
    total_jobs = cursor.fetchone()[0]
    
    # عدد المستخدمين المستخرجين
    cursor.execute('SELECT COUNT(*) FROM extracted_users')
    total_extracted_users = cursor.fetchone()[0]
    
    # مهام اليوم
    cursor.execute('''
        SELECT COUNT(*) FROM extraction_jobs 
        WHERE DATE(created_at) = DATE('now')
    ''')
    jobs_today = cursor.fetchone()[0]
    
    conn.close()
    
    return SystemStats(
        total_accounts=total_accounts,
        active_accounts=active_accounts,
        total_jobs=total_jobs,
        total_extracted_users=total_extracted_users,
        jobs_today=jobs_today
    )

# ==================== الأتمتة ====================

@app.post("/api/automation/start-login")
async def start_automated_login():
    """بدء عملية تسجيل الدخول التلقائي"""
    try:
        # محاكاة بدء الأتمتة
        return {
            "success": True,
            "message": "تم بدء الأتمتة - يرجى تثبيت selenium لتفعيل الميزة الكاملة",
            "status": "simulation_mode"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"خطأ في بدء الأتمتة: {str(e)}",
            "error": "automation_error"
        }

@app.post("/api/automation/complete-workflow")
async def complete_automation_workflow():
    """إكمال سير العمل التلقائي"""
    try:
        # محاكاة إكمال سير العمل
        return {
            "success": True,
            "message": "تم إكمال سير العمل - وضع المحاكاة",
            "status": "simulation_completed"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"خطأ في سير العمل: {str(e)}",
            "error": "workflow_error"
        }

@app.get("/api/automation/status")
async def get_automation_status():
    """الحصول على حالة الأتمتة"""
    return {
        "automation_active": False,
        "browser_active": False,
        "extraction_overlay_injected": False,
        "mode": "simulation"
    }

if __name__ == "__main__":
    print("🚀 بدء تشغيل Instagram Likes Extractor Backend")
    print("=" * 60)
    print("✅ Backend API: http://localhost:8000")
    print("📊 API Docs: http://localhost:8000/docs")
    print("🔧 Admin: http://localhost:8000/redoc")
    print("=" * 60)
    
    uvicorn.run(
        "simple_backend:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
