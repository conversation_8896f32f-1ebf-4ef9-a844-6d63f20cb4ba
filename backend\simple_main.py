"""
Instagram Likes Extractor - Simple Backend API
نسخة مبسطة بدون SQLAlchemy لحل مشكلة greenlet
"""

from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
from datetime import datetime
import uvicorn
import json
import hashlib

# قاعدة بيانات مؤقتة في الذاكرة
accounts_db = []
account_id_counter = 1

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Instagram Likes Extractor API",
    description="API لاستخراج قائمة المعجبين من منشورات Instagram",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# نماذج البيانات البسيطة
class MessageResponse:
    def __init__(self, message: str, success: bool = True):
        self.message = message
        self.success = success

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_account_dict(account_data: dict, account_id: int = None) -> dict:
    """إنشاء قاموس حساب"""
    if account_id is None:
        global account_id_counter
        account_id = account_id_counter
        account_id_counter += 1
    
    return {
        "id": account_id,
        "name": account_data.get("name", ""),
        "username": account_data.get("username", ""),
        "email": account_data.get("email"),
        "phone": account_data.get("phone"),
        "is_active": account_data.get("is_active", True),
        "is_verified": account_data.get("is_verified", False),
        "notes": account_data.get("notes", ""),
        "profile_pic_url": account_data.get("profile_pic_url"),
        "follower_count": account_data.get("follower_count", 0),
        "following_count": account_data.get("following_count", 0),
        "has_2fa": account_data.get("has_2fa", False),
        "is_locked": account_data.get("is_locked", False),
        "login_attempts": account_data.get("login_attempts", 0),
        "last_used": datetime.now().isoformat(),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

# ==================== API Routes ====================

@app.get("/")
async def root():
    """الصفحة الرئيسية للـ API"""
    return {
        "message": "Instagram Likes Extractor API v2.0 - Backend منفصل",
        "success": True
    }

@app.get("/api/accounts")
async def get_accounts(skip: int = 0, limit: int = 100, active_only: bool = False):
    """الحصول على قائمة حسابات Instagram"""
    
    filtered_accounts = accounts_db
    
    if active_only:
        filtered_accounts = [acc for acc in accounts_db if acc.get("is_active", True)]
    
    # تطبيق التصفح
    start = skip
    end = skip + limit
    result = filtered_accounts[start:end]
    
    return result

@app.get("/api/accounts/{account_id}")
async def get_account(account_id: int):
    """الحصول على حساب Instagram محدد"""
    
    account = next((acc for acc in accounts_db if acc["id"] == account_id), None)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )
    
    return account

@app.post("/api/accounts/auto-detected")
async def create_auto_detected_account(account_data: dict):
    """إضافة حساب تم اكتشافه تلقائياً"""
    
    try:
        # استخراج البيانات من الطلب
        username = account_data.get('username', '').strip()
        name = account_data.get('name', username)
        
        if not username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="اسم المستخدم مطلوب"
            )
        
        # التحقق من عدم وجود الحساب مسبقاً
        existing_account = next((acc for acc in accounts_db if acc["username"] == username), None)
        
        if existing_account:
            # إذا كان الحساب موجود، نحدث آخر استخدام ونعيده
            existing_account["last_used"] = datetime.now().isoformat()
            existing_account["notes"] = f"تم اكتشافه مرة أخرى في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            existing_account["updated_at"] = datetime.now().isoformat()
            return existing_account
        
        # إنشاء حساب جديد للحساب المكتشف تلقائياً
        new_account_data = {
            "name": name,
            "username": username,
            "email": account_data.get('email'),
            "phone": account_data.get('phone'),
            "is_verified": account_data.get('is_verified', False),
            "is_active": True,
            "notes": account_data.get('notes', f"تم اكتشافه تلقائياً في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
            "profile_pic_url": account_data.get('profile_pic_url'),
            "follower_count": account_data.get('follower_count', 0),
            "following_count": account_data.get('following_count', 0)
        }
        
        new_account = create_account_dict(new_account_data)
        accounts_db.append(new_account)
        
        return new_account
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إضافة الحساب المكتشف: {str(e)}"
        )

@app.post("/api/accounts")
async def create_account(account_data: dict):
    """إنشاء حساب Instagram جديد"""
    
    username = account_data.get('username', '').strip()
    password = account_data.get('password', '')
    name = account_data.get('name', username)
    
    if not username or not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم المستخدم وكلمة المرور مطلوبان"
        )
    
    # التحقق من عدم وجود الحساب مسبقاً
    existing_account = next((acc for acc in accounts_db if acc["username"] == username), None)
    
    if existing_account:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم المستخدم موجود مسبقاً"
        )
    
    # إنشاء الحساب الجديد
    new_account_data = {
        "name": name,
        "username": username,
        "password_hash": hash_password(password),
        "email": account_data.get('email'),
        "phone": account_data.get('phone'),
        "is_active": account_data.get('is_active', True),
        "notes": account_data.get('notes', '')
    }
    
    new_account = create_account_dict(new_account_data)
    accounts_db.append(new_account)
    
    return new_account

@app.delete("/api/accounts/{account_id}")
async def delete_account(account_id: int):
    """حذف حساب Instagram"""
    
    account_index = next((i for i, acc in enumerate(accounts_db) if acc["id"] == account_id), None)
    
    if account_index is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )
    
    accounts_db.pop(account_index)
    
    return {"message": "تم حذف الحساب بنجاح", "success": True}

@app.get("/api/stats/system")
async def get_system_stats():
    """الحصول على إحصائيات النظام"""
    
    total_accounts = len(accounts_db)
    active_accounts = len([acc for acc in accounts_db if acc.get("is_active", True)])
    accounts_with_2fa = len([acc for acc in accounts_db if acc.get("has_2fa", False)])
    verified_accounts = len([acc for acc in accounts_db if acc.get("is_verified", False)])
    
    return {
        "total_accounts": total_accounts,
        "active_accounts": active_accounts,
        "accounts_with_2fa": accounts_with_2fa,
        "verified_accounts": verified_accounts,
        "total_jobs": 0,
        "total_extracted_users": 0,
        "jobs_today": 0
    }

# ==================== 2FA Endpoints (مبسطة) ====================

@app.post("/api/accounts/{account_id}/setup-2fa")
async def setup_2fa_for_account(account_id: int):
    """إعداد المصادقة الثنائية لحساب (مبسط)"""
    
    account = next((acc for acc in accounts_db if acc["id"] == account_id), None)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )
    
    if account.get("has_2fa", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المصادقة الثنائية مفعلة مسبقاً"
        )
    
    # محاكاة إعداد 2FA
    import secrets
    secret = secrets.token_hex(16)
    backup_codes = [f"{secrets.randbelow(*********):08d}" for _ in range(10)]
    
    account["has_2fa"] = False  # سيتم تفعيلها بعد التحقق
    account["two_fa_secret"] = secret
    account["two_fa_backup_codes"] = backup_codes
    account["two_fa_method"] = "app"
    account["updated_at"] = datetime.now().isoformat()
    
    return {
        "success": True,
        "secret": secret,
        "qr_code": f"otpauth://totp/Instagram:{account['username']}?secret={secret}&issuer=Instagram",
        "backup_codes": backup_codes
    }

@app.post("/api/accounts/{account_id}/verify-2fa-setup")
async def verify_2fa_setup(account_id: int, verification: dict):
    """التحقق من إعداد 2FA وتفعيله (مبسط)"""
    
    account = next((acc for acc in accounts_db if acc["id"] == account_id), None)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحساب غير موجود"
        )
    
    code = verification.get('code')
    if not code or len(code) != 6:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رمز التحقق غير صحيح"
        )
    
    # محاكاة التحقق (قبول أي رمز من 6 أرقام)
    account["has_2fa"] = True
    account["updated_at"] = datetime.now().isoformat()
    
    return {
        "success": True,
        "message": "تم تفعيل المصادقة الثنائية بنجاح"
    }

# ==================== تشغيل الخادم ====================

if __name__ == "__main__":
    print("🚀 بدء تشغيل Instagram Extractor Backend (Simple)")
    print("📊 API: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/api/docs")
    print("🔍 Auto-Detected Endpoint: http://localhost:8000/api/accounts/auto-detected")
    print("=" * 60)
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
