#!/usr/bin/env python3
"""
خادم بسيط لاختبار الاتصال
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class SimpleHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/':
            response = {
                "message": "Instagram Likes Extractor API v2.0 - Backend منفصل",
                "success": True
            }
        elif self.path == '/api/accounts':
            response = []  # قائمة فارغة من الحسابات
        elif self.path == '/api/stats/system':
            response = {
                "total_accounts": 0,
                "active_accounts": 0,
                "total_jobs": 0,
                "total_extracted_users": 0,
                "jobs_today": 0
            }
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/api/accounts':
            # محاكاة إنشاء حساب جديد
            try:
                data = json.loads(post_data.decode('utf-8'))
                response = {
                    "id": 1,
                    "name": data.get("name", ""),
                    "username": data.get("username", ""),
                    "email": data.get("email"),
                    "phone": data.get("phone"),
                    "is_active": data.get("is_active", True),
                    "is_verified": False,
                    "last_used": None,
                    "created_at": "2024-06-01T12:00:00",
                    "updated_at": "2024-06-01T12:00:00",
                    "notes": data.get("notes")
                }
            except:
                response = {"error": "Invalid JSON", "success": False}
        else:
            response = {"error": "Not found", "success": False}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

def run_server():
    """تشغيل الخادم"""
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, SimpleHandler)
    
    print("=" * 60)
    print("🔧 Instagram Likes Extractor - Simple Backend Server")
    print("=" * 60)
    print("✅ Backend جاهز للعمل!")
    print("🌐 API Base: http://localhost:8000")
    print("📊 Test: http://localhost:8000/api/accounts")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
