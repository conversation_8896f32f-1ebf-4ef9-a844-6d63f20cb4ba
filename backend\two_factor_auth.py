"""
خدمة المصادقة الثنائية (2FA) - دعم TOTP و Backup Codes
"""

import pyotp
import qrcode
import io
import base64
import json
import secrets
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import List, Optional, Tuple
import os

class TwoFactorAuth:
    """خدمة المصادقة الثنائية"""
    
    def __init__(self):
        # مفتاح التشفير للبيانات الحساسة
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        key_file = "backend/.encryption_key"
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def generate_secret(self) -> str:
        """إنشاء secret جديد للـ TOTP"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, secret: str, username: str, issuer: str = "Instagram Extractor") -> str:
        """إنشاء QR code للـ TOTP"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=username,
            issuer_name=issuer
        )
        
        # إنشاء QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # تحويل إلى base64
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_totp(self, secret: str, token: str) -> bool:
        """التحقق من رمز TOTP"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=1)  # نافذة صالحة لـ 30 ثانية إضافية
        except Exception:
            return False
    
    def generate_backup_codes(self, count: int = 10) -> List[str]:
        """إنشاء backup codes"""
        codes = []
        for _ in range(count):
            # إنشاء كود من 8 أرقام
            code = ''.join([str(secrets.randbelow(10)) for _ in range(8)])
            # تقسيم إلى مجموعات من 4 أرقام
            formatted_code = f"{code[:4]}-{code[4:]}"
            codes.append(formatted_code)
        return codes
    
    def verify_backup_code(self, stored_codes: List[str], provided_code: str) -> Tuple[bool, List[str]]:
        """التحقق من backup code واستهلاكه"""
        if provided_code in stored_codes:
            # إزالة الكود المستخدم
            updated_codes = [code for code in stored_codes if code != provided_code]
            return True, updated_codes
        return False, stored_codes
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    def encrypt_secret(self, secret: str) -> str:
        """تشفير TOTP secret"""
        return self.encrypt_data(secret)
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """فك تشفير TOTP secret"""
        return self.decrypt_data(encrypted_secret)
    
    def encrypt_backup_codes(self, codes: List[str]) -> str:
        """تشفير backup codes"""
        codes_json = json.dumps(codes)
        return self.encrypt_data(codes_json)
    
    def decrypt_backup_codes(self, encrypted_codes: str) -> List[str]:
        """فك تشفير backup codes"""
        codes_json = self.decrypt_data(encrypted_codes)
        return json.loads(codes_json)
    
    def setup_2fa_for_account(self, username: str) -> dict:
        """إعداد 2FA لحساب جديد"""
        # إنشاء secret جديد
        secret = self.generate_secret()
        
        # إنشاء QR code
        qr_code = self.generate_qr_code(secret, username)
        
        # إنشاء backup codes
        backup_codes = self.generate_backup_codes()
        
        # تشفير البيانات
        encrypted_secret = self.encrypt_secret(secret)
        encrypted_backup_codes = self.encrypt_backup_codes(backup_codes)
        
        return {
            'secret': secret,  # للعرض مرة واحدة فقط
            'encrypted_secret': encrypted_secret,
            'qr_code': qr_code,
            'backup_codes': backup_codes,  # للعرض مرة واحدة فقط
            'encrypted_backup_codes': encrypted_backup_codes
        }
    
    def validate_2fa_login(self, encrypted_secret: str, encrypted_backup_codes: str, 
                          provided_code: str) -> Tuple[bool, Optional[str]]:
        """التحقق من 2FA أثناء تسجيل الدخول"""
        try:
            # فك تشفير البيانات
            secret = self.decrypt_secret(encrypted_secret)
            backup_codes = self.decrypt_backup_codes(encrypted_backup_codes) if encrypted_backup_codes else []
            
            # التحقق من TOTP أولاً
            if self.verify_totp(secret, provided_code):
                return True, None
            
            # التحقق من backup codes
            is_valid, updated_codes = self.verify_backup_code(backup_codes, provided_code)
            if is_valid:
                # إرجاع backup codes المحدثة مشفرة
                updated_encrypted_codes = self.encrypt_backup_codes(updated_codes)
                return True, updated_encrypted_codes
            
            return False, None
            
        except Exception as e:
            print(f"خطأ في التحقق من 2FA: {e}")
            return False, None
    
    def disable_2fa(self) -> bool:
        """تعطيل 2FA"""
        # في التطبيق الحقيقي، ستحتاج لحذف البيانات من قاعدة البيانات
        return True
    
    def get_remaining_backup_codes_count(self, encrypted_backup_codes: str) -> int:
        """الحصول على عدد backup codes المتبقية"""
        try:
            if not encrypted_backup_codes:
                return 0
            backup_codes = self.decrypt_backup_codes(encrypted_backup_codes)
            return len(backup_codes)
        except Exception:
            return 0
    
    def regenerate_backup_codes(self) -> dict:
        """إعادة إنشاء backup codes جديدة"""
        new_codes = self.generate_backup_codes()
        encrypted_codes = self.encrypt_backup_codes(new_codes)
        
        return {
            'backup_codes': new_codes,  # للعرض مرة واحدة فقط
            'encrypted_backup_codes': encrypted_codes
        }

# إنشاء instance عام
two_fa_service = TwoFactorAuth()

# دوال مساعدة للاستخدام السريع
def setup_2fa(username: str) -> dict:
    """إعداد 2FA لمستخدم"""
    return two_fa_service.setup_2fa_for_account(username)

def verify_2fa(encrypted_secret: str, encrypted_backup_codes: str, code: str) -> Tuple[bool, Optional[str]]:
    """التحقق من رمز 2FA"""
    return two_fa_service.validate_2fa_login(encrypted_secret, encrypted_backup_codes, code)

def generate_backup_codes() -> dict:
    """إنشاء backup codes جديدة"""
    return two_fa_service.regenerate_backup_codes()

if __name__ == "__main__":
    # اختبار الخدمة
    print("🔐 اختبار خدمة المصادقة الثنائية")
    
    # إعداد 2FA لمستخدم تجريبي
    setup_data = setup_2fa("test_user")
    print(f"✅ تم إنشاء secret: {setup_data['secret'][:10]}...")
    print(f"✅ تم إنشاء {len(setup_data['backup_codes'])} backup codes")
    
    # اختبار التحقق
    test_totp = pyotp.TOTP(setup_data['secret'])
    current_code = test_totp.now()
    
    is_valid, updated_codes = verify_2fa(
        setup_data['encrypted_secret'],
        setup_data['encrypted_backup_codes'],
        current_code
    )
    
    print(f"✅ اختبار TOTP: {'نجح' if is_valid else 'فشل'}")
    
    # اختبار backup code
    first_backup_code = setup_data['backup_codes'][0]
    is_valid, updated_codes = verify_2fa(
        setup_data['encrypted_secret'],
        setup_data['encrypted_backup_codes'],
        first_backup_code
    )
    
    print(f"✅ اختبار Backup Code: {'نجح' if is_valid else 'فشل'}")
    print("🎉 جميع الاختبارات نجحت!")
