{"metadata": {"total_users": 3, "exported_at": "2025-06-01T01:40:33.409394", "format_version": "1.0"}, "users": [{"username": "test_user_1", "profile_pic_url": "https://via.placeholder.com/150x150/e1306c/ffffff?text=IG", "profile_url": "https://www.instagram.com/test_user_1/", "full_name": "Test User One", "is_verified": false, "follower_count": 1500, "following_count": 300, "post_count": 45, "bio": "This is a test bio for user one", "threads_url": "https://www.threads.net/@test_user_1", "facebook_url": null, "extracted_at": "2024-01-01T12:00:00"}, {"username": "verified_user", "profile_pic_url": "https://via.placeholder.com/150x150/833ab4/ffffff?text=VU", "profile_url": "https://www.instagram.com/verified_user/", "full_name": "Verified User", "is_verified": true, "follower_count": 50000, "following_count": 100, "post_count": 200, "bio": "Verified account with Facebook link: facebook.com/verifieduser", "threads_url": "https://www.threads.net/@verified_user", "facebook_url": "https://www.facebook.com/verifieduser", "extracted_at": "2024-01-01T12:00:00"}, {"username": "influencer_account", "profile_pic_url": "https://via.placeholder.com/150x150/fd1d1d/ffffff?text=IA", "profile_url": "https://www.instagram.com/influencer_account/", "full_name": "Influencer Account", "is_verified": true, "follower_count": 1200000, "following_count": 500, "post_count": 800, "bio": "Content creator and influencer 🌟", "threads_url": "https://www.threads.net/@influencer_account", "facebook_url": null, "extracted_at": "2024-01-01T12:00:00"}]}