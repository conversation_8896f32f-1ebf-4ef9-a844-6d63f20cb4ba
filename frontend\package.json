{"name": "tauri-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3"}}