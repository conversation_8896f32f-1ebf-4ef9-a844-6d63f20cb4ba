import React, { useState, useEffect } from 'react';
import UrlInput from './components/UrlInput';
import LoadingScreen from './components/LoadingScreen';
import ResultsTable from './components/ResultsTable';
import ExportButtons from './components/ExportButtons';
import ProgressTracker from './components/ProgressTracker';
import Toast from './components/Toast';
import AccountsManager from './components/AccountsManager';
import DashboardView from './components/DashboardView';
import InstagramBrowser from './components/InstagramBrowser';
import SmartAccountManager from './components/SmartAccountManager';
import Sidebar from './components/layout/Sidebar';
import AppHeader from './components/layout/Header';
import ModernDashboard from './components/dashboard/ModernDashboard';
import { InstagramUser, ExtractionProgress } from './types';
import { apiService, InstagramAccount, ExtractedUser } from './services/api';

function App() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<InstagramUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [progress, setProgress] = useState<ExtractionProgress | null>(null);
  const [currentTask, setCurrentTask] = useState<string>('');
  const [extractionLogs, setExtractionLogs] = useState<string[]>([]);
  const [toast, setToast] = useState<{ message: string, type: 'success' | 'error' | 'warning' | 'info' } | null>(null);

  // حالة النظام الجديد
  const [currentView, setCurrentView] = useState<'dashboard' | 'accounts' | 'extraction' | 'browser' | 'smart'>('dashboard');
  const [selectedAccount, setSelectedAccount] = useState<InstagramAccount | null>(null);
  const [backendConnected, setBackendConnected] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([
    {
      id: '1',
      title: 'تم إضافة حساب جديد',
      message: 'تم إضافة حساب @business_account بنجاح',
      type: 'success',
      time: 'منذ 5 دقائق',
      read: false
    },
    {
      id: '2',
      title: 'اكتمال عملية استخراج',
      message: 'تم استخراج 245 معجب من المنشور',
      type: 'info',
      time: 'منذ 15 دقيقة',
      read: false
    }
  ]);

  // بيانات النظام
  const [accounts, setAccounts] = useState<InstagramAccount[]>([
    {
      id: 1,
      name: 'حساب العمل',
      username: 'business_account',
      email: '<EMAIL>',
      is_active: true,
      is_verified: false,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-06-01T14:20:00Z',
      notes: 'حساب العمل الرئيسي'
    },
    {
      id: 2,
      name: 'حساب شخصي',
      username: 'personal_account',
      email: '<EMAIL>',
      is_active: true,
      is_verified: true,
      created_at: '2024-02-20T09:15:00Z',
      updated_at: '2024-06-01T12:45:00Z',
      notes: 'حساب شخصي موثق'
    }
  ]);

  const [extractions, setExtractions] = useState<any[]>([
    {
      id: 1,
      post_url: 'https://instagram.com/p/ABC123',
      account: 'business_account',
      count: 245,
      timestamp: '2024-06-01T10:30:00Z'
    },
    {
      id: 2,
      post_url: 'https://instagram.com/p/DEF456',
      account: 'personal_account',
      count: 189,
      timestamp: '2024-06-01T14:20:00Z'
    },
    {
      id: 3,
      post_url: 'https://instagram.com/p/GHI789',
      account: 'business_account',
      count: 312,
      timestamp: '2024-05-31T16:45:00Z'
    }
  ]);

  // دالة إضافة لوج جديد
  const addLog = (message: string) => {
    setExtractionLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // دالة إظهار الإشعارات
  const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
    setToast({ message, type });
  };

  // فحص الاتصال بالـ Backend
  useEffect(() => {
    const checkBackendConnection = async () => {
      try {
        const connected = await apiService.testConnection();
        setBackendConnected(connected);
        if (connected) {
          showToast('تم الاتصال بالخادم بنجاح', 'success');
        } else {
          showToast('تعذر الاتصال بالخادم - سيتم استخدام وضع المحاكاة', 'warning');
        }
      } catch (error) {
        setBackendConnected(false);
        showToast('تعذر الاتصال بالخادم - سيتم استخدام وضع المحاكاة', 'warning');
      }
    };

    checkBackendConnection();
  }, []);

  // دالة محاكاة الاستخراج مع تحديثات مباشرة
  const simulateExtraction = async () => {
    const steps = [
      { task: 'فتح المتصفح...', duration: 2000, progress: 10 },
      { task: 'الانتقال إلى المنشور...', duration: 3000, progress: 20 },
      { task: 'تسجيل الدخول إلى إنستجرام...', duration: 4000, progress: 30 },
      { task: 'البحث عن زر المعجبين...', duration: 2000, progress: 40 },
      { task: 'فتح قائمة المعجبين...', duration: 3000, progress: 50 },
      { task: 'بدء استخراج المعجبين...', duration: 2000, progress: 60 },
      { task: 'تمرير القائمة واستخراج البيانات...', duration: 8000, progress: 85 },
      { task: 'تحسين بيانات المستخدمين...', duration: 3000, progress: 95 },
      { task: 'إنهاء الاستخراج...', duration: 1000, progress: 100 }
    ];

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      setCurrentTask(step.task);
      setProgress({
        current: i + 1,
        total: steps.length,
        status: step.task,
        percentage: step.progress
      });
      addLog(step.task);

      await new Promise(resolve => setTimeout(resolve, step.duration));
    }

    // إنشاء بيانات تجريبية
    const sampleUsers: InstagramUser[] = [
      {
        username: 'user_example_1',
        profile_pic_url: 'https://via.placeholder.com/150x150/e1306c/ffffff?text=U1',
        profile_url: 'https://www.instagram.com/user_example_1/',
        full_name: 'مستخدم تجريبي 1',
        is_verified: false,
        follower_count: 1250,
        following_count: 890,
        post_count: 45,
        bio: 'مستخدم تجريبي للاختبار 📸',
        threads_url: 'https://www.threads.net/@user_example_1',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      },
      {
        username: 'verified_user_2',
        profile_pic_url: 'https://via.placeholder.com/150x150/833ab4/ffffff?text=V2',
        profile_url: 'https://www.instagram.com/verified_user_2/',
        full_name: 'مستخدم موثق',
        is_verified: true,
        follower_count: 125000,
        following_count: 450,
        post_count: 320,
        bio: 'حساب موثق ⭐ | مؤثر رقمي',
        threads_url: 'https://www.threads.net/@verified_user_2',
        facebook_url: 'https://www.facebook.com/verified_user_2',
        extracted_at: new Date().toISOString()
      },
      {
        username: 'content_creator_3',
        profile_pic_url: 'https://via.placeholder.com/150x150/fd1d1d/ffffff?text=C3',
        profile_url: 'https://www.instagram.com/content_creator_3/',
        full_name: 'صانع محتوى',
        is_verified: false,
        follower_count: 25600,
        following_count: 1200,
        post_count: 156,
        bio: 'صانع محتوى | مصور فوتوغرافي 📷',
        threads_url: 'https://www.threads.net/@content_creator_3',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      }
    ];

    return sampleUsers;
  };

  const handleExtract = async () => {
    if (!url.trim()) {
      setError('يرجى إدخال رابط منشور إنستجرام صحيح');
      showToast('يرجى إدخال رابط منشور إنستجرام صحيح', 'warning');
      return;
    }

    if (!selectedAccount) {
      setError('يرجى اختيار حساب Instagram للاستخراج');
      showToast('يرجى اختيار حساب Instagram للاستخراج', 'warning');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults([]);
    setProgress(null);
    setCurrentTask('');
    setExtractionLogs([]);

    try {
      addLog('بدء عملية الاستخراج...');

      if (backendConnected) {
        // استخدام Backend API
        try {
          addLog(`استخدام الحساب: ${selectedAccount.name} (@${selectedAccount.username})`);
          const job = await apiService.startExtraction(selectedAccount.id, url);
          addLog(`تم إنشاء مهمة الاستخراج #${job.id}`);

          // محاكاة تتبع التقدم (في المستقبل سيكون WebSocket)
          const simulatedUsers = await simulateExtraction();

          // تحويل البيانات للتوافق مع النوع القديم
          const convertedUsers: InstagramUser[] = simulatedUsers.map(user => ({
            username: user.username,
            profile_pic_url: user.profile_pic_url || '',
            profile_url: user.profile_url || '',
            full_name: user.full_name || '',
            is_verified: user.is_verified,
            follower_count: user.follower_count || 0,
            following_count: user.following_count || 0,
            post_count: user.post_count || 0,
            bio: user.bio || '',
            threads_url: user.threads_url || '',
            facebook_url: user.facebook_url || null,
            extracted_at: user.extracted_at
          }));

          setResults(convertedUsers);
          addLog(`تم استخراج ${convertedUsers.length} مستخدم بنجاح!`);
          showToast(`تم استخراج ${convertedUsers.length} مستخدم بنجاح!`, 'success');
        } catch (apiError) {
          throw apiError;
        }
      } else {
        // استخدام وضع المحاكاة
        addLog('تعذر الاتصال بالخادم، جارٍ استخدام وضع المحاكاة...');
        showToast('جارٍ استخدام وضع المحاكاة للعرض التوضيحي', 'info');
        const simulatedUsers = await simulateExtraction();
        setResults(simulatedUsers);
        addLog(`تم استخراج ${simulatedUsers.length} مستخدم في وضع المحاكاة!`);
        showToast(`تم استخراج ${simulatedUsers.length} مستخدم في وضع المحاكاة!`, 'success');
      }

    } catch (err) {
      setError(err as string || 'حدث خطأ أثناء استخراج البيانات');
      addLog(`خطأ: ${err}`);
      showToast('حدث خطأ أثناء استخراج البيانات', 'error');
    } finally {
      setIsLoading(false);
      setCurrentTask('');
      setProgress(null);
    }
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  // دالة تبديل الشريط الجانبي
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // دالة تحديد الإشعارات كمقروءة
  const markNotificationRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gradient-to-br from-gray-900 to-gray-800' : 'bg-gradient-to-br from-gray-50 to-white'
      }`}>

      {/* الشريط الجانبي */}
      <Sidebar
        currentView={currentView}
        onViewChange={setCurrentView}
        backendConnected={backendConnected}
        accountsCount={accounts.length}
        extractionsCount={extractions.length}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={toggleSidebar}
      />

      {/* الهيدر */}
      <AppHeader
        darkMode={darkMode}
        onToggleDarkMode={toggleDarkMode}
        sidebarCollapsed={sidebarCollapsed}
        onToggleSidebar={toggleSidebar}
        notifications={notifications}
        onMarkNotificationRead={markNotificationRead}
      />

      {/* المحتوى الرئيسي */}
      <main className={`
        transition-all duration-300 ease-in-out pt-16
        ${sidebarCollapsed ? 'mr-20' : 'mr-80'}
      `}>
        <div className="container mx-auto px-6 py-8 max-w-7xl">

          {/* عرض المحتوى حسب التبويب المختار */}
          {currentView === 'dashboard' && (
            <ModernDashboard
              onNavigate={setCurrentView}
              backendConnected={backendConnected}
              accounts={accounts}
              extractions={extractions}
            />
          )}

          {currentView === 'accounts' && (
            <AccountsManager
              onAccountSelect={setSelectedAccount}
              selectedAccountId={selectedAccount?.id}
            />
          )}

          {currentView === 'browser' && (
            <InstagramBrowser
              selectedAccount={selectedAccount}
            />
          )}

          {currentView === 'smart' && (
            <SmartAccountManager />
          )}

          {currentView === 'extraction' && (
            <div className="max-w-4xl mx-auto space-y-8">
              <UrlInput
                url={url}
                onUrlChange={setUrl}
                onExtract={handleExtract}
                isLoading={isLoading}
                selectedAccount={selectedAccount}
              />

              {error && (
                <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg">
                  <p className="font-medium">خطأ:</p>
                  <p>{error}</p>
                </div>
              )}

              {isLoading && (
                <div className="space-y-6">
                  <LoadingScreen />
                  {progress && (
                    <ProgressTracker
                      progress={progress}
                      currentTask={currentTask}
                      logs={extractionLogs}
                    />
                  )}
                </div>
              )}

              {results.length > 0 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      النتائج ({results.length} حساب)
                    </h2>
                    <ExportButtons data={results} />
                  </div>
                  <ResultsTable users={results} />
                </div>
              )}
            </div>
          )}
        </div>

        {/* نظام الإشعارات */}
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </main>
    </div>
  );
}

export default App;
