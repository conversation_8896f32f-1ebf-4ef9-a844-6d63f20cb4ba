import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import UrlInput from './components/UrlInput';
import LoadingScreen from './components/LoadingScreen';
import ResultsTable from './components/ResultsTable';
import ExportButtons from './components/ExportButtons';
import ProgressTracker from './components/ProgressTracker';
import Toast from './components/Toast';
import AccountsManager from './components/AccountsManager';
import DashboardView from './components/DashboardView';
import InstagramBrowser from './components/InstagramBrowser';
import { InstagramUser, ExtractionProgress } from './types';
import { apiService, InstagramAccount, ExtractedUser } from './services/api';

function App() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<InstagramUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [progress, setProgress] = useState<ExtractionProgress | null>(null);
  const [currentTask, setCurrentTask] = useState<string>('');
  const [extractionLogs, setExtractionLogs] = useState<string[]>([]);
  const [toast, setToast] = useState<{ message: string, type: 'success' | 'error' | 'warning' | 'info' } | null>(null);

  // حالة النظام الجديد
  const [currentView, setCurrentView] = useState<'dashboard' | 'accounts' | 'extraction' | 'browser'>('dashboard');
  const [selectedAccount, setSelectedAccount] = useState<InstagramAccount | null>(null);
  const [backendConnected, setBackendConnected] = useState(false);

  // دالة إضافة لوج جديد
  const addLog = (message: string) => {
    setExtractionLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // دالة إظهار الإشعارات
  const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
    setToast({ message, type });
  };

  // فحص الاتصال بالـ Backend
  useEffect(() => {
    const checkBackendConnection = async () => {
      try {
        const connected = await apiService.testConnection();
        setBackendConnected(connected);
        if (connected) {
          showToast('تم الاتصال بالخادم بنجاح', 'success');
        } else {
          showToast('تعذر الاتصال بالخادم - سيتم استخدام وضع المحاكاة', 'warning');
        }
      } catch (error) {
        setBackendConnected(false);
        showToast('تعذر الاتصال بالخادم - سيتم استخدام وضع المحاكاة', 'warning');
      }
    };

    checkBackendConnection();
  }, []);

  // دالة محاكاة الاستخراج مع تحديثات مباشرة
  const simulateExtraction = async () => {
    const steps = [
      { task: 'فتح المتصفح...', duration: 2000, progress: 10 },
      { task: 'الانتقال إلى المنشور...', duration: 3000, progress: 20 },
      { task: 'تسجيل الدخول إلى إنستجرام...', duration: 4000, progress: 30 },
      { task: 'البحث عن زر المعجبين...', duration: 2000, progress: 40 },
      { task: 'فتح قائمة المعجبين...', duration: 3000, progress: 50 },
      { task: 'بدء استخراج المعجبين...', duration: 2000, progress: 60 },
      { task: 'تمرير القائمة واستخراج البيانات...', duration: 8000, progress: 85 },
      { task: 'تحسين بيانات المستخدمين...', duration: 3000, progress: 95 },
      { task: 'إنهاء الاستخراج...', duration: 1000, progress: 100 }
    ];

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      setCurrentTask(step.task);
      setProgress({
        current: i + 1,
        total: steps.length,
        status: step.task,
        percentage: step.progress
      });
      addLog(step.task);

      await new Promise(resolve => setTimeout(resolve, step.duration));
    }

    // إنشاء بيانات تجريبية
    const sampleUsers: InstagramUser[] = [
      {
        username: 'user_example_1',
        profile_pic_url: 'https://via.placeholder.com/150x150/e1306c/ffffff?text=U1',
        profile_url: 'https://www.instagram.com/user_example_1/',
        full_name: 'مستخدم تجريبي 1',
        is_verified: false,
        follower_count: 1250,
        following_count: 890,
        post_count: 45,
        bio: 'مستخدم تجريبي للاختبار 📸',
        threads_url: 'https://www.threads.net/@user_example_1',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      },
      {
        username: 'verified_user_2',
        profile_pic_url: 'https://via.placeholder.com/150x150/833ab4/ffffff?text=V2',
        profile_url: 'https://www.instagram.com/verified_user_2/',
        full_name: 'مستخدم موثق',
        is_verified: true,
        follower_count: 125000,
        following_count: 450,
        post_count: 320,
        bio: 'حساب موثق ⭐ | مؤثر رقمي',
        threads_url: 'https://www.threads.net/@verified_user_2',
        facebook_url: 'https://www.facebook.com/verified_user_2',
        extracted_at: new Date().toISOString()
      },
      {
        username: 'content_creator_3',
        profile_pic_url: 'https://via.placeholder.com/150x150/fd1d1d/ffffff?text=C3',
        profile_url: 'https://www.instagram.com/content_creator_3/',
        full_name: 'صانع محتوى',
        is_verified: false,
        follower_count: 25600,
        following_count: 1200,
        post_count: 156,
        bio: 'صانع محتوى | مصور فوتوغرافي 📷',
        threads_url: 'https://www.threads.net/@content_creator_3',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      }
    ];

    return sampleUsers;
  };

  const handleExtract = async () => {
    if (!url.trim()) {
      setError('يرجى إدخال رابط منشور إنستجرام صحيح');
      showToast('يرجى إدخال رابط منشور إنستجرام صحيح', 'warning');
      return;
    }

    if (!selectedAccount) {
      setError('يرجى اختيار حساب Instagram للاستخراج');
      showToast('يرجى اختيار حساب Instagram للاستخراج', 'warning');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults([]);
    setProgress(null);
    setCurrentTask('');
    setExtractionLogs([]);

    try {
      addLog('بدء عملية الاستخراج...');

      if (backendConnected) {
        // استخدام Backend API
        try {
          addLog(`استخدام الحساب: ${selectedAccount.name} (@${selectedAccount.username})`);
          const job = await apiService.startExtraction(selectedAccount.id, url);
          addLog(`تم إنشاء مهمة الاستخراج #${job.id}`);

          // محاكاة تتبع التقدم (في المستقبل سيكون WebSocket)
          const simulatedUsers = await simulateExtraction();

          // تحويل البيانات للتوافق مع النوع القديم
          const convertedUsers: InstagramUser[] = simulatedUsers.map(user => ({
            username: user.username,
            profile_pic_url: user.profile_pic_url || '',
            profile_url: user.profile_url || '',
            full_name: user.full_name || '',
            is_verified: user.is_verified,
            follower_count: user.follower_count || 0,
            following_count: user.following_count || 0,
            post_count: user.post_count || 0,
            bio: user.bio || '',
            threads_url: user.threads_url || '',
            facebook_url: user.facebook_url || null,
            extracted_at: user.extracted_at
          }));

          setResults(convertedUsers);
          addLog(`تم استخراج ${convertedUsers.length} مستخدم بنجاح!`);
          showToast(`تم استخراج ${convertedUsers.length} مستخدم بنجاح!`, 'success');
        } catch (apiError) {
          throw apiError;
        }
      } else {
        // استخدام وضع المحاكاة
        addLog('تعذر الاتصال بالخادم، جارٍ استخدام وضع المحاكاة...');
        showToast('جارٍ استخدام وضع المحاكاة للعرض التوضيحي', 'info');
        const simulatedUsers = await simulateExtraction();
        setResults(simulatedUsers);
        addLog(`تم استخراج ${simulatedUsers.length} مستخدم في وضع المحاكاة!`);
        showToast(`تم استخراج ${simulatedUsers.length} مستخدم في وضع المحاكاة!`, 'success');
      }

    } catch (err) {
      setError(err as string || 'حدث خطأ أثناء استخراج البيانات');
      addLog(`خطأ: ${err}`);
      showToast('حدث خطأ أثناء استخراج البيانات', 'error');
    } finally {
      setIsLoading(false);
      setCurrentTask('');
      setProgress(null);
    }
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'
      }`}>
      <div className="container mx-auto px-4 py-8">
        <Header darkMode={darkMode} onToggleDarkMode={toggleDarkMode} />

        {/* شريط التنقل */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4">
            <div className="flex justify-between items-center">
              <div className="flex space-x-4">
                <button
                  onClick={() => setCurrentView('dashboard')}
                  className={`px-4 py-2 rounded-lg transition-colors ${currentView === 'dashboard'
                    ? 'bg-pink-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                >
                  🏠 لوحة التحكم
                </button>
                <button
                  onClick={() => setCurrentView('accounts')}
                  className={`px-4 py-2 rounded-lg transition-colors ${currentView === 'accounts'
                    ? 'bg-pink-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                >
                  👥 إدارة الحسابات
                </button>
                <button
                  onClick={() => setCurrentView('extraction')}
                  className={`px-4 py-2 rounded-lg transition-colors ${currentView === 'extraction'
                    ? 'bg-pink-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                >
                  🔍 استخراج المعجبين
                </button>
                <button
                  onClick={() => setCurrentView('browser')}
                  className={`px-4 py-2 rounded-lg transition-colors ${currentView === 'browser'
                    ? 'bg-purple-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                >
                  🌐 تصفح Instagram
                </button>
              </div>

              <div className="flex items-center space-x-3">
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${backendConnected
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                  }`}>
                  <div className={`w-2 h-2 rounded-full ${backendConnected ? 'bg-green-500' : 'bg-yellow-500'
                    }`}></div>
                  <span>{backendConnected ? 'متصل' : 'محاكاة'}</span>
                </div>

                {selectedAccount && (
                  <div className="flex items-center space-x-2">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      الحساب: <span className="font-semibold">{selectedAccount.name}</span>
                    </div>
                    <button
                      onClick={() => window.open(`https://www.instagram.com/${selectedAccount.username}/`, '_blank')}
                      className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 transition-colors text-sm"
                      title="فتح حساب Instagram"
                    >
                      🔗 فتح
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto space-y-8">
          {/* عرض المحتوى حسب التبويب المختار */}
          {currentView === 'accounts' && (
            <AccountsManager
              onAccountSelect={setSelectedAccount}
              selectedAccountId={selectedAccount?.id}
            />
          )}

          {currentView === 'browser' && (
            <InstagramBrowser
              selectedAccount={selectedAccount}
            />
          )}

          {currentView === 'extraction' && (
            <>
              <UrlInput
                url={url}
                onUrlChange={setUrl}
                onExtract={handleExtract}
                isLoading={isLoading}
                selectedAccount={selectedAccount}
              />

              {error && (
                <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg">
                  <p className="font-medium">خطأ:</p>
                  <p>{error}</p>
                </div>
              )}

              {isLoading && (
                <div className="space-y-6">
                  <LoadingScreen />
                  {progress && (
                    <ProgressTracker
                      progress={progress}
                      currentTask={currentTask}
                      logs={extractionLogs}
                    />
                  )}
                </div>
              )}

              {results.length > 0 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      النتائج ({results.length} حساب)
                    </h2>
                    <ExportButtons data={results} />
                  </div>
                  <ResultsTable users={results} />
                </div>
              )}
            </>
          )}

          {currentView === 'dashboard' && (
            <DashboardView
              backendConnected={backendConnected}
              selectedAccount={selectedAccount}
              onNavigate={setCurrentView}
            />
          )}
        </div>
      </div>

      {/* نظام الإشعارات */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
}

export default App;
