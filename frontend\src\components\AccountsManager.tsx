import React, { useState, useEffect } from 'react';
import { apiService, InstagramAccount, InstagramAccountCreate } from '../services/api';
import InstagramAccountCard from './InstagramAccountCard';

interface AccountsManagerProps {
  onAccountSelect?: (account: InstagramAccount) => void;
  selectedAccountId?: number;
}

const AccountsManager: React.FC<AccountsManagerProps> = ({ onAccountSelect, selectedAccountId }) => {
  const [accounts, setAccounts] = useState<InstagramAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState<InstagramAccount | null>(null);
  const [formData, setFormData] = useState<InstagramAccountCreate>({
    name: '',
    username: '',
    password: '',
    email: '',
    phone: '',
    is_active: true,
    notes: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('cards');

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const accountsData = await apiService.getAccounts();
      setAccounts(accountsData);
    } catch (err) {
      setError('فشل في تحميل الحسابات');
      console.error('Error loading accounts:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      if (editingAccount) {
        // تحديث حساب موجود
        const updatedAccount = await apiService.updateAccount(editingAccount.id, formData);
        setAccounts(accounts.map(acc => acc.id === editingAccount.id ? updatedAccount : acc));
        setSuccess('تم تحديث الحساب بنجاح');
      } else {
        // إنشاء حساب جديد
        const newAccount = await apiService.createAccount(formData);
        setAccounts([...accounts, newAccount]);
        setSuccess('تم إنشاء الحساب بنجاح');
      }

      resetForm();
    } catch (err: any) {
      setError(err.message || 'حدث خطأ أثناء حفظ الحساب');
    }
  };

  const handleEdit = (account: InstagramAccount) => {
    setEditingAccount(account);
    setFormData({
      name: account.name,
      username: account.username,
      password: '', // لا نعرض كلمة المرور
      email: account.email || '',
      phone: account.phone || '',
      is_active: account.is_active,
      notes: account.notes || ''
    });
    setShowAddForm(true);
  };

  const handleDelete = async (account: InstagramAccount) => {
    if (!confirm(`هل أنت متأكد من حذف الحساب "${account.name}"؟`)) {
      return;
    }

    try {
      await apiService.deleteAccount(account.id);
      setAccounts(accounts.filter(acc => acc.id !== account.id));
      setSuccess('تم حذف الحساب بنجاح');
    } catch (err: any) {
      setError(err.message || 'فشل في حذف الحساب');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      username: '',
      password: '',
      email: '',
      phone: '',
      is_active: true,
      notes: ''
    });
    setEditingAccount(null);
    setShowAddForm(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
        <span className="mr-3">جارٍ تحميل الحسابات...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            إدارة حسابات Instagram
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
            إدارة حسابات Instagram للاستخراج مع إمكانية فتح الحسابات مباشرة
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* أزرار تبديل العرض */}
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('cards')}
              className={`px-3 py-1 rounded-md text-sm transition-colors ${viewMode === 'cards'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
                }`}
            >
              🎴 بطاقات
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded-md text-sm transition-colors ${viewMode === 'list'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
                }`}
            >
              📋 قائمة
            </button>
          </div>

          <button
            onClick={() => setShowAddForm(true)}
            className="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            + إضافة حساب جديد
          </button>
        </div>
      </div>

      {/* رسائل النجاح والخطأ */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 dark:bg-red-900/20 border border-red-400 text-red-700 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-100 dark:bg-green-900/20 border border-green-400 text-green-700 dark:text-green-400 rounded-lg">
          {success}
        </div>
      )}

      {/* نموذج إضافة/تعديل الحساب */}
      {showAddForm && (
        <div className="mb-6 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            {editingAccount ? 'تعديل الحساب' : 'إضافة حساب جديد'}
          </h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم الحساب *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="مثال: حساب العمل"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم المستخدم *
                </label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="username"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  كلمة المرور *
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required={!editingAccount}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder={editingAccount ? "اتركه فارغاً لعدم التغيير" : "كلمة المرور"}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  رقم الهاتف
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="+966xxxxxxxxx"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                />
                <label className="mr-2 block text-sm text-gray-900 dark:text-white">
                  حساب نشط
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ملاحظات
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                placeholder="ملاحظات إضافية..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg transition-colors"
              >
                {editingAccount ? 'تحديث' : 'إضافة'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* قائمة الحسابات */}
      <div>
        {accounts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد حسابات مضافة بعد
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              ابدأ بإضافة حساب Instagram الأول للاستخراج
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="bg-pink-500 hover:bg-pink-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              + إضافة حساب الآن
            </button>
          </div>
        ) : viewMode === 'cards' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {accounts.map((account) => (
              <InstagramAccountCard
                key={account.id}
                account={account}
                isSelected={selectedAccountId === account.id}
                onSelect={onAccountSelect || (() => { })}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {accounts.map((account) => (
              <div
                key={account.id}
                className={`p-4 border rounded-lg transition-all cursor-pointer ${selectedAccountId === account.id
                  ? 'border-pink-500 bg-pink-50 dark:bg-pink-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                  }`}
                onClick={() => onAccountSelect?.(account)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {account.name}
                      </h3>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        @{account.username}
                      </span>
                      {account.is_verified && (
                        <span className="text-blue-500">✓</span>
                      )}
                      {!account.is_active && (
                        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                          غير نشط
                        </span>
                      )}
                    </div>

                    {account.email && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        📧 {account.email}
                      </p>
                    )}

                    {account.notes && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        📝 {account.notes}
                      </p>
                    )}

                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>تم الإنشاء: {new Date(account.created_at).toLocaleDateString('ar')}</span>
                      {account.last_used && (
                        <span>آخر استخدام: {new Date(account.last_used).toLocaleDateString('ar')}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(`https://www.instagram.com/${account.username}/`, '_blank');
                      }}
                      className="p-2 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
                      title="فتح حساب Instagram"
                    >
                      🔗
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(account);
                      }}
                      className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      title="تعديل"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(account);
                      }}
                      className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      title="حذف"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountsManager;
