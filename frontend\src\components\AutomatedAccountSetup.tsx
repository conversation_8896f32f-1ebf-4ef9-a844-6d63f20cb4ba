import React, { useState, useEffect } from 'react';

interface AutomatedAccountSetupProps {
  onAccountAdded?: (account: any) => void;
  onClose?: () => void;
}

const AutomatedAccountSetup: React.FC<AutomatedAccountSetupProps> = ({
  onAccountAdded,
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState<'start' | 'opening' | 'waiting' | 'processing' | 'complete' | 'error'>('start');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [automationStatus, setAutomationStatus] = useState<any>(null);

  // التحقق من حالة الأتمتة
  const checkAutomationStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/automation/status');
      const data = await response.json();
      setAutomationStatus(data);
    } catch (err) {
      console.error('خطأ في التحقق من حالة الأتمتة:', err);
    }
  };

  useEffect(() => {
    checkAutomationStatus();
    const interval = setInterval(checkAutomationStatus, 3000);
    return () => clearInterval(interval);
  }, []);

  // بدء عملية الأتمتة
  const startAutomation = async () => {
    try {
      setCurrentStep('opening');
      setMessage('جارٍ فتح Instagram...');
      setError('');

      const response = await fetch('http://localhost:8000/api/automation/start-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setCurrentStep('waiting');
        setMessage('تم فتح Instagram! يرجى تسجيل الدخول في النافذة الجديدة...');

        // بدء مراقبة اكتمال تسجيل الدخول
        setTimeout(() => {
          completeWorkflow();
        }, 5000); // انتظار 5 ثوانٍ للمستخدم لتسجيل الدخول
      } else {
        setCurrentStep('error');
        setError(data.message || 'فشل في فتح Instagram');
      }
    } catch (err) {
      setCurrentStep('error');
      setError('خطأ في الاتصال بالخادم');
    }
  };

  // إكمال سير العمل
  const completeWorkflow = async () => {
    try {
      setCurrentStep('processing');
      setMessage('جارٍ معالجة الحساب وحفظ الكوكيز...');

      const response = await fetch('http://localhost:8000/api/automation/complete-workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setCurrentStep('complete');
        setMessage('تم إضافة الحساب بنجاح! يمكنك الآن تصفح Instagram واستخراج المعجبين.');

        // إشعار المكون الأب بإضافة الحساب
        if (onAccountAdded) {
          onAccountAdded({
            id: Date.now(),
            name: 'حساب جديد',
            username: 'تم إضافته تلقائياً',
            automated: true
          });
        }
      } else {
        setCurrentStep('error');
        setError(data.message || 'فشل في معالجة الحساب');
      }
    } catch (err) {
      setCurrentStep('error');
      setError('خطأ في معالجة الحساب');
    }
  };

  // إغلاق الأتمتة
  const closeAutomation = async () => {
    try {
      await fetch('http://localhost:8000/api/automation/close', { method: 'POST' });
      if (onClose) onClose();
    } catch (err) {
      console.error('خطأ في إغلاق الأتمتة:', err);
    }
  };

  const getStepIcon = () => {
    switch (currentStep) {
      case 'start': return '🚀';
      case 'opening': return '🔄';
      case 'waiting': return '⏳';
      case 'processing': return '⚙️';
      case 'complete': return '✅';
      case 'error': return '❌';
      default: return '🔄';
    }
  };

  const getStepColor = () => {
    switch (currentStep) {
      case 'complete': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'processing': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 max-w-md w-full mx-4">
        {/* العنوان */}
        <div className="text-center mb-6">
          <div className={`text-6xl mb-4 ${getStepColor()}`}>
            {getStepIcon()}
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            إضافة حساب تلقائياً
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            سيتم فتح Instagram وحفظ الحساب تلقائياً
          </p>
        </div>

        {/* الخطوات */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${['opening', 'waiting', 'processing', 'complete'].includes(currentStep)
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-600'
              }`}>
              1
            </div>
            <span className="text-gray-700 dark:text-gray-300">فتح Instagram</span>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${['waiting', 'processing', 'complete'].includes(currentStep)
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-600'
              }`}>
              2
            </div>
            <span className="text-gray-700 dark:text-gray-300">تسجيل الدخول</span>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${['processing', 'complete'].includes(currentStep)
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-600'
              }`}>
              3
            </div>
            <span className="text-gray-700 dark:text-gray-300">حفظ الكوكيز</span>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${currentStep === 'complete'
              ? 'bg-green-500 text-white'
              : 'bg-gray-300 text-gray-600'
              }`}>
              4
            </div>
            <span className="text-gray-700 dark:text-gray-300">حفظ الحساب</span>
          </div>
        </div>

        {/* الرسالة الحالية */}
        {message && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-blue-800 dark:text-blue-200 text-sm">{message}</p>
          </div>
        )}

        {/* رسالة الخطأ */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
          </div>
        )}

        {/* معلومات الأتمتة */}
        {automationStatus && (
          <div className="mb-6 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">حالة الأتمتة:</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الأتمتة نشطة:</span>
                <span className={automationStatus.automation_active ? 'text-green-600' : 'text-red-600'}>
                  {automationStatus.automation_active ? '✅ نعم' : '❌ لا'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">المتصفح نشط:</span>
                <span className={automationStatus.browser_active ? 'text-green-600' : 'text-red-600'}>
                  {automationStatus.browser_active ? '✅ نعم' : '❌ لا'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">واجهة الاستخراج:</span>
                <span className={automationStatus.extraction_overlay_injected ? 'text-green-600' : 'text-gray-600'}>
                  {automationStatus.extraction_overlay_injected ? '✅ مُفعلة' : '⏳ غير مُفعلة'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* الأزرار */}
        <div className="flex space-x-3">
          {currentStep === 'start' && (
            <>
              <button
                onClick={startAutomation}
                className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white py-3 px-6 rounded-lg font-medium transition-all transform hover:scale-105"
              >
                🚀 بدء الأتمتة
              </button>
              <button
                onClick={onClose}
                className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                إلغاء
              </button>
            </>
          )}

          {currentStep === 'waiting' && (
            <button
              onClick={completeWorkflow}
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              ⚙️ متابعة المعالجة
            </button>
          )}

          {['processing'].includes(currentStep) && (
            <div className="flex-1 bg-gray-400 text-white py-3 px-6 rounded-lg font-medium text-center">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                جارٍ المعالجة...
              </div>
            </div>
          )}

          {['complete', 'error'].includes(currentStep) && (
            <>
              {currentStep === 'complete' && (
                <button
                  onClick={closeAutomation}
                  className="flex-1 bg-green-500 hover:bg-green-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                >
                  ✅ إنهاء
                </button>
              )}
              {currentStep === 'error' && (
                <button
                  onClick={() => setCurrentStep('start')}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                >
                  🔄 إعادة المحاولة
                </button>
              )}
              <button
                onClick={onClose}
                className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                إغلاق
              </button>
            </>
          )}
        </div>

        {/* تحذير */}
        <div className="mt-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <p className="text-yellow-800 dark:text-yellow-200 text-xs">
            ⚠️ تأكد من تسجيل الدخول بحساب Instagram صحيح في النافذة المفتوحة
          </p>
        </div>
      </div>
    </div>
  );
};

export default AutomatedAccountSetup;
