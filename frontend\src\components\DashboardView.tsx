import React, { useState, useEffect } from 'react';
import { apiService, InstagramAccount, SystemStats } from '../services/api';

interface DashboardViewProps {
  backendConnected: boolean;
  selectedAccount: InstagramAccount | null;
  onNavigate: (view: 'dashboard' | 'accounts' | 'extraction') => void;
}

const DashboardView: React.FC<DashboardViewProps> = ({
  backendConnected,
  selectedAccount,
  onNavigate
}) => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, [backendConnected]);

  const loadStats = async () => {
    if (!backendConnected) {
      // إحصائيات وهمية للمحاكاة
      setStats({
        total_accounts: 0,
        active_accounts: 0,
        total_jobs: 0,
        total_extracted_users: 0,
        jobs_today: 0
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const systemStats = await apiService.getSystemStats();
      setStats(systemStats);
    } catch (error) {
      console.error('Error loading stats:', error);
      // إحصائيات افتراضية في حالة الخطأ
      setStats({
        total_accounts: 0,
        active_accounts: 0,
        total_jobs: 0,
        total_extracted_users: 0,
        jobs_today: 0
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
        <span className="mr-3">جارٍ تحميل الإحصائيات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* ترحيب */}
      <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-orange-500 rounded-xl shadow-lg p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              مرحباً بك في Instagram Likes Extractor
            </h1>
            <p className="text-pink-100 text-lg">
              نظام متطور لاستخراج قوائم المعجبين من منشورات Instagram
            </p>
          </div>
          <div className="text-6xl opacity-20">
            📊
          </div>
        </div>
      </div>

      {/* حالة الاتصال */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                حالة النظام
              </h3>
              <div className="space-y-2">
                <div className={`flex items-center space-x-2 ${backendConnected ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                  <div className={`w-3 h-3 rounded-full ${backendConnected ? 'bg-green-500' : 'bg-yellow-500'
                    }`}></div>
                  <span className="font-medium">
                    {backendConnected ? 'Backend متصل' : 'وضع المحاكاة'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {backendConnected
                    ? 'جميع الميزات متاحة'
                    : 'ميزات محدودة - للعرض التوضيحي'
                  }
                </div>
              </div>
            </div>
            <div className="text-4xl">
              {backendConnected ? '🟢' : '🟡'}
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                الحساب المختار
              </h3>
              {selectedAccount ? (
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {selectedAccount.name}
                    </div>
                    <button
                      onClick={() => window.open(`https://www.instagram.com/${selectedAccount.username}/`, '_blank')}
                      className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 transition-colors"
                      title="فتح حساب Instagram"
                    >
                      🔗
                    </button>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    @{selectedAccount.username}
                  </div>
                  {selectedAccount.is_verified && (
                    <div className="text-blue-500 text-sm">✓ موثق</div>
                  )}
                </div>
              ) : (
                <div className="text-gray-500 dark:text-gray-400">
                  لم يتم اختيار حساب
                </div>
              )}
            </div>
            <div className="text-4xl">
              {selectedAccount ? '👤' : '❓'}
            </div>
          </div>
        </div>
      </div>

      {/* الإحصائيات */}
      {stats && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
            إحصائيات النظام
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-600 dark:text-pink-400">
                {stats.total_accounts}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إجمالي الحسابات
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                {stats.active_accounts}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                حسابات نشطة
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                {stats.total_jobs}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إجمالي المهام
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                {stats.total_extracted_users}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                مستخدمين مستخرجين
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                {stats.jobs_today}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                مهام اليوم
              </div>
            </div>
          </div>
        </div>
      )}

      {/* الإجراءات السريعة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
          الإجراءات السريعة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => onNavigate('accounts')}
            className="p-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all transform hover:scale-105"
          >
            <div className="text-3xl mb-3">👥</div>
            <div className="font-semibold mb-2">إدارة الحسابات</div>
            <div className="text-sm opacity-90">
              إضافة وتعديل حسابات Instagram
            </div>
          </button>

          <button
            onClick={() => onNavigate('extraction')}
            className="p-6 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all transform hover:scale-105"
            disabled={!selectedAccount}
          >
            <div className="text-3xl mb-3">🔍</div>
            <div className="font-semibold mb-2">بدء الاستخراج</div>
            <div className="text-sm opacity-90">
              {selectedAccount ? 'استخراج المعجبين' : 'اختر حساب أولاً'}
            </div>
          </button>

          <button
            onClick={loadStats}
            className="p-6 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded-lg hover:from-green-600 hover:to-teal-600 transition-all transform hover:scale-105"
          >
            <div className="text-3xl mb-3">🔄</div>
            <div className="font-semibold mb-2">تحديث الإحصائيات</div>
            <div className="text-sm opacity-90">
              إعادة تحميل البيانات
            </div>
          </button>
        </div>
      </div>

      {/* نصائح وإرشادات */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold text-blue-900 dark:text-blue-100 mb-4">
          💡 نصائح للاستخدام الأمثل
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="text-blue-500 mt-1">🔐</div>
              <div>
                <div className="font-medium text-blue-900 dark:text-blue-100">
                  أمان كلمات المرور
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  جميع كلمات المرور مشفرة ومحمية
                </div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="text-blue-500 mt-1">⚡</div>
              <div>
                <div className="font-medium text-blue-900 dark:text-blue-100">
                  سرعة الاستخراج
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  العملية قد تستغرق عدة دقائق حسب عدد المعجبين
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="text-blue-500 mt-1">📊</div>
              <div>
                <div className="font-medium text-blue-900 dark:text-blue-100">
                  تصدير البيانات
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  يمكن تصدير النتائج بصيغ CSV, JSON, Excel
                </div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="text-blue-500 mt-1">🛡️</div>
              <div>
                <div className="font-medium text-blue-900 dark:text-blue-100">
                  تجنب الحظر
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  نستخدم تقنيات متقدمة لتجنب حظر الحسابات
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardView;
