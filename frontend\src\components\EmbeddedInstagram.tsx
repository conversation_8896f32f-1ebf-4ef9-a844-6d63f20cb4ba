import React, { useState, useEffect, useRef } from 'react';

interface EmbeddedInstagramProps {
  onAccountDetected?: (account: any) => void;
  onPostHover?: (postUrl: string) => void;
}

const EmbeddedInstagram: React.FC<EmbeddedInstagramProps> = ({
  onAccountDetected,
  onPostHover
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState('https://www.instagram.com/accounts/login/');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<any>(null);
  const [extractionMode, setExtractionMode] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // مراقبة تغييرات الـ iframe
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const handleLoad = () => {
      setIsLoading(false);
      try {
        // محاولة الوصول لمحتوى الـ iframe
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (iframeDoc) {
          checkLoginStatus(iframeDoc);
          injectExtractionButtons(iframeDoc);
        }
      } catch (error) {
        console.log('CORS restriction - using alternative method');
        // استخدام postMessage للتواصل
        setupPostMessageListener();
      }
    };

    iframe.addEventListener('load', handleLoad);
    return () => iframe.removeEventListener('load', handleLoad);
  }, []);

  // فحص حالة تسجيل الدخول
  const checkLoginStatus = (doc: Document) => {
    // البحث عن عناصر تدل على تسجيل الدخول
    const loginForm = doc.querySelector('form[method="post"]');
    const profileLink = doc.querySelector('a[href*="/"]');
    
    if (!loginForm && profileLink) {
      setIsLoggedIn(true);
      // استخراج معلومات الحساب
      extractAccountInfo(doc);
    }
  };

  // استخراج معلومات الحساب
  const extractAccountInfo = (doc: Document) => {
    try {
      // البحث عن اسم المستخدم في الـ URL أو العناصر
      const currentUrl = doc.location?.href || '';
      const usernameMatch = currentUrl.match(/instagram\.com\/([^\/\?]+)/);
      
      if (usernameMatch) {
        const username = usernameMatch[1];
        const account = {
          username: username,
          name: username, // سيتم تحديثه لاحقاً
          profile_url: `https://www.instagram.com/${username}/`,
          detected_at: new Date().toISOString()
        };
        
        setCurrentAccount(account);
        if (onAccountDetected) {
          onAccountDetected(account);
        }
      }
    } catch (error) {
      console.error('خطأ في استخراج معلومات الحساب:', error);
    }
  };

  // حقن أزرار الاستخراج
  const injectExtractionButtons = (doc: Document) => {
    if (!extractionMode) return;

    try {
      // إضافة CSS للأزرار
      const style = doc.createElement('style');
      style.textContent = `
        .instagram-extractor-btn {
          position: absolute !important;
          top: 10px !important;
          right: 10px !important;
          background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045) !important;
          color: white !important;
          border: none !important;
          padding: 8px 12px !important;
          border-radius: 20px !important;
          cursor: pointer !important;
          font-size: 12px !important;
          font-weight: bold !important;
          z-index: 9999 !important;
          opacity: 0 !important;
          transition: opacity 0.3s !important;
          box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
        }
        .instagram-extractor-btn:hover {
          transform: scale(1.05) !important;
          opacity: 1 !important;
        }
        article:hover .instagram-extractor-btn {
          opacity: 1 !important;
        }
      `;
      doc.head.appendChild(style);

      // إضافة أزرار للمنشورات
      const posts = doc.querySelectorAll('article');
      posts.forEach((post, index) => {
        if (!post.querySelector('.instagram-extractor-btn')) {
          const btn = doc.createElement('button');
          btn.className = 'instagram-extractor-btn';
          btn.textContent = '🔍 استخراج';
          btn.onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            // العثور على رابط المنشور
            const postLink = post.querySelector('a[href*="/p/"], a[href*="/reel/"]') as HTMLAnchorElement;
            if (postLink && onPostHover) {
              onPostHover(postLink.href);
            }
          };
          
          post.style.position = 'relative';
          post.appendChild(btn);
        }
      });
    } catch (error) {
      console.error('خطأ في حقن أزرار الاستخراج:', error);
    }
  };

  // إعداد مستمع postMessage
  const setupPostMessageListener = () => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://www.instagram.com') return;
      
      if (event.data.type === 'INSTAGRAM_LOGIN_SUCCESS') {
        setIsLoggedIn(true);
        setCurrentAccount(event.data.account);
        if (onAccountDetected) {
          onAccountDetected(event.data.account);
        }
      }
      
      if (event.data.type === 'INSTAGRAM_POST_CLICK') {
        if (onPostHover) {
          onPostHover(event.data.url);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  };

  // التنقل إلى صفحة معينة
  const navigateTo = (url: string) => {
    setCurrentUrl(url);
    setIsLoading(true);
  };

  // تفعيل/إلغاء وضع الاستخراج
  const toggleExtractionMode = () => {
    setExtractionMode(!extractionMode);
  };

  // حفظ الحساب تلقائياً
  const saveAccountAutomatically = async () => {
    if (!currentAccount) return;

    try {
      const response = await fetch('http://localhost:8000/api/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `حساب ${currentAccount.username}`,
          username: currentAccount.username,
          notes: `تم إضافته تلقائياً في ${new Date().toLocaleString('ar')}`
        }),
      });

      if (response.ok) {
        alert('✅ تم حفظ الحساب تلقائياً!');
      }
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error);
    }
  };

  return (
    <div className="w-full h-full flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* شريط التحكم */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
        <div className="flex items-center space-x-3">
          <h3 className="font-bold text-lg">📱 Instagram مدمج</h3>
          {isLoggedIn && currentAccount && (
            <div className="flex items-center space-x-2 bg-white/20 rounded-lg px-3 py-1">
              <span className="text-sm">👤 {currentAccount.username}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* أزرار التنقل */}
          <button
            onClick={() => navigateTo('https://www.instagram.com/')}
            className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded-lg text-sm transition-colors"
            title="الصفحة الرئيسية"
          >
            🏠
          </button>
          
          <button
            onClick={() => navigateTo('https://www.instagram.com/accounts/login/')}
            className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded-lg text-sm transition-colors"
            title="تسجيل الدخول"
          >
            🔐
          </button>
          
          {/* وضع الاستخراج */}
          <button
            onClick={toggleExtractionMode}
            className={`px-3 py-1 rounded-lg text-sm transition-colors ${
              extractionMode 
                ? 'bg-green-500 hover:bg-green-600' 
                : 'bg-white/20 hover:bg-white/30'
            }`}
            title="تفعيل/إلغاء وضع الاستخراج"
          >
            {extractionMode ? '🔍 مُفعل' : '🔍 غير مُفعل'}
          </button>
          
          {/* حفظ الحساب */}
          {isLoggedIn && currentAccount && (
            <button
              onClick={saveAccountAutomatically}
              className="px-3 py-1 bg-green-500 hover:bg-green-600 rounded-lg text-sm transition-colors"
              title="حفظ الحساب تلقائياً"
            >
              💾 حفظ
            </button>
          )}
        </div>
      </div>

      {/* شريط العنوان */}
      <div className="flex items-center p-2 bg-gray-100 dark:bg-gray-700 border-b">
        <div className="flex items-center space-x-2 flex-1">
          <input
            type="url"
            value={currentUrl}
            onChange={(e) => setCurrentUrl(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                navigateTo(currentUrl);
              }
            }}
            className="flex-1 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
            placeholder="https://www.instagram.com/"
          />
          <button
            onClick={() => navigateTo(currentUrl)}
            className="px-4 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded-lg text-sm transition-colors"
          >
            انتقال
          </button>
        </div>
      </div>

      {/* حالة التحميل */}
      {isLoading && (
        <div className="flex items-center justify-center p-8 bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
            <span className="text-gray-600 dark:text-gray-400">جارٍ تحميل Instagram...</span>
          </div>
        </div>
      )}

      {/* معلومات الحالة */}
      <div className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 text-sm">
        <div className="flex items-center space-x-3">
          <span className={`flex items-center space-x-1 ${isLoggedIn ? 'text-green-600' : 'text-red-600'}`}>
            <span>{isLoggedIn ? '✅' : '❌'}</span>
            <span>{isLoggedIn ? 'مسجل الدخول' : 'غير مسجل'}</span>
          </span>
          
          <span className={`flex items-center space-x-1 ${extractionMode ? 'text-green-600' : 'text-gray-600'}`}>
            <span>{extractionMode ? '🔍' : '⏸️'}</span>
            <span>وضع الاستخراج</span>
          </span>
        </div>
        
        {currentAccount && (
          <span className="text-blue-600 dark:text-blue-400">
            👤 {currentAccount.username}
          </span>
        )}
      </div>

      {/* الـ iframe */}
      <div className="flex-1 relative">
        <iframe
          ref={iframeRef}
          src={currentUrl}
          className="w-full h-full border-0"
          title="Instagram"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
          onLoad={() => setIsLoading(false)}
        />
        
        {/* طبقة تفاعل شفافة */}
        {extractionMode && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-lg text-sm font-bold shadow-lg">
              🔍 وضع الاستخراج مُفعل - مرر على المنشورات
            </div>
          </div>
        )}
      </div>

      {/* تعليمات الاستخدام */}
      <div className="p-3 bg-gray-50 dark:bg-gray-700 border-t text-sm">
        <div className="flex items-center justify-between">
          <div className="text-gray-600 dark:text-gray-400">
            💡 <strong>تعليمات:</strong> 
            {!isLoggedIn ? ' سجل دخولك أولاً' : 
             !extractionMode ? ' فعل وضع الاستخراج' : 
             ' مرر على المنشورات لاستخراج المعجبين'}
          </div>
          
          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
            <span>🔒 آمن</span>
            <span>•</span>
            <span>⚡ سريع</span>
            <span>•</span>
            <span>🎯 دقيق</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmbeddedInstagram;
