import React, { useState } from 'react';
import { InstagramAccount } from '../services/api';

interface InstagramAccountCardProps {
  account: InstagramAccount;
  isSelected: boolean;
  onSelect: (account: InstagramAccount) => void;
  onEdit: (account: InstagramAccount) => void;
  onDelete: (account: InstagramAccount) => void;
}

const InstagramAccountCard: React.FC<InstagramAccountCardProps> = ({
  account,
  isSelected,
  onSelect,
  onEdit,
  onDelete
}) => {
  const [showActions, setShowActions] = useState(false);

  const openInstagramProfile = () => {
    window.open(`https://www.instagram.com/${account.username}/`, '_blank');
  };

  const openInstagramLogin = () => {
    window.open('https://www.instagram.com/accounts/login/', '_blank');
  };

  const copyUsername = () => {
    navigator.clipboard.writeText(account.username);
    // يمكن إضافة toast notification هنا
  };

  const getAccountStatusColor = () => {
    if (!account.is_active) return 'border-gray-300 bg-gray-50';
    if (isSelected) return 'border-pink-500 bg-pink-50 dark:bg-pink-900/20';
    return 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500';
  };

  const getAccountStatusIcon = () => {
    if (!account.is_active) return '⏸️';
    if (account.is_verified) return '✅';
    return '👤';
  };

  return (
    <div
      className={`relative p-4 border rounded-lg transition-all cursor-pointer ${getAccountStatusColor()}`}
      onClick={() => onSelect(account)}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* شارة الحالة */}
      <div className="absolute top-2 left-2">
        <span className="text-lg" title={account.is_verified ? 'حساب موثق' : 'حساب عادي'}>
          {getAccountStatusIcon()}
        </span>
      </div>

      {/* معلومات الحساب */}
      <div className="space-y-3">
        {/* الاسم واسم المستخدم */}
        <div>
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
              {account.name}
            </h3>
            {isSelected && (
              <span className="px-2 py-1 text-xs bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400 rounded-full">
                مختار
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2 mt-1">
            <span className="text-gray-600 dark:text-gray-400">@{account.username}</span>
            <button
              onClick={(e) => {
                e.stopPropagation();
                copyUsername();
              }}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="نسخ اسم المستخدم"
            >
              📋
            </button>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="space-y-1 text-sm">
          {account.email && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <span className="mr-2">📧</span>
              <span>{account.email}</span>
            </div>
          )}
          
          {account.phone && (
            <div className="flex items-center text-gray-600 dark:text-gray-400">
              <span className="mr-2">📱</span>
              <span>{account.phone}</span>
            </div>
          )}
          
          {account.notes && (
            <div className="flex items-start text-gray-600 dark:text-gray-400">
              <span className="mr-2 mt-0.5">📝</span>
              <span className="text-xs">{account.notes}</span>
            </div>
          )}
        </div>

        {/* تواريخ */}
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-600">
          <span>أُنشئ: {new Date(account.created_at).toLocaleDateString('ar')}</span>
          {account.last_used && (
            <span>آخر استخدام: {new Date(account.last_used).toLocaleDateString('ar')}</span>
          )}
        </div>

        {/* حالة النشاط */}
        {!account.is_active && (
          <div className="text-center py-2">
            <span className="px-3 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 rounded-full">
              غير نشط
            </span>
          </div>
        )}
      </div>

      {/* أزرار الإجراءات */}
      <div className={`absolute top-2 right-2 flex space-x-1 transition-opacity duration-200 ${
        showActions ? 'opacity-100' : 'opacity-0'
      }`}>
        {/* فتح Instagram */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            openInstagramProfile();
          }}
          className="p-1.5 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors text-sm"
          title="فتح حساب Instagram"
        >
          🔗
        </button>

        {/* فتح صفحة تسجيل الدخول */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            openInstagramLogin();
          }}
          className="p-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm"
          title="فتح صفحة تسجيل الدخول"
        >
          🔐
        </button>

        {/* تعديل */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEdit(account);
          }}
          className="p-1.5 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors text-sm"
          title="تعديل الحساب"
        >
          ✏️
        </button>

        {/* حذف */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(account);
          }}
          className="p-1.5 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors text-sm"
          title="حذف الحساب"
        >
          🗑️
        </button>
      </div>

      {/* شريط سريع للإجراءات */}
      <div className="mt-4 flex justify-center space-x-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            openInstagramProfile();
          }}
          className="flex-1 py-2 px-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white text-sm rounded-lg transition-all transform hover:scale-105"
        >
          🔗 فتح الحساب
        </button>
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            openInstagramLogin();
          }}
          className="flex-1 py-2 px-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white text-sm rounded-lg transition-all transform hover:scale-105"
        >
          🔐 تسجيل دخول
        </button>
      </div>

      {/* مؤشر الاختيار */}
      {isSelected && (
        <div className="absolute inset-0 border-2 border-pink-500 rounded-lg pointer-events-none">
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-pink-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs">✓</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstagramAccountCard;
