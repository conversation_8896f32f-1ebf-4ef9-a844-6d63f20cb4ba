import React, { useState, useEffect } from 'react';

interface InstagramBrowserProps {
  selectedAccount?: any;
}

const InstagramBrowser: React.FC<InstagramBrowserProps> = ({ selectedAccount }) => {
  const [automationStatus, setAutomationStatus] = useState<any>(null);
  const [extractionResults, setExtractionResults] = useState<any[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');

  // التحقق من حالة الأتمتة
  const checkAutomationStatus = async () => {
    try {
      const response = await fetch('/api/automation/status');
      const data = await response.json();
      setAutomationStatus(data);
    } catch (err) {
      console.error('خطأ في التحقق من حالة الأتمتة:', err);
    }
  };

  useEffect(() => {
    checkAutomationStatus();
    const interval = setInterval(checkAutomationStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // استخراج المعجبين من رابط
  const extractLikesFromUrl = async (url: string) => {
    if (!url || isExtracting) return;

    try {
      setIsExtracting(true);
      setCurrentUrl(url);

      const response = await fetch('/api/automation/extract-likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      const data = await response.json();

      if (data.success) {
        setExtractionResults(prev => [...prev, {
          id: Date.now(),
          url: url,
          likes: data.data,
          count: data.extracted_count,
          timestamp: new Date().toISOString()
        }]);
      } else {
        alert(`خطأ في الاستخراج: ${data.message}`);
      }
    } catch (err) {
      alert('خطأ في الاتصال بالخادم');
    } finally {
      setIsExtracting(false);
      setCurrentUrl('');
    }
  };

  // فتح Instagram للتصفح
  const openInstagramForBrowsing = () => {
    if (selectedAccount) {
      window.open(`https://www.instagram.com/${selectedAccount.username}/`, '_blank');
    } else {
      window.open('https://www.instagram.com/', '_blank');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      {/* العنوان */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          🌐 تصفح Instagram مع الاستخراج التلقائي
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          تصفح Instagram واستخرج المعجبين بنقرة واحدة على أي منشور
        </p>
      </div>

      {/* معلومات الحساب المختار */}
      {selectedAccount ? (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                الحساب النشط:
              </h3>
              <div className="flex items-center space-x-3 mt-1">
                <span className="text-blue-800 dark:text-blue-200 font-medium">
                  {selectedAccount.name}
                </span>
                <span className="text-blue-600 dark:text-blue-400">
                  @{selectedAccount.username}
                </span>
                {selectedAccount.is_verified && (
                  <span className="text-blue-500">✅</span>
                )}
              </div>
            </div>
            <button
              onClick={openInstagramForBrowsing}
              className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-lg transition-all transform hover:scale-105"
            >
              🌐 تصفح Instagram
            </button>
          </div>
        </div>
      ) : (
        <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center">
            <span className="text-yellow-600 dark:text-yellow-400 mr-3">⚠️</span>
            <div>
              <h3 className="font-semibold text-yellow-900 dark:text-yellow-100">
                لم يتم اختيار حساب
              </h3>
              <p className="text-yellow-800 dark:text-yellow-200 text-sm mt-1">
                يرجى اختيار حساب من "إدارة الحسابات" أولاً
              </p>
            </div>
          </div>
        </div>
      )}

      {/* حالة الأتمتة */}
      {automationStatus && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">🤖 حالة الأتمتة:</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-2xl mb-1 ${automationStatus.automation_active ? 'text-green-500' : 'text-red-500'}`}>
                {automationStatus.automation_active ? '✅' : '❌'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">الأتمتة</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl mb-1 ${automationStatus.browser_active ? 'text-green-500' : 'text-red-500'}`}>
                {automationStatus.browser_active ? '🌐' : '❌'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">المتصفح</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl mb-1 ${automationStatus.extraction_overlay_injected ? 'text-green-500' : 'text-gray-500'}`}>
                {automationStatus.extraction_overlay_injected ? '🔍' : '⏳'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">واجهة الاستخراج</div>
            </div>
          </div>
        </div>
      )}

      {/* استخراج يدوي */}
      <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <h4 className="font-semibold text-green-900 dark:text-green-100 mb-3">🔍 استخراج يدوي:</h4>
        <div className="flex space-x-3">
          <input
            type="url"
            placeholder="https://www.instagram.com/p/ABC123..."
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const target = e.target as HTMLInputElement;
                extractLikesFromUrl(target.value);
              }
            }}
          />
          <button
            onClick={() => {
              const input = document.querySelector('input[type="url"]') as HTMLInputElement;
              if (input?.value) {
                extractLikesFromUrl(input.value);
              }
            }}
            disabled={isExtracting}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isExtracting
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {isExtracting ? '🔄 جارٍ...' : '🔍 استخراج'}
          </button>
        </div>
      </div>

      {/* التعليمات */}
      <div className="mb-6 p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800">
        <h4 className="font-semibold text-indigo-900 dark:text-indigo-100 mb-3">📋 كيفية الاستخدام:</h4>
        <div className="space-y-2 text-sm text-indigo-800 dark:text-indigo-200">
          <div className="flex items-start space-x-2">
            <span className="font-bold">1.</span>
            <span>تأكد من تشغيل الأتمتة من "إدارة الحسابات" → "🤖 إضافة تلقائي"</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-bold">2.</span>
            <span>انقر "🌐 تصفح Instagram" لفتح Instagram في نافذة جديدة</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-bold">3.</span>
            <span>ستظهر أزرار "🔍 استخراج" على كل منشور عند التمرير عليه</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-bold">4.</span>
            <span>انقر "🔍 استخراج" على أي منشور لاستخراج المعجبين تلقائياً</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-bold">5.</span>
            <span>أو استخدم الاستخراج اليدوي بلصق رابط المنشور أعلاه</span>
          </div>
        </div>
      </div>

      {/* نتائج الاستخراج */}
      {extractionResults.length > 0 && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">📊 نتائج الاستخراج:</h4>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {extractionResults.map((result) => (
              <div key={result.id} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {result.count} معجب
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {result.url}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(result.timestamp).toLocaleTimeString('ar')}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      const data = JSON.stringify(result.likes, null, 2);
                      const blob = new Blob([data], { type: 'application/json' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `likes_${result.id}.json`;
                      a.click();
                    }}
                    className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                  >
                    📥 تحميل JSON
                  </button>
                  <button
                    onClick={() => {
                      const csv = result.likes.map((like: any) => 
                        `${like.username},${like.profile_url},${like.extracted_at}`
                      ).join('\n');
                      const blob = new Blob([`Username,Profile URL,Extracted At\n${csv}`], { type: 'text/csv' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `likes_${result.id}.csv`;
                      a.click();
                    }}
                    className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded transition-colors"
                  >
                    📊 تحميل CSV
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* حالة الاستخراج */}
      {isExtracting && (
        <div className="text-center py-4">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-blue-800 dark:text-blue-200">
              جارٍ استخراج المعجبين من: {currentUrl}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default InstagramBrowser;
