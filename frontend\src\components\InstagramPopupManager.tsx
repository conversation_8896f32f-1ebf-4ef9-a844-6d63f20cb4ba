import React, { useState, useEffect, useRef } from 'react';

interface InstagramPopupManagerProps {
  onAccountDetected?: (account: any) => void;
  onPostExtraction?: (postUrl: string, likes: any[]) => void;
  onClose?: () => void;
}

const InstagramPopupManager: React.FC<InstagramPopupManagerProps> = ({
  onAccountDetected,
  onPostExtraction,
  onClose
}) => {
  const [popupWindow, setPopupWindow] = useState<Window | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [currentAccount, setCurrentAccount] = useState<any>(null);
  const [extractionMode, setExtractionMode] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // فتح نافذة Instagram
  const openInstagramWindow = () => {
    const popup = window.open(
      'https://www.instagram.com/accounts/login/',
      'instagram_window',
      'width=800,height=600,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
    );
    
    if (popup) {
      setPopupWindow(popup);
      setIsConnected(true);
      addLog('✅ تم فتح نافذة Instagram');
      startMonitoring(popup);
    } else {
      addLog('❌ فشل في فتح النافذة - تأكد من السماح للنوافذ المنبثقة');
    }
  };

  // مراقبة النافذة المنبثقة
  const startMonitoring = (popup: Window) => {
    intervalRef.current = setInterval(() => {
      try {
        if (popup.closed) {
          setIsConnected(false);
          setPopupWindow(null);
          addLog('🔴 تم إغلاق نافذة Instagram');
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          return;
        }

        // محاولة قراءة URL الحالي
        try {
          const currentUrl = popup.location.href;
          checkForAccountInfo(currentUrl, popup);
        } catch (error) {
          // CORS error - نافذة في domain مختلف
          addLog('🔄 مراقبة النافذة...');
        }

        // حقن script للتواصل
        injectCommunicationScript(popup);
        
      } catch (error) {
        console.log('مراقبة النافذة:', error);
      }
    }, 2000);
  };

  // فحص معلومات الحساب
  const checkForAccountInfo = (url: string, popup: Window) => {
    if (url.includes('instagram.com') && !url.includes('/accounts/login')) {
      // المستخدم سجل دخوله
      const usernameMatch = url.match(/instagram\.com\/([^\/\?]+)/);
      if (usernameMatch && usernameMatch[1] !== 'accounts') {
        const username = usernameMatch[1];
        const account = {
          username: username,
          name: username,
          profile_url: `https://www.instagram.com/${username}/`,
          detected_at: new Date().toISOString(),
          source: 'popup_detection'
        };
        
        setCurrentAccount(account);
        addLog(`✅ تم اكتشاف الحساب: @${username}`);
        
        if (onAccountDetected) {
          onAccountDetected(account);
        }
      }
    }
  };

  // حقن script للتواصل مع النافذة
  const injectCommunicationScript = (popup: Window) => {
    try {
      const script = `
        // إضافة مستمع للرسائل
        if (!window.instagramExtractorInjected) {
          window.instagramExtractorInjected = true;
          
          // إضافة أزرار الاستخراج
          function addExtractionButtons() {
            const posts = document.querySelectorAll('article');
            posts.forEach((post, index) => {
              if (!post.querySelector('.instagram-extractor-btn')) {
                const btn = document.createElement('button');
                btn.className = 'instagram-extractor-btn';
                btn.innerHTML = '🔍 استخراج';
                btn.style.cssText = \`
                  position: absolute !important;
                  top: 10px !important;
                  right: 10px !important;
                  background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045) !important;
                  color: white !important;
                  border: none !important;
                  padding: 8px 12px !important;
                  border-radius: 20px !important;
                  cursor: pointer !important;
                  font-size: 12px !important;
                  font-weight: bold !important;
                  z-index: 9999 !important;
                  opacity: 0 !important;
                  transition: opacity 0.3s !important;
                  box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
                \`;
                
                btn.onmouseover = () => btn.style.opacity = '1';
                btn.onmouseout = () => btn.style.opacity = '0.7';
                
                btn.onclick = function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  
                  const postLink = post.querySelector('a[href*="/p/"], a[href*="/reel/"]');
                  if (postLink) {
                    window.opener.postMessage({
                      type: 'EXTRACT_POST',
                      url: postLink.href,
                      timestamp: Date.now()
                    }, '*');
                  }
                };
                
                post.style.position = 'relative';
                post.appendChild(btn);
                
                // إظهار الزر عند hover على المنشور
                post.onmouseover = () => btn.style.opacity = '1';
                post.onmouseout = () => btn.style.opacity = '0';
              }
            });
          }
          
          // تشغيل الدالة عند التحميل وعند التغيير
          addExtractionButtons();
          
          // مراقبة التغييرات
          const observer = new MutationObserver(addExtractionButtons);
          observer.observe(document.body, {
            childList: true,
            subtree: true
          });
          
          // إرسال معلومات الحساب
          const currentUrl = window.location.href;
          const usernameMatch = currentUrl.match(/instagram\\.com\\/([^\\/\\?]+)/);
          if (usernameMatch && usernameMatch[1] !== 'accounts') {
            window.opener.postMessage({
              type: 'ACCOUNT_DETECTED',
              username: usernameMatch[1],
              url: currentUrl,
              timestamp: Date.now()
            }, '*');
          }
        }
      `;
      
      popup.eval(script);
    } catch (error) {
      // لا يمكن حقن script بسبب CORS
      console.log('لا يمكن حقن script:', error);
    }
  };

  // مستمع الرسائل من النافذة المنبثقة
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://www.instagram.com') return;
      
      if (event.data.type === 'ACCOUNT_DETECTED') {
        const account = {
          username: event.data.username,
          name: event.data.username,
          profile_url: `https://www.instagram.com/${event.data.username}/`,
          detected_at: new Date().toISOString(),
          source: 'postmessage'
        };
        
        setCurrentAccount(account);
        addLog(`✅ تم اكتشاف الحساب: @${event.data.username}`);
        
        if (onAccountDetected) {
          onAccountDetected(account);
        }
      }
      
      if (event.data.type === 'EXTRACT_POST') {
        addLog(`🔍 طلب استخراج من: ${event.data.url}`);
        handlePostExtraction(event.data.url);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // معالجة استخراج المنشور
  const handlePostExtraction = async (postUrl: string) => {
    if (!currentAccount) {
      addLog('⚠️ لا يوجد حساب مكتشف للاستخراج');
      return;
    }

    try {
      setIsLoading(true);
      addLog(`🔄 بدء استخراج المعجبين من: ${postUrl}`);

      const response = await fetch('http://localhost:8000/api/automation/extract-likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          url: postUrl,
          account: currentAccount.username 
        }),
      });

      const data = await response.json();

      if (data.success) {
        addLog(`✅ تم استخراج ${data.extracted_count} معجب`);
        
        if (onPostExtraction) {
          onPostExtraction(postUrl, data.data);
        }
      } else {
        addLog(`❌ فشل الاستخراج: ${data.message}`);
      }
    } catch (error) {
      addLog(`❌ خطأ في الاستخراج: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة لوج
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString('ar');
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // تنظيف عند الإغلاق
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (popupWindow && !popupWindow.closed) {
        popupWindow.close();
      }
    };
  }, []);

  // إغلاق النافذة
  const closeWindow = () => {
    if (popupWindow && !popupWindow.closed) {
      popupWindow.close();
    }
    setIsConnected(false);
    setPopupWindow(null);
    if (onClose) onClose();
  };

  // التنقل في النافذة
  const navigateToUrl = (url: string) => {
    if (popupWindow && !popupWindow.closed) {
      popupWindow.location.href = url;
      addLog(`🔄 الانتقال إلى: ${url}`);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      {/* العنوان */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            📱 إدارة Instagram المتقدمة
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            فتح Instagram في نافذة منفصلة مع تكامل كامل
          </p>
        </div>
        
        {isConnected && (
          <button
            onClick={closeWindow}
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
          >
            ✕ إغلاق
          </button>
        )}
      </div>

      {/* حالة الاتصال */}
      <div className="mb-6 p-4 rounded-lg border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="font-medium text-gray-900 dark:text-white">
              {isConnected ? '🟢 متصل' : '🔴 غير متصل'}
            </span>
            
            {currentAccount && (
              <div className="flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/20 px-3 py-1 rounded-lg">
                <span className="text-blue-800 dark:text-blue-200 text-sm">
                  👤 @{currentAccount.username}
                </span>
              </div>
            )}
          </div>
          
          {isLoading && (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-blue-600 text-sm">جارٍ الاستخراج...</span>
            </div>
          )}
        </div>
      </div>

      {/* أزرار التحكم */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
        <button
          onClick={openInstagramWindow}
          disabled={isConnected}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            isConnected
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-purple-500 hover:bg-purple-600 text-white'
          }`}
        >
          🚀 فتح Instagram
        </button>
        
        <button
          onClick={() => navigateToUrl('https://www.instagram.com/')}
          disabled={!isConnected}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            !isConnected
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          🏠 الرئيسية
        </button>
        
        <button
          onClick={() => navigateToUrl('https://www.instagram.com/accounts/login/')}
          disabled={!isConnected}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            !isConnected
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          🔐 تسجيل دخول
        </button>
        
        <button
          onClick={() => setExtractionMode(!extractionMode)}
          disabled={!isConnected}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            !isConnected
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : extractionMode
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-orange-500 hover:bg-orange-600 text-white'
          }`}
        >
          {extractionMode ? '🔍 إيقاف الاستخراج' : '🔍 تفعيل الاستخراج'}
        </button>
      </div>

      {/* التعليمات */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">📋 التعليمات:</h4>
        <ol className="text-blue-800 dark:text-blue-200 text-sm space-y-1">
          <li>1. انقر "🚀 فتح Instagram" لفتح النافذة</li>
          <li>2. سجل دخولك في النافذة المفتوحة</li>
          <li>3. سيتم اكتشاف حسابك تلقائياً</li>
          <li>4. انقر "🔍 تفعيل الاستخراج" لإضافة أزرار الاستخراج</li>
          <li>5. تصفح Instagram وانقر "🔍 استخراج" على أي منشور</li>
        </ol>
      </div>

      {/* سجل العمليات */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">📝 سجل العمليات:</h4>
        <div className="space-y-1 max-h-40 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-sm">لا توجد عمليات بعد...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm text-gray-700 dark:text-gray-300 font-mono">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default InstagramPopupManager;
