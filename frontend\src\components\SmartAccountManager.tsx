import React, { useState, useEffect } from 'react';
import EmbeddedInstagram from './EmbeddedInstagram';

interface Account {
  id: number;
  name: string;
  username: string;
  email?: string;
  phone?: string;
  is_active: boolean;
  is_verified: boolean;
  last_used?: string;
  created_at: string;
  updated_at: string;
  notes?: string;
}

const SmartAccountManager: React.FC = () => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [showEmbeddedInstagram, setShowEmbeddedInstagram] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [detectedAccount, setDetectedAccount] = useState<any>(null);
  const [extractedPosts, setExtractedPosts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // تحميل الحسابات
  const loadAccounts = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/accounts');
      const data = await response.json();
      setAccounts(data);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // بدء الإضافة التلقائية
  const startAutomaticAddition = () => {
    setShowEmbeddedInstagram(true);
    setDetectedAccount(null);
  };

  // عند اكتشاف حساب في Instagram
  const handleAccountDetected = async (account: any) => {
    setDetectedAccount(account);
    
    // حفظ الحساب تلقائياً
    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:8000/api/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `حساب ${account.username}`,
          username: account.username,
          notes: `تم إضافته تلقائياً في ${new Date().toLocaleString('ar')} من Instagram مدمج`,
          is_active: true
        }),
      });

      if (response.ok) {
        const newAccount = await response.json();
        setAccounts(prev => [newAccount, ...prev]);
        setSelectedAccount(newAccount);
        
        // إشعار نجاح
        showSuccessNotification(`✅ تم حفظ حساب ${account.username} تلقائياً!`);
      }
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error);
      showErrorNotification('❌ فشل في حفظ الحساب');
    } finally {
      setIsLoading(false);
    }
  };

  // عند النقر على منشور للاستخراج
  const handlePostHover = async (postUrl: string) => {
    if (!selectedAccount) {
      showErrorNotification('⚠️ يرجى اختيار حساب أولاً');
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:8000/api/automation/extract-likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          url: postUrl,
          account_id: selectedAccount.id 
        }),
      });

      const data = await response.json();

      if (data.success) {
        const extractionResult = {
          id: Date.now(),
          post_url: postUrl,
          account: selectedAccount.username,
          likes: data.data,
          count: data.extracted_count,
          timestamp: new Date().toISOString()
        };

        setExtractedPosts(prev => [extractionResult, ...prev]);
        showSuccessNotification(`✅ تم استخراج ${data.extracted_count} معجب من المنشور`);
      } else {
        showErrorNotification(`❌ فشل الاستخراج: ${data.message}`);
      }
    } catch (error) {
      console.error('خطأ في استخراج المعجبين:', error);
      showErrorNotification('❌ خطأ في الاتصال بالخادم');
    } finally {
      setIsLoading(false);
    }
  };

  // إشعارات
  const showSuccessNotification = (message: string) => {
    // يمكن استبدالها بمكتبة إشعارات متقدمة
    alert(message);
  };

  const showErrorNotification = (message: string) => {
    alert(message);
  };

  // تصدير البيانات
  const exportData = (format: 'json' | 'csv', extractionId: number) => {
    const extraction = extractedPosts.find(e => e.id === extractionId);
    if (!extraction) return;

    if (format === 'json') {
      const dataStr = JSON.stringify(extraction.likes, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `instagram_likes_${extractionId}.json`;
      a.click();
    } else if (format === 'csv') {
      const csv = extraction.likes.map((like: any) => 
        `${like.username},${like.profile_url},${like.extracted_at}`
      ).join('\n');
      const blob = new Blob([`Username,Profile URL,Extracted At\n${csv}`], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `instagram_likes_${extractionId}.csv`;
      a.click();
    }
  };

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            🤖 إدارة الحسابات الذكية
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إضافة واستخدام حسابات Instagram بطريقة تلقائية ومدمجة
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={startAutomaticAddition}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105 shadow-lg"
          >
            🚀 إضافة حساب تلقائياً
          </button>
          
          <button
            onClick={() => setShowEmbeddedInstagram(false)}
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            📋 عرض الحسابات
          </button>
        </div>
      </div>

      {/* Instagram مدمج */}
      {showEmbeddedInstagram ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              📱 Instagram مدمج - إضافة واستخدام الحسابات
            </h3>
            <button
              onClick={() => setShowEmbeddedInstagram(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* معلومات الحساب المكتشف */}
          {detectedAccount && (
            <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-green-900 dark:text-green-100">
                    ✅ تم اكتشاف الحساب:
                  </h4>
                  <p className="text-green-800 dark:text-green-200">
                    👤 {detectedAccount.username} - تم حفظه تلقائياً
                  </p>
                </div>
                <div className="text-green-600 text-2xl">🎉</div>
              </div>
            </div>
          )}

          {/* اختيار الحساب للاستخراج */}
          {accounts.length > 0 && (
            <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🎯 اختر حساب للاستخراج:
              </h4>
              <select
                value={selectedAccount?.id || ''}
                onChange={(e) => {
                  const account = accounts.find(a => a.id === parseInt(e.target.value));
                  setSelectedAccount(account || null);
                }}
                className="w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
              >
                <option value="">اختر حساب...</option>
                {accounts.map(account => (
                  <option key={account.id} value={account.id}>
                    {account.name} (@{account.username})
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Instagram مدمج */}
          <div className="h-96 border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <EmbeddedInstagram
              onAccountDetected={handleAccountDetected}
              onPostHover={handlePostHover}
            />
          </div>

          {/* حالة التحميل */}
          {isLoading && (
            <div className="mt-4 flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                <span className="text-blue-800 dark:text-blue-200">جارٍ المعالجة...</span>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* عرض الحسابات والنتائج */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* قائمة الحسابات */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              👥 الحسابات المحفوظة ({accounts.length})
            </h3>
            
            {accounts.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">👥</div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  لا توجد حسابات بعد
                </h4>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  ابدأ بإضافة حساب Instagram الأول
                </p>
                <button
                  onClick={startAutomaticAddition}
                  className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  🚀 إضافة حساب الآن
                </button>
              </div>
            ) : (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {accounts.map(account => (
                  <div
                    key={account.id}
                    className={`p-4 border rounded-lg transition-all cursor-pointer ${
                      selectedAccount?.id === account.id
                        ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }`}
                    onClick={() => setSelectedAccount(account)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {account.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          @{account.username}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          أُضيف: {new Date(account.created_at).toLocaleDateString('ar')}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {account.is_verified && <span className="text-blue-500">✅</span>}
                        {selectedAccount?.id === account.id && (
                          <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 rounded-full">
                            مختار
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* نتائج الاستخراج */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              📊 نتائج الاستخراج ({extractedPosts.length})
            </h3>
            
            {extractedPosts.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">📊</div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  لا توجد عمليات استخراج بعد
                </h4>
                <p className="text-gray-500 dark:text-gray-400">
                  استخدم Instagram المدمج لاستخراج المعجبين
                </p>
              </div>
            ) : (
              <div className="space-y-4 max-h-80 overflow-y-auto">
                {extractedPosts.map(extraction => (
                  <div key={extraction.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {extraction.count} معجب
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          من: {extraction.account}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {extraction.post_url}
                        </p>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(extraction.timestamp).toLocaleTimeString('ar')}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={() => exportData('json', extraction.id)}
                        className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors"
                      >
                        📥 JSON
                      </button>
                      <button
                        onClick={() => exportData('csv', extraction.id)}
                        className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white text-xs rounded transition-colors"
                      >
                        📊 CSV
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartAccountManager;
