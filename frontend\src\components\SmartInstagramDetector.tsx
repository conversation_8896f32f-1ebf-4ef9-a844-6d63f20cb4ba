import React, { useState, useEffect, useRef } from 'react';

interface SmartInstagramDetectorProps {
  onAccountDetected?: (account: any) => void;
  onPostExtraction?: (postUrl: string, likes: any[]) => void;
  onClose?: () => void;
}

interface DetectedSession {
  window: Window;
  account: any;
  url: string;
  isActive: boolean;
  lastActivity: Date;
}

const SmartInstagramDetector: React.FC<SmartInstagramDetectorProps> = ({
  onAccountDetected,
  onPostExtraction,
  onClose
}) => {
  const [detectedSessions, setDetectedSessions] = useState<DetectedSession[]>([]);
  const [activeSession, setActiveSession] = useState<DetectedSession | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [extractionMode, setExtractionMode] = useState(false);
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // بدء فحص النوافذ المفتوحة
  const startSessionScan = async () => {
    setIsScanning(true);
    addLog('🔍 بدء فحص النوافذ المفتوحة...');

    try {
      // فحص النوافذ المفتوحة في المتصفح
      await scanExistingSessions();
      
      // إذا لم توجد جلسات، فتح نافذة جديدة
      if (detectedSessions.length === 0) {
        addLog('📱 لم توجد جلسات Instagram مفتوحة، فتح نافذة جديدة...');
        await openNewInstagramWindow();
      } else {
        addLog(`✅ تم العثور على ${detectedSessions.length} جلسة Instagram مفتوحة`);
      }
    } catch (error) {
      addLog(`❌ خطأ في فحص الجلسات: ${error}`);
    } finally {
      setIsScanning(false);
    }
  };

  // فحص الجلسات الموجودة
  const scanExistingSessions = async (): Promise<void> => {
    return new Promise((resolve) => {
      // محاولة الوصول للنوافذ المفتوحة
      const testWindows: Window[] = [];
      
      // فحص النوافذ المحتملة
      for (let i = 0; i < 10; i++) {
        try {
          // محاولة فتح نافذة بنفس الاسم للتحقق من وجودها
          const testWindow = window.open('', `instagram_window_${i}`, 'width=1,height=1');
          
          if (testWindow && testWindow.location) {
            try {
              // فحص إذا كانت النافذة تحتوي على Instagram
              if (testWindow.location.href.includes('instagram.com')) {
                testWindows.push(testWindow);
                addLog(`🔍 تم العثور على نافذة Instagram: ${testWindow.location.href}`);
              }
            } catch (corsError) {
              // CORS error يعني أن النافذة في domain مختلف (ربما Instagram)
              if (testWindow.name && testWindow.name.includes('instagram')) {
                testWindows.push(testWindow);
                addLog(`🔍 تم العثور على نافذة Instagram محتملة`);
              }
            }
          }
        } catch (error) {
          // تجاهل الأخطاء
        }
      }

      // معالجة النوافذ المكتشفة
      const sessions: DetectedSession[] = [];
      testWindows.forEach((win, index) => {
        const session: DetectedSession = {
          window: win,
          account: null,
          url: '',
          isActive: !win.closed,
          lastActivity: new Date()
        };

        // محاولة استخراج معلومات الحساب
        detectAccountInWindow(win, session);
        sessions.push(session);
      });

      setDetectedSessions(sessions);
      resolve();
    });
  };

  // اكتشاف الحساب في النافذة
  const detectAccountInWindow = async (window: Window, session: DetectedSession) => {
    try {
      // محاولة قراءة URL
      const url = window.location.href;
      session.url = url;

      // استخراج اسم المستخدم من URL
      const usernameMatch = url.match(/instagram\.com\/([^\/\?]+)/);
      if (usernameMatch && usernameMatch[1] !== 'accounts') {
        const username = usernameMatch[1];
        const account = {
          username: username,
          name: username,
          profile_url: `https://www.instagram.com/${username}/`,
          detected_at: new Date().toISOString(),
          source: 'existing_session',
          window_id: session.window.name || 'unnamed'
        };

        session.account = account;
        addLog(`✅ تم اكتشاف حساب في جلسة موجودة: @${username}`);

        if (onAccountDetected) {
          onAccountDetected(account);
        }
      }

      // حقن script للتواصل
      injectCommunicationScript(window, session);

    } catch (error) {
      // CORS error - استخدام طرق بديلة
      addLog(`🔄 استخدام طرق بديلة لاكتشاف الحساب في النافذة`);
      setupAlternativeDetection(window, session);
    }
  };

  // إعداد اكتشاف بديل للحسابات
  const setupAlternativeDetection = (window: Window, session: DetectedSession) => {
    // مراقبة تغييرات النافذة
    const checkInterval = setInterval(() => {
      try {
        if (window.closed) {
          clearInterval(checkInterval);
          session.isActive = false;
          return;
        }

        // محاولة قراءة العنوان
        const title = window.document?.title;
        if (title && title.includes('Instagram')) {
          addLog(`📱 تم اكتشاف نافذة Instagram نشطة`);
          
          // محاولة استخراج معلومات من العنوان
          const usernameFromTitle = title.match(/@([a-zA-Z0-9._]+)/);
          if (usernameFromTitle) {
            const username = usernameFromTitle[1];
            const account = {
              username: username,
              name: username,
              profile_url: `https://www.instagram.com/${username}/`,
              detected_at: new Date().toISOString(),
              source: 'title_detection'
            };

            session.account = account;
            addLog(`✅ تم اكتشاف حساب من العنوان: @${username}`);

            if (onAccountDetected) {
              onAccountDetected(account);
            }
          }
        }
      } catch (error) {
        // تجاهل أخطاء CORS
      }
    }, 3000);

    // تنظيف بعد 30 ثانية
    setTimeout(() => clearInterval(checkInterval), 30000);
  };

  // فتح نافذة Instagram جديدة
  const openNewInstagramWindow = async (): Promise<Window | null> => {
    const popup = window.open(
      'https://www.instagram.com/',
      'instagram_main_window',
      'width=1000,height=700,scrollbars=yes,resizable=yes,toolbar=yes,menubar=yes'
    );

    if (popup) {
      const session: DetectedSession = {
        window: popup,
        account: null,
        url: 'https://www.instagram.com/',
        isActive: true,
        lastActivity: new Date()
      };

      setDetectedSessions(prev => [...prev, session]);
      setActiveSession(session);
      
      addLog('✅ تم فتح نافذة Instagram جديدة');
      
      // بدء مراقبة النافذة الجديدة
      startWindowMonitoring(popup, session);
      
      return popup;
    } else {
      addLog('❌ فشل في فتح نافذة جديدة - تأكد من السماح للنوافذ المنبثقة');
      return null;
    }
  };

  // مراقبة النافذة
  const startWindowMonitoring = (window: Window, session: DetectedSession) => {
    const monitorInterval = setInterval(() => {
      try {
        if (window.closed) {
          session.isActive = false;
          clearInterval(monitorInterval);
          addLog('🔴 تم إغلاق النافذة');
          return;
        }

        // تحديث آخر نشاط
        session.lastActivity = new Date();

        // محاولة اكتشاف الحساب
        detectAccountInWindow(window, session);

        // حقن أزرار الاستخراج إذا كان الوضع مفعل
        if (extractionMode) {
          injectExtractionButtons(window);
        }

      } catch (error) {
        // تجاهل أخطاء CORS
      }
    }, 2000);

    // حفظ مرجع للتنظيف
    session.window.addEventListener('beforeunload', () => {
      clearInterval(monitorInterval);
    });
  };

  // حقن script التواصل
  const injectCommunicationScript = (window: Window, session: DetectedSession) => {
    try {
      const script = `
        if (!window.instagramSmartDetectorInjected) {
          window.instagramSmartDetectorInjected = true;
          
          // إرسال معلومات الحساب
          function sendAccountInfo() {
            const currentUrl = window.location.href;
            const usernameMatch = currentUrl.match(/instagram\\.com\\/([^\\/\\?]+)/);
            
            if (usernameMatch && usernameMatch[1] !== 'accounts' && usernameMatch[1] !== 'explore') {
              window.opener?.postMessage({
                type: 'ACCOUNT_DETECTED',
                username: usernameMatch[1],
                url: currentUrl,
                timestamp: Date.now(),
                source: 'script_injection'
              }, '*');
            }
          }
          
          // إرسال معلومات فورية
          sendAccountInfo();
          
          // مراقبة تغييرات URL
          let lastUrl = window.location.href;
          setInterval(() => {
            if (window.location.href !== lastUrl) {
              lastUrl = window.location.href;
              sendAccountInfo();
            }
          }, 1000);
        }
      `;

      window.eval(script);
      addLog('✅ تم حقن script التواصل');
    } catch (error) {
      addLog(`⚠️ لا يمكن حقن script: ${error}`);
    }
  };

  // حقن أزرار الاستخراج
  const injectExtractionButtons = (window: Window) => {
    try {
      const script = `
        if (!window.extractionButtonsInjected) {
          window.extractionButtonsInjected = true;
          
          function addExtractionButtons() {
            const posts = document.querySelectorAll('article');
            posts.forEach((post, index) => {
              if (!post.querySelector('.smart-extractor-btn')) {
                const btn = document.createElement('button');
                btn.className = 'smart-extractor-btn';
                btn.innerHTML = '🔍 استخراج ذكي';
                btn.style.cssText = \`
                  position: absolute !important;
                  top: 10px !important;
                  right: 10px !important;
                  background: linear-gradient(45deg, #667eea, #764ba2) !important;
                  color: white !important;
                  border: none !important;
                  padding: 8px 12px !important;
                  border-radius: 20px !important;
                  cursor: pointer !important;
                  font-size: 12px !important;
                  font-weight: bold !important;
                  z-index: 9999 !important;
                  opacity: 0 !important;
                  transition: all 0.3s !important;
                  box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
                \`;
                
                btn.onmouseover = () => {
                  btn.style.opacity = '1';
                  btn.style.transform = 'scale(1.05)';
                };
                btn.onmouseout = () => {
                  btn.style.opacity = '0.8';
                  btn.style.transform = 'scale(1)';
                };
                
                btn.onclick = function(e) {
                  e.preventDefault();
                  e.stopPropagation();
                  
                  const postLink = post.querySelector('a[href*="/p/"], a[href*="/reel/"]');
                  if (postLink) {
                    // تأثير بصري
                    btn.innerHTML = '⏳ جارٍ...';
                    btn.style.background = 'linear-gradient(45deg, #ffa726, #ff7043)';
                    
                    window.opener?.postMessage({
                      type: 'EXTRACT_POST',
                      url: postLink.href,
                      timestamp: Date.now(),
                      source: 'smart_extraction'
                    }, '*');
                    
                    // إعادة تعيين الزر بعد ثانيتين
                    setTimeout(() => {
                      btn.innerHTML = '✅ تم';
                      btn.style.background = 'linear-gradient(45deg, #66bb6a, #43a047)';
                      setTimeout(() => {
                        btn.innerHTML = '🔍 استخراج ذكي';
                        btn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                      }, 2000);
                    }, 2000);
                  }
                };
                
                post.style.position = 'relative';
                post.appendChild(btn);
                
                // إظهار الزر عند hover على المنشور
                post.onmouseover = () => btn.style.opacity = '1';
                post.onmouseout = () => btn.style.opacity = '0';
              }
            });
          }
          
          // تشغيل فوري
          addExtractionButtons();
          
          // مراقبة التغييرات
          const observer = new MutationObserver(addExtractionButtons);
          observer.observe(document.body, {
            childList: true,
            subtree: true
          });
        }
      `;

      window.eval(script);
      addLog('✅ تم حقن أزرار الاستخراج الذكية');
    } catch (error) {
      addLog(`⚠️ لا يمكن حقن أزرار الاستخراج: ${error}`);
    }
  };

  // مستمع الرسائل
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://www.instagram.com') return;

      if (event.data.type === 'ACCOUNT_DETECTED') {
        const account = {
          username: event.data.username,
          name: event.data.username,
          profile_url: `https://www.instagram.com/${event.data.username}/`,
          detected_at: new Date().toISOString(),
          source: event.data.source || 'postmessage'
        };

        addLog(`✅ تم اكتشاف حساب: @${event.data.username} (${event.data.source})`);

        if (onAccountDetected) {
          onAccountDetected(account);
        }
      }

      if (event.data.type === 'EXTRACT_POST') {
        addLog(`🔍 طلب استخراج ذكي من: ${event.data.url}`);
        handleSmartExtraction(event.data.url);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // معالجة الاستخراج الذكي
  const handleSmartExtraction = async (postUrl: string) => {
    try {
      addLog(`🔄 بدء الاستخراج الذكي من: ${postUrl}`);

      const response = await fetch('http://localhost:8000/api/automation/extract-likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          url: postUrl,
          extraction_type: 'smart_detection'
        }),
      });

      const data = await response.json();

      if (data.success) {
        addLog(`✅ تم استخراج ${data.extracted_count} معجب بنجاح`);
        
        if (onPostExtraction) {
          onPostExtraction(postUrl, data.data);
        }
      } else {
        addLog(`❌ فشل الاستخراج: ${data.message}`);
      }
    } catch (error) {
      addLog(`❌ خطأ في الاستخراج: ${error}`);
    }
  };

  // تفعيل وضع الاستخراج لجميع الجلسات
  const toggleExtractionMode = () => {
    setExtractionMode(!extractionMode);
    
    detectedSessions.forEach(session => {
      if (session.isActive && !session.window.closed) {
        if (!extractionMode) {
          injectExtractionButtons(session.window);
        }
      }
    });

    addLog(`${!extractionMode ? '✅ تم تفعيل' : '⏸️ تم إيقاف'} وضع الاستخراج الذكي`);
  };

  // إضافة لوج
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString('ar');
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  // تنظيف عند الإغلاق
  useEffect(() => {
    return () => {
      if (scanIntervalRef.current) {
        clearInterval(scanIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      {/* العنوان */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            🧠 كاشف Instagram الذكي
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            اكتشاف واستخدام الحسابات المفتوحة مسبقاً في المتصفح
          </p>
        </div>
        
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        )}
      </div>

      {/* إحصائيات الجلسات */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {detectedSessions.length}
          </div>
          <div className="text-sm text-blue-800 dark:text-blue-200">جلسات مكتشفة</div>
        </div>
        
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {detectedSessions.filter(s => s.isActive).length}
          </div>
          <div className="text-sm text-green-800 dark:text-green-200">جلسات نشطة</div>
        </div>
        
        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {detectedSessions.filter(s => s.account).length}
          </div>
          <div className="text-sm text-purple-800 dark:text-purple-200">حسابات مكتشفة</div>
        </div>
      </div>

      {/* أزرار التحكم */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
        <button
          onClick={startSessionScan}
          disabled={isScanning}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            isScanning
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {isScanning ? '🔄 جارٍ الفحص...' : '🔍 فحص الجلسات'}
        </button>
        
        <button
          onClick={() => openNewInstagramWindow()}
          className="py-2 px-4 bg-purple-500 hover:bg-purple-600 text-white rounded-lg font-medium transition-colors"
        >
          🚀 نافذة جديدة
        </button>
        
        <button
          onClick={toggleExtractionMode}
          className={`py-2 px-4 rounded-lg font-medium transition-colors ${
            extractionMode
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          {extractionMode ? '⏸️ إيقاف الاستخراج' : '🔍 تفعيل الاستخراج'}
        </button>
        
        <button
          onClick={() => setLogs([])}
          className="py-2 px-4 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"
        >
          🗑️ مسح السجل
        </button>
      </div>

      {/* الجلسات المكتشفة */}
      {detectedSessions.length > 0 && (
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            📱 الجلسات المكتشفة:
          </h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {detectedSessions.map((session, index) => (
              <div
                key={index}
                className={`p-3 border rounded-lg ${
                  session.isActive
                    ? 'border-green-200 bg-green-50 dark:bg-green-900/20'
                    : 'border-red-200 bg-red-50 dark:bg-red-900/20'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className={`w-2 h-2 rounded-full ${
                        session.isActive ? 'bg-green-500' : 'bg-red-500'
                      }`}></span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {session.account ? `@${session.account.username}` : 'غير محدد'}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      آخر نشاط: {session.lastActivity.toLocaleTimeString('ar')}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => {
                      if (!session.window.closed) {
                        session.window.focus();
                      }
                    }}
                    disabled={session.window.closed}
                    className="text-blue-500 hover:text-blue-700 text-sm"
                  >
                    🔍 تركيز
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* سجل العمليات */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-3">📝 سجل العمليات:</h4>
        <div className="space-y-1 max-h-60 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              انقر "🔍 فحص الجلسات" للبدء...
            </p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm text-gray-700 dark:text-gray-300 font-mono">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default SmartInstagramDetector;
