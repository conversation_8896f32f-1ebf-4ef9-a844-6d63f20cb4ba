import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

interface InstagramAccount {
  id: number;
  name: string;
  username: string;
  email?: string;
  phone?: string;
  has_2fa: boolean;
  two_fa_method?: string;
  is_active: boolean;
  is_verified: boolean;
  is_locked: boolean;
  login_attempts: number;
  last_used?: string;
  created_at: string;
  notes?: string;
  profile_pic_url?: string;
  follower_count: number;
  following_count: number;
}

interface TwoFASetup {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

const AdvancedAccountManager: React.FC = () => {
  const [accounts, setAccounts] = useState<InstagramAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showTwoFASetup, setShowTwoFASetup] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<InstagramAccount | null>(null);
  const [twoFAData, setTwoFAData] = useState<TwoFASetup | null>(null);
  const [verificationCode, setVerificationCode] = useState('');

  // بيانات النموذج
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    email: '',
    phone: '',
    has_2fa: false,
    two_fa_method: 'app',
    notes: ''
  });

  // تحميل الحسابات
  const loadAccounts = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8000/api/accounts');
      if (response.ok) {
        const data = await response.json();
        setAccounts(data);
      }
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // إضافة حساب جديد
  const handleAddAccount = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('http://localhost:8000/api/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const newAccount = await response.json();
        
        // إذا كان 2FA مفعل، إعداد المصادقة الثنائية
        if (formData.has_2fa) {
          await setup2FA(newAccount.id);
        } else {
          setAccounts([...accounts, newAccount]);
          setShowAddForm(false);
          resetForm();
        }
      } else {
        const error = await response.json();
        alert(`خطأ: ${error.detail}`);
      }
    } catch (error) {
      console.error('خطأ في إضافة الحساب:', error);
      alert('فشل في إضافة الحساب');
    }
  };

  // إعداد 2FA
  const setup2FA = async (accountId: number) => {
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}/setup-2fa`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setTwoFAData(data);
        setShowTwoFASetup(true);
        setSelectedAccount(accounts.find(acc => acc.id === accountId) || null);
      }
    } catch (error) {
      console.error('خطأ في إعداد 2FA:', error);
    }
  };

  // التحقق من 2FA وإكمال الإعداد
  const verify2FASetup = async () => {
    if (!selectedAccount || !verificationCode) return;

    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${selectedAccount.id}/verify-2fa-setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: verificationCode }),
      });

      if (response.ok) {
        alert('تم تفعيل المصادقة الثنائية بنجاح!');
        setShowTwoFASetup(false);
        setShowAddForm(false);
        setTwoFAData(null);
        setVerificationCode('');
        resetForm();
        loadAccounts();
      } else {
        alert('رمز التحقق غير صحيح');
      }
    } catch (error) {
      console.error('خطأ في التحقق من 2FA:', error);
    }
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      name: '',
      username: '',
      password: '',
      email: '',
      phone: '',
      has_2fa: false,
      two_fa_method: 'app',
      notes: ''
    });
  };

  // حذف حساب
  const deleteAccount = async (accountId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) return;

    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setAccounts(accounts.filter(acc => acc.id !== accountId));
      }
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error);
    }
  };

  // تبديل حالة الحساب
  const toggleAccountStatus = async (accountId: number, isActive: boolean) => {
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${accountId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: !isActive }),
      });

      if (response.ok) {
        loadAccounts();
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الحساب:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            إدارة الحسابات المتقدمة
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            إدارة حسابات Instagram مع دعم المصادقة الثنائية
          </p>
        </div>
        
        <Button
          onClick={() => setShowAddForm(true)}
          variant="primary"
          leftIcon={<span>➕</span>}
        >
          إضافة حساب جديد
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{accounts.length}</div>
              <div className="text-sm text-gray-600">إجمالي الحسابات</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {accounts.filter(acc => acc.is_active).length}
              </div>
              <div className="text-sm text-gray-600">حسابات نشطة</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {accounts.filter(acc => acc.has_2fa).length}
              </div>
              <div className="text-sm text-gray-600">مع 2FA</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {accounts.filter(acc => acc.is_verified).length}
              </div>
              <div className="text-sm text-gray-600">حسابات موثقة</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* قائمة الحسابات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {accounts.map((account) => (
          <Card key={account.id} variant="elevated">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {account.profile_pic_url ? (
                    <img
                      src={account.profile_pic_url}
                      alt={account.username}
                      className="w-12 h-12 rounded-full"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-xl">👤</span>
                    </div>
                  )}
                  
                  <div>
                    <CardTitle className="text-lg">{account.name}</CardTitle>
                    <p className="text-sm text-gray-600">@{account.username}</p>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  {account.is_verified && (
                    <Badge variant="success">✓ موثق</Badge>
                  )}
                  {account.has_2fa && (
                    <Badge variant="info">🔐 2FA</Badge>
                  )}
                  {!account.is_active && (
                    <Badge variant="error">معطل</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                {/* معلومات الحساب */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">المتابعين:</span>
                    <span className="font-medium ml-2">{account.follower_count.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">يتابع:</span>
                    <span className="font-medium ml-2">{account.following_count.toLocaleString()}</span>
                  </div>
                  {account.email && (
                    <div className="col-span-2">
                      <span className="text-gray-600">البريد:</span>
                      <span className="font-medium ml-2">{account.email}</span>
                    </div>
                  )}
                  {account.phone && (
                    <div className="col-span-2">
                      <span className="text-gray-600">الهاتف:</span>
                      <span className="font-medium ml-2">{account.phone}</span>
                    </div>
                  )}
                </div>

                {/* 2FA Info */}
                {account.has_2fa && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium text-blue-900">
                          المصادقة الثنائية مفعلة
                        </span>
                        <p className="text-xs text-blue-700">
                          الطريقة: {account.two_fa_method === 'app' ? 'تطبيق المصادقة' : account.two_fa_method}
                        </p>
                      </div>
                      <span className="text-blue-500 text-xl">🔐</span>
                    </div>
                  </div>
                )}

                {/* أزرار التحكم */}
                <div className="flex space-x-2 pt-3 border-t">
                  <Button
                    size="sm"
                    variant={account.is_active ? "error" : "success"}
                    onClick={() => toggleAccountStatus(account.id, account.is_active)}
                  >
                    {account.is_active ? "تعطيل" : "تفعيل"}
                  </Button>
                  
                  {!account.has_2fa && (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => setup2FA(account.id)}
                    >
                      تفعيل 2FA
                    </Button>
                  )}
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => deleteAccount(account.id)}
                  >
                    حذف
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* نموذج إضافة حساب */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>إضافة حساب Instagram جديد</CardTitle>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleAddAccount} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">اسم الحساب</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">اسم المستخدم</label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                    placeholder="بدون @"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">كلمة المرور</label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">رقم الهاتف</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="has_2fa"
                    checked={formData.has_2fa}
                    onChange={(e) => setFormData({...formData, has_2fa: e.target.checked})}
                    className="rounded"
                  />
                  <label htmlFor="has_2fa" className="text-sm font-medium">
                    تفعيل المصادقة الثنائية (2FA)
                  </label>
                </div>
                
                {formData.has_2fa && (
                  <div>
                    <label className="block text-sm font-medium mb-1">طريقة 2FA</label>
                    <select
                      value={formData.two_fa_method}
                      onChange={(e) => setFormData({...formData, two_fa_method: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                    >
                      <option value="app">تطبيق المصادقة (Google Authenticator)</option>
                      <option value="sms">رسائل SMS</option>
                      <option value="email">البريد الإلكتروني</option>
                    </select>
                  </div>
                )}
                
                <div>
                  <label className="block text-sm font-medium mb-1">ملاحظات</label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    className="w-full p-2 border rounded-lg"
                    rows={3}
                  />
                </div>
                
                <div className="flex space-x-3 pt-4">
                  <Button type="submit" variant="primary" fullWidth>
                    إضافة الحساب
                  </Button>
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={() => {
                      setShowAddForm(false);
                      resetForm();
                    }}
                    fullWidth
                  >
                    إلغاء
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}

      {/* نموذج إعداد 2FA */}
      {showTwoFASetup && twoFAData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-lg max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>إعداد المصادقة الثنائية</CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-6">
                {/* QR Code */}
                <div className="text-center">
                  <h3 className="text-lg font-medium mb-4">امسح الرمز بتطبيق المصادقة</h3>
                  <img
                    src={twoFAData.qr_code}
                    alt="QR Code"
                    className="mx-auto border rounded-lg"
                  />
                  <p className="text-sm text-gray-600 mt-2">
                    استخدم Google Authenticator أو أي تطبيق TOTP آخر
                  </p>
                </div>
                
                {/* Secret Key */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">المفتاح السري (للإدخال اليدوي):</h4>
                  <code className="text-sm bg-white p-2 rounded border block">
                    {twoFAData.secret}
                  </code>
                </div>
                
                {/* Backup Codes */}
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">رموز الاحتياط (احفظها في مكان آمن):</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                    {twoFAData.backup_codes.map((code, index) => (
                      <div key={index} className="bg-white p-2 rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-yellow-700 mt-2">
                    ⚠️ هذه الرموز تظهر مرة واحدة فقط. احفظها في مكان آمن!
                  </p>
                </div>
                
                {/* Verification */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    أدخل رمز التحقق من التطبيق:
                  </label>
                  <input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="w-full p-3 border rounded-lg text-center text-lg font-mono"
                    placeholder="000000"
                    maxLength={6}
                  />
                </div>
                
                <div className="flex space-x-3">
                  <Button
                    onClick={verify2FASetup}
                    variant="primary"
                    fullWidth
                    disabled={verificationCode.length !== 6}
                  >
                    تأكيد وتفعيل 2FA
                  </Button>
                  <Button
                    onClick={() => {
                      setShowTwoFASetup(false);
                      setTwoFAData(null);
                      setVerificationCode('');
                    }}
                    variant="secondary"
                    fullWidth
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AdvancedAccountManager;
