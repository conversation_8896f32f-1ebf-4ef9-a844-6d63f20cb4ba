import React, { useState } from 'react';
import Card, { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON>, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

interface InstagramAccount {
  id: number;
  name: string;
  username: string;
  has_2fa: boolean;
  two_fa_method?: string;
  is_active: boolean;
  is_verified: boolean;
  is_locked: boolean;
  login_attempts: number;
}

interface LoginStep {
  step: 'select_account' | 'enter_password' | 'enter_2fa' | 'success' | 'error';
  message?: string;
  error?: string;
}

interface InstagramLoginWith2FAProps {
  onLoginSuccess?: (account: InstagramAccount) => void;
  onClose?: () => void;
}

const InstagramLoginWith2FA: React.FC<InstagramLoginWith2FAProps> = ({
  onLoginSuccess,
  onClose
}) => {
  const [accounts, setAccounts] = useState<InstagramAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<InstagramAccount | null>(null);
  const [password, setPassword] = useState('');
  const [twoFACode, setTwoFACode] = useState('');
  const [loginStep, setLoginStep] = useState<LoginStep>({ step: 'select_account' });
  const [loading, setLoading] = useState(false);
  const [showBackupCodeInput, setShowBackupCodeInput] = useState(false);

  // تحميل الحسابات عند بدء التشغيل
  React.useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/accounts');
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.filter((acc: InstagramAccount) => acc.is_active && !acc.is_locked));
      }
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  // اختيار حساب
  const selectAccount = (account: InstagramAccount) => {
    setSelectedAccount(account);
    setLoginStep({ step: 'enter_password' });
  };

  // التحقق من كلمة المرور
  const verifyPassword = async () => {
    if (!selectedAccount || !password) return;

    setLoading(true);
    try {
      // في التطبيق الحقيقي، ستحتاج لـ API للتحقق من كلمة المرور
      // هنا سنفترض أن كلمة المرور صحيحة ونتقل للخطوة التالية
      
      if (selectedAccount.has_2fa) {
        setLoginStep({ 
          step: 'enter_2fa',
          message: `يرجى إدخال رمز التحقق من ${selectedAccount.two_fa_method === 'app' ? 'تطبيق المصادقة' : selectedAccount.two_fa_method}`
        });
      } else {
        // تسجيل دخول مباشر بدون 2FA
        handleLoginSuccess();
      }
    } catch (error) {
      setLoginStep({
        step: 'error',
        error: 'كلمة المرور غير صحيحة'
      });
    } finally {
      setLoading(false);
    }
  };

  // التحقق من 2FA
  const verify2FA = async () => {
    if (!selectedAccount || !twoFACode) return;

    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/api/accounts/${selectedAccount.id}/verify-2fa-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: twoFACode }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          handleLoginSuccess();
        } else {
          throw new Error(data.message || 'فشل في التحقق من 2FA');
        }
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'رمز التحقق غير صحيح');
      }
    } catch (error) {
      setLoginStep({
        step: 'error',
        error: error instanceof Error ? error.message : 'خطأ في التحقق من 2FA'
      });
    } finally {
      setLoading(false);
    }
  };

  // نجح تسجيل الدخول
  const handleLoginSuccess = () => {
    setLoginStep({
      step: 'success',
      message: `تم تسجيل الدخول بنجاح للحساب @${selectedAccount?.username}`
    });

    if (onLoginSuccess && selectedAccount) {
      onLoginSuccess(selectedAccount);
    }

    // إغلاق النافذة بعد 2 ثانية
    setTimeout(() => {
      if (onClose) onClose();
    }, 2000);
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setSelectedAccount(null);
    setPassword('');
    setTwoFACode('');
    setLoginStep({ step: 'select_account' });
    setShowBackupCodeInput(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>تسجيل الدخول إلى Instagram</CardTitle>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {/* اختيار الحساب */}
          {loginStep.step === 'select_account' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">اختر الحساب:</h3>
              
              {accounts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-600">لا توجد حسابات متاحة</p>
                  <Button
                    onClick={onClose}
                    variant="secondary"
                    className="mt-4"
                  >
                    إغلاق
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {accounts.map((account) => (
                    <div
                      key={account.id}
                      onClick={() => selectAccount(account)}
                      className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{account.name}</div>
                          <div className="text-sm text-gray-600">@{account.username}</div>
                        </div>
                        
                        <div className="flex space-x-2">
                          {account.is_verified && (
                            <Badge variant="success">✓</Badge>
                          )}
                          {account.has_2fa && (
                            <Badge variant="info">🔐</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* إدخال كلمة المرور */}
          {loginStep.step === 'enter_password' && selectedAccount && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium">تسجيل الدخول</h3>
                <p className="text-gray-600">@{selectedAccount.username}</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">كلمة المرور:</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 border rounded-lg"
                  placeholder="أدخل كلمة المرور"
                  onKeyPress={(e) => e.key === 'Enter' && verifyPassword()}
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={verifyPassword}
                  variant="primary"
                  fullWidth
                  disabled={!password || loading}
                  isLoading={loading}
                >
                  {selectedAccount.has_2fa ? 'التالي' : 'تسجيل الدخول'}
                </Button>
                <Button
                  onClick={resetForm}
                  variant="secondary"
                  fullWidth
                >
                  رجوع
                </Button>
              </div>
            </div>
          )}

          {/* إدخال رمز 2FA */}
          {loginStep.step === 'enter_2fa' && selectedAccount && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium">المصادقة الثنائية</h3>
                <p className="text-gray-600">@{selectedAccount.username}</p>
                {loginStep.message && (
                  <p className="text-sm text-blue-600 mt-2">{loginStep.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {showBackupCodeInput ? 'رمز الاحتياط:' : 'رمز التحقق:'}
                </label>
                <input
                  type="text"
                  value={twoFACode}
                  onChange={(e) => setTwoFACode(e.target.value)}
                  className="w-full p-3 border rounded-lg text-center text-lg font-mono"
                  placeholder={showBackupCodeInput ? "0000-0000" : "000000"}
                  maxLength={showBackupCodeInput ? 9 : 6}
                  onKeyPress={(e) => e.key === 'Enter' && verify2FA()}
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={verify2FA}
                  variant="primary"
                  fullWidth
                  disabled={!twoFACode || loading}
                  isLoading={loading}
                >
                  تحقق
                </Button>
                <Button
                  onClick={() => setLoginStep({ step: 'enter_password' })}
                  variant="secondary"
                  fullWidth
                >
                  رجوع
                </Button>
              </div>

              <div className="text-center">
                <button
                  onClick={() => {
                    setShowBackupCodeInput(!showBackupCodeInput);
                    setTwoFACode('');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {showBackupCodeInput ? 'استخدام تطبيق المصادقة' : 'استخدام رمز احتياط'}
                </button>
              </div>
            </div>
          )}

          {/* نجح تسجيل الدخول */}
          {loginStep.step === 'success' && (
            <div className="text-center py-8">
              <div className="text-green-500 text-6xl mb-4">✅</div>
              <h3 className="text-lg font-medium text-green-900">تم بنجاح!</h3>
              {loginStep.message && (
                <p className="text-green-700 mt-2">{loginStep.message}</p>
              )}
              <div className="mt-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">جاري فتح Instagram...</p>
              </div>
            </div>
          )}

          {/* خطأ */}
          {loginStep.step === 'error' && (
            <div className="text-center py-8">
              <div className="text-red-500 text-6xl mb-4">❌</div>
              <h3 className="text-lg font-medium text-red-900">فشل تسجيل الدخول</h3>
              {loginStep.error && (
                <p className="text-red-700 mt-2">{loginStep.error}</p>
              )}
              
              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={resetForm}
                  variant="primary"
                  fullWidth
                >
                  إعادة المحاولة
                </Button>
                <Button
                  onClick={onClose}
                  variant="secondary"
                  fullWidth
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InstagramLoginWith2FA;
