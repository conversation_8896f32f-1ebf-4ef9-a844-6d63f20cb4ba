import React, { useState, useRef } from 'react';
import Card, { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import InstagramLoginWith2FA from './InstagramLoginWith2FA';

interface InstagramAccount {
  id: number;
  name: string;
  username: string;
  has_2fa: boolean;
  two_fa_method?: string;
  is_active: boolean;
  is_verified: boolean;
  is_locked: boolean;
}

interface OpeningProgress {
  status: 'idle' | 'opening' | 'waiting_login' | 'verifying_2fa' | 'success' | 'error';
  message: string;
  progress: number;
  account?: InstagramAccount;
  error?: string;
}

interface InstagramOpenerWith2FAProps {
  onAccountOpened?: (account: InstagramAccount, window: Window) => void;
  onClose?: () => void;
}

const InstagramOpenerWith2FA: React.FC<InstagramOpenerWith2FAProps> = ({
  onAccountOpened,
  onClose
}) => {
  const [accounts, setAccounts] = useState<InstagramAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<InstagramAccount | null>(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [openingProgress, setOpeningProgress] = useState<OpeningProgress>({
    status: 'idle',
    message: 'اختر حساب لفتحه في Instagram',
    progress: 0
  });
  const [instagramWindow, setInstagramWindow] = useState<Window | null>(null);
  const windowRef = useRef<Window | null>(null);

  // تحميل الحسابات عند بدء التشغيل
  React.useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/accounts');
      if (response.ok) {
        const data = await response.json();
        setAccounts(data.filter((acc: InstagramAccount) => acc.is_active && !acc.is_locked));
      }
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  // فتح Instagram للحساب المحدد
  const openInstagramForAccount = async (account: InstagramAccount) => {
    setSelectedAccount(account);
    setOpeningProgress({
      status: 'opening',
      message: `جاري فتح Instagram للحساب @${account.username}...`,
      progress: 20,
      account
    });

    try {
      // فتح نافذة Instagram
      const newWindow = window.open(
        'https://www.instagram.com/',
        `instagram_${account.username}`,
        'width=1200,height=800,scrollbars=yes,resizable=yes'
      );

      if (!newWindow) {
        throw new Error('فشل في فتح النافذة - يرجى السماح بالنوافذ المنبثقة');
      }

      windowRef.current = newWindow;
      setInstagramWindow(newWindow);

      setOpeningProgress({
        status: 'waiting_login',
        message: 'تم فتح Instagram - يرجى تسجيل الدخول',
        progress: 40,
        account
      });

      // مراقبة النافذة
      monitorInstagramWindow(newWindow, account);

    } catch (error) {
      setOpeningProgress({
        status: 'error',
        message: 'فشل في فتح Instagram',
        progress: 0,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      });
    }
  };

  // مراقبة نافذة Instagram
  const monitorInstagramWindow = (window: Window, account: InstagramAccount) => {
    let checkCount = 0;
    const maxChecks = 120; // 6 دقائق

    const checkWindow = () => {
      try {
        checkCount++;

        if (window.closed) {
          setOpeningProgress({
            status: 'error',
            message: 'تم إغلاق النافذة قبل اكتمال تسجيل الدخول',
            progress: 0,
            error: 'Window closed'
          });
          return;
        }

        // تحديث رسالة التقدم
        setOpeningProgress(prev => ({
          ...prev,
          message: `مراقبة تسجيل الدخول... (${checkCount}/${maxChecks})`,
          progress: Math.min(40 + (checkCount / maxChecks) * 40, 80)
        }));

        // محاولة الوصول لمحتوى النافذة
        try {
          const url = window.location.href;
          
          // التحقق من تسجيل الدخول
          if (url.includes('instagram.com') && !url.includes('/accounts/login/')) {
            // تم تسجيل الدخول
            if (account.has_2fa) {
              // التحقق من 2FA إذا كان مفعل
              setOpeningProgress({
                status: 'verifying_2fa',
                message: 'تم تسجيل الدخول - جاري التحقق من المصادقة الثنائية...',
                progress: 90,
                account
              });
              
              // في التطبيق الحقيقي، ستحتاج للتحقق من 2FA هنا
              setTimeout(() => {
                handleLoginSuccess(window, account);
              }, 2000);
            } else {
              // تسجيل دخول مباشر بدون 2FA
              handleLoginSuccess(window, account);
            }
            return;
          }
        } catch (e) {
          // CORS restriction - طبيعي
        }

        // التحقق من انتهاء الوقت
        if (checkCount >= maxChecks) {
          setOpeningProgress({
            status: 'error',
            message: 'انتهت مهلة انتظار تسجيل الدخول',
            progress: 0,
            error: 'Timeout'
          });
          return;
        }

        // الاستمرار في المراقبة
        setTimeout(checkWindow, 3000);
      } catch (error) {
        console.error('خطأ في مراقبة النافذة:', error);
      }
    };

    setTimeout(checkWindow, 3000);
  };

  // نجح تسجيل الدخول
  const handleLoginSuccess = (window: Window, account: InstagramAccount) => {
    setOpeningProgress({
      status: 'success',
      message: `تم فتح Instagram بنجاح للحساب @${account.username}`,
      progress: 100,
      account
    });

    if (onAccountOpened) {
      onAccountOpened(account, window);
    }

    // إغلاق هذا المكون بعد 2 ثانية
    setTimeout(() => {
      if (onClose) onClose();
    }, 2000);
  };

  // فتح نموذج تسجيل الدخول المتقدم
  const openAdvancedLogin = () => {
    setShowLoginModal(true);
  };

  // نجح تسجيل الدخول من النموذج المتقدم
  const handleAdvancedLoginSuccess = (account: InstagramAccount) => {
    setShowLoginModal(false);
    openInstagramForAccount(account);
  };

  // إغلاق النافذة
  const closeInstagramWindow = () => {
    if (windowRef.current && !windowRef.current.closed) {
      windowRef.current.close();
    }
    setInstagramWindow(null);
    setOpeningProgress({
      status: 'idle',
      message: 'اختر حساب لفتحه في Instagram',
      progress: 0
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>فتح Instagram مع دعم 2FA</CardTitle>
              <p className="text-gray-600">فتح حسابات Instagram مع المصادقة الثنائية</p>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {/* شريط التقدم */}
          {openingProgress.status !== 'idle' && (
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  {openingProgress.message}
                </span>
                <span className="text-sm text-gray-500">
                  {openingProgress.progress}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    openingProgress.status === 'error' ? 'bg-red-500' :
                    openingProgress.status === 'success' ? 'bg-green-500' :
                    'bg-blue-500'
                  }`}
                  style={{ width: `${openingProgress.progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* قائمة الحسابات */}
          {openingProgress.status === 'idle' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">الحسابات المتاحة:</h3>
                <Button
                  onClick={openAdvancedLogin}
                  variant="secondary"
                  size="sm"
                >
                  تسجيل دخول متقدم
                </Button>
              </div>

              {accounts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-600">لا توجد حسابات متاحة</p>
                  <Button
                    onClick={() => window.location.href = '/accounts'}
                    variant="primary"
                    className="mt-4"
                  >
                    إضافة حساب
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {accounts.map((account) => (
                    <div
                      key={account.id}
                      className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <div className="font-medium">{account.name}</div>
                          <div className="text-sm text-gray-600">@{account.username}</div>
                        </div>
                        
                        <div className="flex space-x-2">
                          {account.is_verified && (
                            <Badge variant="success">✓</Badge>
                          )}
                          {account.has_2fa && (
                            <Badge variant="info">🔐</Badge>
                          )}
                        </div>
                      </div>

                      <Button
                        onClick={() => openInstagramForAccount(account)}
                        variant="primary"
                        size="sm"
                        fullWidth
                      >
                        فتح Instagram
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* حالة النجاح */}
          {openingProgress.status === 'success' && (
            <div className="text-center py-8">
              <div className="text-green-500 text-6xl mb-4">✅</div>
              <h3 className="text-lg font-medium text-green-900">تم بنجاح!</h3>
              <p className="text-green-700 mt-2">{openingProgress.message}</p>
              <div className="mt-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">جاري الإعداد...</p>
              </div>
            </div>
          )}

          {/* حالة الخطأ */}
          {openingProgress.status === 'error' && (
            <div className="text-center py-8">
              <div className="text-red-500 text-6xl mb-4">❌</div>
              <h3 className="text-lg font-medium text-red-900">فشل في فتح Instagram</h3>
              <p className="text-red-700 mt-2">{openingProgress.error}</p>
              
              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={() => {
                    setOpeningProgress({
                      status: 'idle',
                      message: 'اختر حساب لفتحه في Instagram',
                      progress: 0
                    });
                  }}
                  variant="primary"
                  fullWidth
                >
                  إعادة المحاولة
                </Button>
                <Button
                  onClick={onClose}
                  variant="secondary"
                  fullWidth
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          {(openingProgress.status === 'waiting_login' || openingProgress.status === 'verifying_2fa') && (
            <div className="flex space-x-3 mt-6">
              <Button
                onClick={closeInstagramWindow}
                variant="error"
                fullWidth
              >
                إغلاق النافذة
              </Button>
              
              {instagramWindow && (
                <Button
                  onClick={() => instagramWindow.focus()}
                  variant="secondary"
                  fullWidth
                >
                  التركيز على النافذة
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* نموذج تسجيل الدخول المتقدم */}
      {showLoginModal && (
        <InstagramLoginWith2FA
          onLoginSuccess={handleAdvancedLoginSuccess}
          onClose={() => setShowLoginModal(false)}
        />
      )}
    </div>
  );
};

export default InstagramOpenerWith2FA;
