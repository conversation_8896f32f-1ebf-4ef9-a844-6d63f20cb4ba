import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge, { StatusBadge, CountBadge, ProgressBadge } from '../ui/Badge';
import { apiService, DatabaseStats, Notification, Activity } from '../../services/api';

interface DashboardProps {
  onNavigate: (view: string) => void;
  backendConnected: boolean;
  accounts: any[];
  extractions: any[];
}

const ModernDashboard: React.FC<DashboardProps> = ({
  onNavigate,
  backendConnected,
  accounts,
  extractions,
}) => {
  const [stats, setStats] = useState({
    totalAccounts: 0,
    activeAccounts: 0,
    totalExtractions: 0,
    totalLikes: 0,
    todayExtractions: 0,
    successRate: 0,
    extractionTrend: 0,
    accountTrend: 0,
    averageLikesPerExtraction: 0,
  });

  const [recentActivity, setRecentActivity] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      if (backendConnected) {
        try {
          setLoading(true);
          setError(null);

          // تحميل الإحصائيات من الخادم
          const databaseStats = await apiService.getDashboardStatsFromSystem();
          setStats(databaseStats);

          // تحميل النشاط الأخير (محاكاة)
          const mockActivity: Activity[] = [
            {
              id: 1,
              action: 'account_added',
              description: 'تم إضافة حساب جديد',
              created_at: new Date().toISOString(),
              username: 'admin'
            },
            {
              id: 2,
              action: 'system_start',
              description: 'تم تشغيل النظام',
              created_at: new Date().toISOString(),
              username: 'system'
            }
          ];
          setRecentActivity(mockActivity);

        } catch (err) {
          console.error('خطأ في تحميل بيانات لوحة التحكم:', err);
          setError('فشل في تحميل البيانات من الخادم');
          loadLocalData();
        } finally {
          setLoading(false);
        }
      } else {
        loadLocalData();
      }
    };

    const loadLocalData = () => {
      // حساب الإحصائيات الحقيقية
      const activeAccounts = accounts.filter(acc => acc.is_active).length;
      const totalLikes = extractions.reduce((sum, ext) => sum + (ext.count || 0), 0);

      // حساب استخراجات اليوم
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const todayExtractions = extractions.filter(ext => {
        const extDate = new Date(ext.timestamp);
        return extDate >= todayStart;
      }).length;

      // حساب معدل النجاح الحقيقي
      const successfulExtractions = extractions.filter(ext => ext.count > 0).length;
      const successRate = extractions.length > 0
        ? Math.round((successfulExtractions / extractions.length) * 100)
        : 0;

      // حساب الاتجاهات (مقارنة مع الأسبوع الماضي)
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastWeekExtractions = extractions.filter(ext => {
        const extDate = new Date(ext.timestamp);
        return extDate >= lastWeek && extDate < todayStart;
      }).length;

      const thisWeekExtractions = extractions.filter(ext => {
        const extDate = new Date(ext.timestamp);
        return extDate >= todayStart;
      }).length;

      const extractionTrend = lastWeekExtractions > 0
        ? Math.round(((thisWeekExtractions - lastWeekExtractions) / lastWeekExtractions) * 100)
        : thisWeekExtractions > 0 ? 100 : 0;

      // حساب متوسط المعجبين لكل استخراج
      const averageLikesPerExtraction = extractions.length > 0
        ? Math.round(totalLikes / extractions.length)
        : 0;

      // حساب اتجاه الحسابات (مقارنة مع الشهر الماضي)
      const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      const recentAccounts = accounts.filter(acc => {
        const accDate = new Date(acc.created_at);
        return accDate >= lastMonth;
      }).length;
      const accountTrend = accounts.length > 0 ? Math.round((recentAccounts / accounts.length) * 100) : 0;

      setStats({
        total_accounts: accounts.length,
        active_accounts: activeAccounts,
        total_extractions: extractions.length,
        total_extracted_users: totalLikes,
        today_extractions: todayExtractions,
        success_rate: successRate,
      });

      // إعداد النشاط الأخير الحقيقي
      const activity = [
        ...accounts.slice(-5).map(acc => ({
          type: 'account',
          title: `تم إضافة حساب جديد`,
          description: `@${acc.username}`,
          time: acc.created_at,
          icon: '👤',
          color: 'blue',
        })),
        ...extractions.slice(-5).map(ext => ({
          type: 'extraction',
          title: `تم استخراج ${ext.count.toLocaleString()} معجب`,
          description: `من حساب ${ext.account}`,
          time: ext.timestamp,
          icon: '🔍',
          color: 'green',
        })),
      ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 8);

      setRecentActivity(activity);
      setLoading(false);
    };

    loadDashboardData();
  }, [accounts, extractions, backendConnected]);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    description: string;
    icon: string;
    color: string;
    trend?: { value: number; isPositive: boolean };
    onClick?: () => void;
  }> = ({ title, value, description, icon, color, trend, onClick }) => (
    <Card
      variant="gradient"
      hover={!!onClick}
      onClick={onClick}
      className="cursor-pointer group"
    >
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
              {title}
            </p>
            <p className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
              {value}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
            {trend && (
              <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                <span className="mr-1">
                  {trend.isPositive ? '↗️' : '↘️'}
                </span>
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
          <div className={`
            w-16 h-16 rounded-2xl flex items-center justify-center text-2xl
            bg-gradient-to-br ${color} shadow-lg group-hover:scale-110 transition-transform duration-300
          `}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const QuickActionCard: React.FC<{
    title: string;
    description: string;
    icon: string;
    color: string;
    action: string;
    onClick: () => void;
  }> = ({ title, description, icon, color, action, onClick }) => (
    <Card variant="glass" hover className="group">
      <CardContent>
        <div className="text-center">
          <div className={`
            w-20 h-20 mx-auto mb-4 rounded-2xl flex items-center justify-center text-3xl
            bg-gradient-to-br ${color} shadow-lg group-hover:scale-110 transition-transform duration-300
          `}>
            {icon}
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {description}
          </p>
          <Button onClick={onClick} variant="primary" size="sm" fullWidth>
            {action}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-xl text-gray-600 dark:text-gray-400">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong className="font-bold">خطأ: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* ترحيب وحالة النظام */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          مرحباً بك في Instagram Extractor
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
          النظام الذكي لاستخراج وإدارة معجبين Instagram
        </p>

        <div className="flex items-center justify-center space-x-4">
          <StatusBadge status={backendConnected ? 'online' : 'offline'} />
          <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
            النظام {backendConnected ? 'متصل وجاهز' : 'غير متصل'}
          </span>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <StatCard
          title="إجمالي الحسابات"
          value={stats.total_accounts}
          description={`${stats.active_accounts} نشط من ${stats.total_accounts}`}
          icon="👥"
          color="from-blue-500 to-blue-600"
          trend={{
            value: 15,
            isPositive: true
          }}
          onClick={() => onNavigate('accounts')}
        />

        <StatCard
          title="عمليات الاستخراج"
          value={stats.total_extractions}
          description={`${stats.today_extractions} اليوم`}
          icon="🔍"
          color="from-green-500 to-green-600"
          trend={{ value: 8, isPositive: true }}
          onClick={() => onNavigate('extraction')}
        />

        <StatCard
          title="إجمالي المستخدمين"
          value={stats.total_extracted_users.toLocaleString('ar')}
          description={`من ${stats.total_extractions} عملية استخراج`}
          icon="👥"
          color="from-pink-500 to-pink-600"
          trend={{
            value: 12,
            isPositive: true
          }}
        />

        <StatCard
          title="معدل النجاح"
          value={`${stats.success_rate}%`}
          description={`من ${stats.total_extractions} عملية`}
          icon="📊"
          color="from-purple-500 to-purple-600"
          trend={{
            value: stats.success_rate > 80 ? 5 : stats.success_rate > 60 ? 0 : -3,
            isPositive: stats.success_rate > 70
          }}
        />
      </div>

      {/* إجراءات سريعة */}
      <Card variant="default">
        <CardHeader>
          <CardTitle>🚀 إجراءات سريعة</CardTitle>
          <CardDescription>
            ابدأ بالمهام الأكثر استخداماً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <QuickActionCard
              title="كشف ذكي"
              description="اكتشف الحسابات المفتوحة في المتصفح"
              icon="🧠"
              color="from-blue-500 to-purple-600"
              action="بدء الكشف"
              onClick={() => onNavigate('smart')}
            />

            <QuickActionCard
              title="إضافة حساب"
              description="أضف حساب Instagram جديد"
              icon="➕"
              color="from-green-500 to-emerald-600"
              action="إضافة الآن"
              onClick={() => onNavigate('accounts')}
            />

            <QuickActionCard
              title="استخراج سريع"
              description="استخرج معجبين من رابط مباشر"
              icon="⚡"
              color="from-yellow-500 to-orange-600"
              action="استخراج"
              onClick={() => onNavigate('extraction')}
            />

            <QuickActionCard
              title="تصفح Instagram"
              description="تصفح Instagram مع أدوات الاستخراج"
              icon="🌐"
              color="from-pink-500 to-red-600"
              action="تصفح"
              onClick={() => onNavigate('browser')}
            />
          </div>
        </CardContent>
      </Card>

      {/* النشاط الأخير والتقدم */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* النشاط الأخير */}
        <Card variant="default">
          <CardHeader>
            <CardTitle>📋 النشاط الأخير</CardTitle>
            <CardDescription>
              آخر العمليات والأنشطة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">📝</div>
                <p className="text-gray-500 dark:text-gray-400">
                  لا يوجد نشاط حتى الآن
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={activity.id || index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className={`
                      w-10 h-10 rounded-full flex items-center justify-center text-lg
                      ${activity.action === 'account_added' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'}
                    `}>
                      {activity.action === 'account_added' ? '👤' : '⚙️'}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {activity.action === 'account_added' ? 'إضافة حساب' : 'نشاط النظام'}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {new Date(activity.created_at).toLocaleString('ar')} - {activity.username}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* معلومات النظام */}
        <Card variant="gradient">
          <CardHeader>
            <CardTitle>⚙️ معلومات النظام</CardTitle>
            <CardDescription>
              حالة وإعدادات النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">حالة Backend</span>
                <StatusBadge status={backendConnected ? 'online' : 'offline'} />
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">الحسابات النشطة</span>
                <ProgressBadge current={stats.active_accounts} total={stats.total_accounts} />
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">معدل النجاح</span>
                <Badge variant={stats.success_rate > 80 ? 'success' : stats.success_rate > 60 ? 'warning' : 'error'}>
                  {stats.success_rate}%
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">إصدار النظام</span>
                <Badge variant="info">v4.0</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* نصائح وإرشادات */}
      <Card variant="glass">
        <CardHeader>
          <CardTitle>💡 نصائح للاستخدام الأمثل</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <span className="text-2xl">🧠</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">استخدم الكشف الذكي</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  اكتشف الحسابات المفتوحة مسبقاً لتوفير الوقت
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">⚡</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">استخراج سريع</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  استخدم الروابط المباشرة للاستخراج السريع
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">📊</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">راقب الإحصائيات</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  تابع معدل النجاح وحسن الأداء
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">🔒</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">الأمان أولاً</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  استخدم حسابات آمنة ولا تشارك البيانات
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ModernDashboard;
