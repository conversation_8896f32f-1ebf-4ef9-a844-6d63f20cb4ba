import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge, { StatusBadge, CountBadge, ProgressBadge } from '../ui/Badge';

interface DashboardProps {
  onNavigate: (view: string) => void;
  backendConnected: boolean;
  accounts: any[];
  extractions: any[];
}

const ModernDashboard: React.FC<DashboardProps> = ({
  onNavigate,
  backendConnected,
  accounts,
  extractions,
}) => {
  const [stats, setStats] = useState({
    totalAccounts: 0,
    activeAccounts: 0,
    totalExtractions: 0,
    totalLikes: 0,
    todayExtractions: 0,
    successRate: 0,
  });

  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  useEffect(() => {
    // حساب الإحصائيات الحقيقية
    const activeAccounts = accounts.filter(acc => acc.is_active).length;
    const totalLikes = extractions.reduce((sum, ext) => sum + (ext.count || 0), 0);

    // حساب استخراجات اليوم
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayExtractions = extractions.filter(ext => {
      const extDate = new Date(ext.timestamp);
      return extDate >= todayStart;
    }).length;

    // حساب معدل النجاح الحقيقي
    const successfulExtractions = extractions.filter(ext => ext.count > 0).length;
    const successRate = extractions.length > 0
      ? Math.round((successfulExtractions / extractions.length) * 100)
      : 0;

    // حساب الاتجاهات (مقارنة مع الأسبوع الماضي)
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastWeekExtractions = extractions.filter(ext => {
      const extDate = new Date(ext.timestamp);
      return extDate >= lastWeek && extDate < todayStart;
    }).length;

    const thisWeekExtractions = extractions.filter(ext => {
      const extDate = new Date(ext.timestamp);
      return extDate >= todayStart;
    }).length;

    const extractionTrend = lastWeekExtractions > 0
      ? Math.round(((thisWeekExtractions - lastWeekExtractions) / lastWeekExtractions) * 100)
      : thisWeekExtractions > 0 ? 100 : 0;

    setStats({
      totalAccounts: accounts.length,
      activeAccounts,
      totalExtractions: extractions.length,
      totalLikes,
      todayExtractions,
      successRate,
      extractionTrend,
      accountTrend: accounts.length > 0 ? 12 : 0, // يمكن حسابه بناءً على تواريخ الإضافة
    });

    // إعداد النشاط الأخير الحقيقي
    const activity = [
      ...accounts.slice(-5).map(acc => ({
        type: 'account',
        title: `تم إضافة حساب جديد`,
        description: `@${acc.username}`,
        time: acc.created_at,
        icon: '👤',
        color: 'blue',
      })),
      ...extractions.slice(-5).map(ext => ({
        type: 'extraction',
        title: `تم استخراج ${ext.count.toLocaleString()} معجب`,
        description: `من حساب ${ext.account}`,
        time: ext.timestamp,
        icon: '🔍',
        color: 'green',
      })),
    ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 8);

    setRecentActivity(activity);
  }, [accounts, extractions]);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    description: string;
    icon: string;
    color: string;
    trend?: { value: number; isPositive: boolean };
    onClick?: () => void;
  }> = ({ title, value, description, icon, color, trend, onClick }) => (
    <Card
      variant="gradient"
      hover={!!onClick}
      onClick={onClick}
      className="cursor-pointer group"
    >
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
              {title}
            </p>
            <p className="text-3xl font-bold text-gray-900 dark:text-white mb-1">
              {value}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
            {trend && (
              <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                <span className="mr-1">
                  {trend.isPositive ? '↗️' : '↘️'}
                </span>
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
          <div className={`
            w-16 h-16 rounded-2xl flex items-center justify-center text-2xl
            bg-gradient-to-br ${color} shadow-lg group-hover:scale-110 transition-transform duration-300
          `}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const QuickActionCard: React.FC<{
    title: string;
    description: string;
    icon: string;
    color: string;
    action: string;
    onClick: () => void;
  }> = ({ title, description, icon, color, action, onClick }) => (
    <Card variant="glass" hover className="group">
      <CardContent>
        <div className="text-center">
          <div className={`
            w-20 h-20 mx-auto mb-4 rounded-2xl flex items-center justify-center text-3xl
            bg-gradient-to-br ${color} shadow-lg group-hover:scale-110 transition-transform duration-300
          `}>
            {icon}
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {description}
          </p>
          <Button onClick={onClick} variant="primary" size="sm" fullWidth>
            {action}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      {/* ترحيب وحالة النظام */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          مرحباً بك في Instagram Extractor
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
          النظام الذكي لاستخراج وإدارة معجبين Instagram
        </p>

        <div className="flex items-center justify-center space-x-4">
          <StatusBadge status={backendConnected ? 'online' : 'offline'} />
          <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
            النظام {backendConnected ? 'متصل وجاهز' : 'غير متصل'}
          </span>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <StatCard
          title="إجمالي الحسابات"
          value={stats.totalAccounts}
          description={`${stats.activeAccounts} نشط من ${stats.totalAccounts}`}
          icon="👥"
          color="from-blue-500 to-blue-600"
          trend={{ value: stats.accountTrend || 0, isPositive: (stats.accountTrend || 0) >= 0 }}
          onClick={() => onNavigate('accounts')}
        />

        <StatCard
          title="عمليات الاستخراج"
          value={stats.totalExtractions}
          description={`${stats.todayExtractions} اليوم`}
          icon="🔍"
          color="from-green-500 to-green-600"
          trend={{ value: stats.extractionTrend || 0, isPositive: (stats.extractionTrend || 0) >= 0 }}
          onClick={() => onNavigate('extraction')}
        />

        <StatCard
          title="إجمالي المعجبين"
          value={stats.totalLikes.toLocaleString('ar')}
          description="تم استخراجهم بنجاح"
          icon="❤️"
          color="from-pink-500 to-pink-600"
          trend={{
            value: stats.totalLikes > 0 ? Math.round((stats.totalLikes / stats.totalExtractions) * 0.1) : 0,
            isPositive: stats.totalLikes > 0
          }}
        />

        <StatCard
          title="معدل النجاح"
          value={`${stats.successRate}%`}
          description={`${extractions.filter(e => e.count > 0).length} من ${stats.totalExtractions} عملية`}
          icon="📊"
          color="from-purple-500 to-purple-600"
          trend={{
            value: stats.successRate > 80 ? 5 : stats.successRate > 60 ? 0 : -3,
            isPositive: stats.successRate > 70
          }}
        />
      </div>

      {/* إجراءات سريعة */}
      <Card variant="default">
        <CardHeader>
          <CardTitle>🚀 إجراءات سريعة</CardTitle>
          <CardDescription>
            ابدأ بالمهام الأكثر استخداماً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <QuickActionCard
              title="كشف ذكي"
              description="اكتشف الحسابات المفتوحة في المتصفح"
              icon="🧠"
              color="from-blue-500 to-purple-600"
              action="بدء الكشف"
              onClick={() => onNavigate('smart')}
            />

            <QuickActionCard
              title="إضافة حساب"
              description="أضف حساب Instagram جديد"
              icon="➕"
              color="from-green-500 to-emerald-600"
              action="إضافة الآن"
              onClick={() => onNavigate('accounts')}
            />

            <QuickActionCard
              title="استخراج سريع"
              description="استخرج معجبين من رابط مباشر"
              icon="⚡"
              color="from-yellow-500 to-orange-600"
              action="استخراج"
              onClick={() => onNavigate('extraction')}
            />

            <QuickActionCard
              title="تصفح Instagram"
              description="تصفح Instagram مع أدوات الاستخراج"
              icon="🌐"
              color="from-pink-500 to-red-600"
              action="تصفح"
              onClick={() => onNavigate('browser')}
            />
          </div>
        </CardContent>
      </Card>

      {/* النشاط الأخير والتقدم */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* النشاط الأخير */}
        <Card variant="default">
          <CardHeader>
            <CardTitle>📋 النشاط الأخير</CardTitle>
            <CardDescription>
              آخر العمليات والأنشطة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">📝</div>
                <p className="text-gray-500 dark:text-gray-400">
                  لا يوجد نشاط حتى الآن
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className={`
                      w-10 h-10 rounded-full flex items-center justify-center text-lg
                      ${activity.color === 'blue' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'}
                    `}>
                      {activity.icon}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {activity.title}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {new Date(activity.time).toLocaleString('ar')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* معلومات النظام */}
        <Card variant="gradient">
          <CardHeader>
            <CardTitle>⚙️ معلومات النظام</CardTitle>
            <CardDescription>
              حالة وإعدادات النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">حالة Backend</span>
                <StatusBadge status={backendConnected ? 'online' : 'offline'} />
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">الحسابات النشطة</span>
                <ProgressBadge current={stats.activeAccounts} total={stats.totalAccounts} />
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">معدل النجاح</span>
                <Badge variant={stats.successRate > 80 ? 'success' : stats.successRate > 60 ? 'warning' : 'error'}>
                  {stats.successRate}%
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-gray-900 dark:text-white">إصدار النظام</span>
                <Badge variant="info">v4.0</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* نصائح وإرشادات */}
      <Card variant="glass">
        <CardHeader>
          <CardTitle>💡 نصائح للاستخدام الأمثل</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <span className="text-2xl">🧠</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">استخدم الكشف الذكي</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  اكتشف الحسابات المفتوحة مسبقاً لتوفير الوقت
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">⚡</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">استخراج سريع</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  استخدم الروابط المباشرة للاستخراج السريع
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">📊</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">راقب الإحصائيات</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  تابع معدل النجاح وحسن الأداء
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <span className="text-2xl">🔒</span>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">الأمان أولاً</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  استخدم حسابات آمنة ولا تشارك البيانات
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ModernDashboard;
