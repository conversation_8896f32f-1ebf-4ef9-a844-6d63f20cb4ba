import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

interface InstagramSession {
  account_id: number;
  username: string;
  full_name?: string;
  browser: string;
  is_verified: boolean;
  follower_count: number;
  profile_pic_url?: string;
  last_used?: string;
}

interface DetectionStatus {
  status: 'running' | 'completed' | 'failed';
  progress: number;
  message: string;
  sessions?: InstagramSession[];
  error?: string;
}

interface BrowserStatus {
  status: 'connecting' | 'connected' | 'failed';
  username: string;
  message: string;
  current_url?: string;
  error?: string;
}

const AdvancedInstagramManager: React.FC = () => {
  const [activeSessions, setActiveSessions] = useState<InstagramSession[]>([]);
  const [detectionStatus, setDetectionStatus] = useState<DetectionStatus | null>(null);
  const [browserStatus, setBrowserStatus] = useState<BrowserStatus | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // تحميل الجلسات النشطة عند بدء التشغيل
  useEffect(() => {
    loadActiveSessions();
  }, []);

  const loadActiveSessions = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8001/api/sessions/active');
      const data = await response.json();
      
      if (data.success) {
        setActiveSessions(data.sessions);
      }
    } catch (error) {
      console.error('خطأ في تحميل الجلسات:', error);
    } finally {
      setLoading(false);
    }
  };

  const detectSessions = async () => {
    try {
      setIsDetecting(true);
      setDetectionStatus(null);
      
      // بدء عملية الكشف
      const response = await fetch('http://localhost:8001/api/sessions/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (data.success) {
        const detectionId = data.detection_id;
        
        // مراقبة حالة الكشف
        const checkStatus = async () => {
          try {
            const statusResponse = await fetch(
              `http://localhost:8001/api/sessions/detect/${detectionId}/status`
            );
            const statusData = await statusResponse.json();
            
            if (statusData.success) {
              setDetectionStatus(statusData.detection);
              
              if (statusData.detection.status === 'completed') {
                setIsDetecting(false);
                loadActiveSessions(); // إعادة تحميل الجلسات
              } else if (statusData.detection.status === 'failed') {
                setIsDetecting(false);
              } else {
                // الاستمرار في المراقبة
                setTimeout(checkStatus, 2000);
              }
            }
          } catch (error) {
            console.error('خطأ في مراقبة الكشف:', error);
            setIsDetecting(false);
          }
        };
        
        checkStatus();
      }
    } catch (error) {
      console.error('خطأ في بدء الكشف:', error);
      setIsDetecting(false);
    }
  };

  const connectToSession = async (username: string) => {
    try {
      setIsConnecting(true);
      setSelectedSession(username);
      setBrowserStatus(null);
      
      // بدء الاتصال
      const response = await fetch('http://localhost:8001/api/sessions/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        const browserId = data.browser_id;
        
        // مراقبة حالة الاتصال
        const checkBrowserStatus = async () => {
          try {
            const statusResponse = await fetch(
              `http://localhost:8001/api/sessions/browser/${browserId}/status`
            );
            const statusData = await statusResponse.json();
            
            if (statusData.success) {
              setBrowserStatus(statusData.browser);
              
              if (statusData.browser.status === 'connected') {
                setIsConnecting(false);
              } else if (statusData.browser.status === 'failed') {
                setIsConnecting(false);
              } else {
                // الاستمرار في المراقبة
                setTimeout(checkBrowserStatus, 2000);
              }
            }
          } catch (error) {
            console.error('خطأ في مراقبة الاتصال:', error);
            setIsConnecting(false);
          }
        };
        
        checkBrowserStatus();
      }
    } catch (error) {
      console.error('خطأ في الاتصال:', error);
      setIsConnecting(false);
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    if (!dateString) return 'غير محدد';
    
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'منذ لحظات';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `منذ ${days} يوم`;
    }
  };

  return (
    <div className="space-y-6">
      {/* عنوان الصفحة */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          🔗 إدارة Instagram المتقدمة
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          كشف وربط الحسابات المفتوحة تلقائياً مع استخراج الكوكيز
        </p>
      </div>

      {/* أزرار التحكم الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card variant="gradient">
          <CardContent>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-2xl">
                🔍
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                كشف الجلسات
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                البحث عن حسابات Instagram مفتوحة في المتصفحات
              </p>
              <Button 
                onClick={detectSessions}
                disabled={isDetecting}
                variant="primary"
                fullWidth
              >
                {isDetecting ? 'جاري الكشف...' : 'بدء الكشف'}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card variant="gradient">
          <CardContent>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center text-2xl">
                📱
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                الجلسات النشطة
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {activeSessions.length} جلسة محفوظة ومتاحة
              </p>
              <Button 
                onClick={loadActiveSessions}
                disabled={loading}
                variant="secondary"
                fullWidth
              >
                {loading ? 'جاري التحديث...' : 'تحديث القائمة'}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card variant="gradient">
          <CardContent>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-2xl">
                🧹
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                تنظيف الجلسات
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                حذف الجلسات المنتهية الصلاحية
              </p>
              <Button 
                onClick={() => {/* إضافة منطق التنظيف */}}
                variant="danger"
                fullWidth
              >
                تنظيف الجلسات
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* حالة الكشف */}
      {detectionStatus && (
        <Card variant="default">
          <CardHeader>
            <CardTitle>🔍 حالة عملية الكشف</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  التقدم
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {detectionStatus.progress}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${detectionStatus.progress}%` }}
                ></div>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {detectionStatus.message}
              </p>
              
              {detectionStatus.status === 'completed' && detectionStatus.sessions && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">
                    ✅ تم العثور على {detectionStatus.sessions.length} جلسة وحفظها بنجاح!
                  </p>
                </div>
              )}
              
              {detectionStatus.status === 'failed' && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 font-medium">
                    ❌ فشل في عملية الكشف: {detectionStatus.error}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* حالة الاتصال */}
      {browserStatus && (
        <Card variant="default">
          <CardHeader>
            <CardTitle>🔗 حالة الاتصال بالجلسة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  المستخدم: @{browserStatus.username}
                </span>
                <Badge variant={
                  browserStatus.status === 'connected' ? 'success' :
                  browserStatus.status === 'failed' ? 'error' : 'warning'
                }>
                  {browserStatus.status === 'connected' ? 'متصل' :
                   browserStatus.status === 'failed' ? 'فشل' : 'جاري الاتصال'}
                </Badge>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {browserStatus.message}
              </p>
              
              {browserStatus.current_url && (
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  الصفحة الحالية: {browserStatus.current_url}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* قائمة الجلسات النشطة */}
      <Card variant="default">
        <CardHeader>
          <CardTitle>📱 الجلسات المحفوظة</CardTitle>
          <CardDescription>
            الحسابات المكتشفة والمحفوظة مع الكوكيز
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeSessions.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">📱</div>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                لا توجد جلسات محفوظة
              </p>
              <p className="text-sm text-gray-400 dark:text-gray-500">
                استخدم "كشف الجلسات" للعثور على حسابات مفتوحة
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {activeSessions.map((session, index) => (
                <div 
                  key={index}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-500 to-red-500 flex items-center justify-center text-white font-bold">
                      {session.username.charAt(0).toUpperCase()}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          @{session.username}
                        </h4>
                        {session.is_verified && (
                          <span className="text-blue-500">✓</span>
                        )}
                      </div>
                      
                      {session.full_name && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {session.full_name}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>👥 {session.follower_count.toLocaleString()}</span>
                        <span>🌐 {session.browser}</span>
                      </div>
                      
                      {session.last_used && (
                        <p className="text-xs text-gray-400 mt-1">
                          آخر استخدام: {formatTimeAgo(session.last_used)}
                        </p>
                      )}
                      
                      <div className="mt-3 space-y-2">
                        <Button
                          onClick={() => connectToSession(session.username)}
                          disabled={isConnecting && selectedSession === session.username}
                          variant="primary"
                          size="sm"
                          fullWidth
                        >
                          {isConnecting && selectedSession === session.username 
                            ? 'جاري الاتصال...' 
                            : '🔗 فتح الجلسة'}
                        </Button>
                        
                        <Button
                          onClick={() => {/* إضافة منطق الاستخراج */}}
                          variant="secondary"
                          size="sm"
                          fullWidth
                        >
                          ⚡ استخراج سريع
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedInstagramManager;
