import React, { useState, useRef, useEffect } from 'react';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

interface HeaderProps {
  darkMode: boolean;
  onToggleDarkMode: () => void;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
  currentUser?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  notifications: Array<{
    id: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    time: string;
    read: boolean;
  }>;
  onMarkNotificationRead: (id: string) => void;
}

const Header: React.FC<HeaderProps> = ({
  darkMode,
  onToggleDarkMode,
  sidebarCollapsed,
  onToggleSidebar,
  currentUser = {
    name: 'المطور العربي',
    email: '<EMAIL>',
    role: 'مدير النظام'
  },
  notifications = [],
  onMarkNotificationRead,
}) => {
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const profileMenuRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const settingsRef = useRef<HTMLDivElement>(null);

  // إغلاق القوائم عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setShowProfileMenu(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const unreadNotifications = notifications.filter(n => !n.read).length;

  return (
    <header className={`
      fixed top-0 left-0 z-30 h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700
      shadow-sm transition-all duration-300 ease-in-out
      ${sidebarCollapsed ? 'right-20' : 'right-80'}
    `}>
      <div className="h-full px-6 flex items-center justify-between">

        {/* الجانب الأيسر - عنوان الصفحة والتنقل */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onToggleSidebar}
            className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors lg:hidden"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              لوحة التحكم الرئيسية
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              إدارة وتحليل عمليات استخراج Instagram
            </p>
          </div>
        </div>

        {/* الجانب الأيمن - أدوات المستخدم */}
        <div className="flex items-center space-x-6">

          {/* شريط البحث السريع */}
          <div className="hidden md:block relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              placeholder="بحث سريع..."
              className="w-64 pl-3 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500
                       focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
            />
          </div>

          {/* تبديل الوضع المظلم */}
          <button
            onClick={onToggleDarkMode}
            className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            title={darkMode ? 'تفعيل الوضع الفاتح' : 'تفعيل الوضع المظلم'}
          >
            {darkMode ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            )}
          </button>

          {/* الإشعارات */}
          <div className="relative" ref={notificationsRef}>
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="الإشعارات"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              {unreadNotifications > 0 && (
                <Badge
                  variant="error"
                  size="sm"
                  className="absolute -top-1 -left-1 min-w-5 h-5 flex items-center justify-center text-xs"
                >
                  {unreadNotifications > 9 ? '9+' : unreadNotifications}
                </Badge>
              )}
            </button>

            {/* قائمة الإشعارات */}
            {showNotifications && (
              <div className="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">الإشعارات</h3>
                    {unreadNotifications > 0 && (
                      <Badge variant="primary" size="sm">
                        {unreadNotifications} جديد
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="max-h-96 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="p-8 text-center">
                      <div className="text-4xl mb-2">🔔</div>
                      <p className="text-gray-500 dark:text-gray-400">لا توجد إشعارات</p>
                    </div>
                  ) : (
                    notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                          }`}
                        onClick={() => onMarkNotificationRead(notification.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-2 h-2 rounded-full mt-2 ${notification.type === 'success' ? 'bg-green-500' :
                              notification.type === 'warning' ? 'bg-yellow-500' :
                                notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                            }`} />
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                              {notification.title}
                            </h4>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                              {notification.message}
                            </p>
                            <p className="text-gray-500 dark:text-gray-500 text-xs mt-2">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {notifications.length > 0 && (
                  <div className="p-3 border-t border-gray-200 dark:border-gray-700">
                    <button className="w-full text-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium transition-colors">
                      عرض جميع الإشعارات
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* الإعدادات */}
          <div className="relative" ref={settingsRef}>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="الإعدادات"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>

            {/* قائمة الإعدادات */}
            {showSettings && (
              <div className="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">الإعدادات</h3>
                </div>

                <div className="p-2">
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">⚙️</span>
                    <span className="text-gray-700 dark:text-gray-300">إعدادات عامة</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">🔒</span>
                    <span className="text-gray-700 dark:text-gray-300">الخصوصية والأمان</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">🔔</span>
                    <span className="text-gray-700 dark:text-gray-300">إعدادات الإشعارات</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">🎨</span>
                    <span className="text-gray-700 dark:text-gray-300">المظهر والتخصيص</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">📊</span>
                    <span className="text-gray-700 dark:text-gray-300">إعدادات الاستخراج</span>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* البروفايل */}
          <div className="relative" ref={profileMenuRef}>
            <button
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {currentUser.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {currentUser.role}
                </div>
              </div>
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">
                  {currentUser.name.charAt(0)}
                </span>
              </div>
            </button>

            {/* قائمة البروفايل */}
            {showProfileMenu && (
              <div className="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-lg font-bold">
                        {currentUser.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {currentUser.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {currentUser.email}
                      </div>
                      <Badge variant="primary" size="sm" className="mt-1">
                        {currentUser.role}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="p-2">
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">👤</span>
                    <span className="text-gray-700 dark:text-gray-300">الملف الشخصي</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">📊</span>
                    <span className="text-gray-700 dark:text-gray-300">إحصائياتي</span>
                  </button>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="mr-3">🔑</span>
                    <span className="text-gray-700 dark:text-gray-300">تغيير كلمة المرور</span>
                  </button>
                  <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>
                  <button className="w-full flex items-center px-3 py-2 text-right rounded-lg hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors text-red-600 dark:text-red-400">
                    <span className="mr-3">🚪</span>
                    <span>تسجيل الخروج</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
