import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';
import Badge, { StatusBadge } from '../ui/Badge';

interface NavbarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  backendConnected: boolean;
  accountsCount: number;
  extractionsCount: number;
}

const Navbar: React.FC<NavbarProps> = ({
  currentView,
  onViewChange,
  backendConnected,
  accountsCount,
  extractionsCount,
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'لوحة التحكم',
      icon: '📊',
      description: 'نظرة عامة على النظام',
    },
    {
      id: 'smart',
      label: 'النظام الذكي',
      icon: '🧠',
      description: 'كشف واستخدام الحسابات',
      gradient: true,
    },
    {
      id: 'accounts',
      label: 'إدارة الحسابات',
      icon: '👥',
      description: 'إضافة وإدارة الحسابات',
      badge: accountsCount,
    },
    {
      id: 'extraction',
      label: 'استخراج المعجبين',
      icon: '🔍',
      description: 'استخراج قوائم المعجبين',
    },
    {
      id: 'browser',
      label: 'تصفح Instagram',
      icon: '🌐',
      description: 'تصفح مدمج لـ Instagram',
    },
  ];

  const NavItem: React.FC<{
    item: typeof navigationItems[0];
    isActive: boolean;
    onClick: () => void;
  }> = ({ item, isActive, onClick }) => (
    <button
      onClick={onClick}
      className={`
        relative group px-4 py-3 rounded-xl font-medium transition-all duration-300
        flex items-center space-x-3 min-w-0
        ${isActive
          ? item.gradient
            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
            : 'bg-blue-500 text-white shadow-lg'
          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
        }
        ${isActive ? 'scale-105' : 'hover:scale-105'}
      `}
    >
      <span className="text-xl flex-shrink-0">{item.icon}</span>
      <div className="flex-1 text-right min-w-0">
        <div className="font-semibold truncate">{item.label}</div>
        <div className={`text-xs truncate ${isActive ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'}`}>
          {item.description}
        </div>
      </div>
      
      {item.badge && item.badge > 0 && (
        <Badge variant="warning" size="sm">
          {item.badge}
        </Badge>
      )}
      
      {/* تأثير التمرير */}
      <div className={`
        absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300
        ${!isActive ? 'bg-gradient-to-r from-blue-500/10 to-purple-600/10' : ''}
      `} />
    </button>
  );

  return (
    <>
      {/* شريط التنقل الرئيسي */}
      <nav className={`
        fixed top-0 left-0 right-0 z-50 transition-all duration-300
        ${isScrolled 
          ? 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700' 
          : 'bg-white dark:bg-gray-900'
        }
      `}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            
            {/* الشعار والعنوان */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-2xl">📱</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    Instagram Extractor
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    النظام الذكي لاستخراج المعجبين
                  </p>
                </div>
              </div>
            </div>

            {/* عناصر التنقل - سطح المكتب */}
            <div className="hidden lg:flex items-center space-x-2">
              {navigationItems.map((item) => (
                <NavItem
                  key={item.id}
                  item={item}
                  isActive={currentView === item.id}
                  onClick={() => onViewChange(item.id)}
                />
              ))}
            </div>

            {/* معلومات الحالة */}
            <div className="flex items-center space-x-4">
              {/* حالة الاتصال */}
              <div className="hidden sm:flex items-center space-x-2">
                <StatusBadge status={backendConnected ? 'online' : 'offline'} />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {backendConnected ? 'متصل' : 'غير متصل'}
                </span>
              </div>

              {/* إحصائيات سريعة */}
              <div className="hidden md:flex items-center space-x-3">
                <div className="flex items-center space-x-1">
                  <span className="text-sm text-gray-500 dark:text-gray-400">حسابات:</span>
                  <Badge variant="primary" size="sm">{accountsCount}</Badge>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-sm text-gray-500 dark:text-gray-400">استخراجات:</span>
                  <Badge variant="success" size="sm">{extractionsCount}</Badge>
                </div>
              </div>

              {/* زر القائمة المحمولة */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* القائمة المحمولة */}
        {isMobileMenuOpen && (
          <div className="lg:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg">
            <div className="px-4 py-4 space-y-2">
              {navigationItems.map((item) => (
                <NavItem
                  key={item.id}
                  item={item}
                  isActive={currentView === item.id}
                  onClick={() => {
                    onViewChange(item.id);
                    setIsMobileMenuOpen(false);
                  }}
                />
              ))}
              
              {/* معلومات الحالة في القائمة المحمولة */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <StatusBadge status={backendConnected ? 'online' : 'offline'} />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      حالة الاتصال
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge variant="primary" size="sm">{accountsCount} حسابات</Badge>
                    <Badge variant="success" size="sm">{extractionsCount} استخراجات</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* مساحة فارغة لتجنب تداخل المحتوى */}
      <div className="h-20" />
    </>
  );
};

export default Navbar;
