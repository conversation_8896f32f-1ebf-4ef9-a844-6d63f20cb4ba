import React, { useState } from 'react';
import Badge, { StatusBadge } from '../ui/Badge';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  backendConnected: boolean;
  accountsCount: number;
  extractionsCount: number;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  currentView,
  onViewChange,
  backendConnected,
  accountsCount,
  extractionsCount,
  isCollapsed,
  onToggleCollapse,
}) => {
  const navigationItems = [
    {
      id: 'dashboard',
      label: 'لوحة التحكم',
      icon: '📊',
      description: 'نظرة عامة شاملة',
      color: 'text-blue-600 dark:text-blue-400',
    },
    {
      id: 'smart',
      label: 'النظام الذكي',
      icon: '🧠',
      description: 'كشف واستخدام الحسابات',
      color: 'text-purple-600 dark:text-purple-400',
      gradient: true,
    },
    {
      id: 'accounts',
      label: 'إدارة الحسابات',
      icon: '👥',
      description: 'إضافة وإدارة الحسابات',
      color: 'text-green-600 dark:text-green-400',
      badge: accountsCount,
    },
    {
      id: 'extraction',
      label: 'استخراج المعجبين',
      icon: '🔍',
      description: 'استخراج قوائم المعجبين',
      color: 'text-orange-600 dark:text-orange-400',
      badge: extractionsCount,
    },
    {
      id: 'browser',
      label: 'تصفح Instagram',
      icon: '🌐',
      description: 'تصفح مدمج لـ Instagram',
      color: 'text-pink-600 dark:text-pink-400',
    },
    {
      id: 'instagram-manager',
      label: 'إدارة Instagram المتقدمة',
      icon: '🔗',
      description: 'كشف وربط الحسابات المفتوحة',
      color: 'text-indigo-600 dark:text-indigo-400',
      gradient: true,
    },
  ];

  const NavItem: React.FC<{
    item: typeof navigationItems[0];
    isActive: boolean;
    onClick: () => void;
  }> = ({ item, isActive, onClick }) => (
    <button
      onClick={onClick}
      className={`
        group relative w-full flex items-center px-4 py-4 mb-3 rounded-xl
        font-medium transition-all duration-300 ease-in-out
        ${isActive
          ? item.gradient
            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'
            : 'bg-blue-500 text-white shadow-lg transform scale-105'
          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:scale-105'
        }
        ${isCollapsed ? 'justify-center' : 'justify-start'}
      `}
      title={isCollapsed ? item.label : ''}
    >
      {/* الأيقونة */}
      <span className={`text-xl flex-shrink-0 ${item.color} ${isActive ? 'text-white' : ''}`}>
        {item.icon}
      </span>

      {/* النص والوصف */}
      {!isCollapsed && (
        <div className="flex-1 text-right mr-3 min-w-0">
          <div className="font-semibold truncate">{item.label}</div>
          <div className={`text-xs truncate mt-0.5 ${isActive ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'
            }`}>
            {item.description}
          </div>
        </div>
      )}

      {/* الشارة */}
      {!isCollapsed && item.badge && item.badge > 0 && (
        <Badge
          variant={isActive ? 'gray' : 'primary'}
          size="sm"
          className="ml-2"
        >
          {item.badge}
        </Badge>
      )}

      {/* مؤشر النشاط */}
      {isActive && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full" />
      )}

      {/* تأثير التمرير */}
      <div className={`
        absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300
        ${!isActive ? 'bg-gradient-to-r from-blue-500/10 to-purple-600/10' : ''}
      `} />
    </button>
  );

  return (
    <div className={`
      fixed right-0 top-0 h-full bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700
      shadow-xl transition-all duration-300 ease-in-out z-40
      ${isCollapsed ? 'w-20' : 'w-80'}
    `}>
      {/* رأس الشريط الجانبي */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-2xl">📱</span>
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                  Instagram Extractor
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  النظام الذكي v4.0
                </p>
              </div>
            </div>
          )}

          <button
            onClick={onToggleCollapse}
            className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            title={isCollapsed ? 'توسيع الشريط الجانبي' : 'طي الشريط الجانبي'}
          >
            <svg
              className={`w-5 h-5 transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* حالة النظام */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {!isCollapsed ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                حالة النظام
              </span>
              <StatusBadge status={backendConnected ? 'online' : 'offline'} />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  {accountsCount}
                </div>
                <div className="text-xs text-blue-800 dark:text-blue-200">
                  حسابات
                </div>
              </div>

              <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                  {extractionsCount}
                </div>
                <div className="text-xs text-green-800 dark:text-green-200">
                  استخراجات
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-2">
            <StatusBadge status={backendConnected ? 'online' : 'offline'} showText={false} />
            <div className="text-xs text-center">
              <div className="text-blue-600 dark:text-blue-400 font-bold">{accountsCount}</div>
              <div className="text-green-600 dark:text-green-400 font-bold">{extractionsCount}</div>
            </div>
          </div>
        )}
      </div>

      {/* عناصر التنقل */}
      <div className="flex-1 overflow-y-auto p-4">
        <nav className="space-y-1">
          {navigationItems.map((item) => (
            <NavItem
              key={item.id}
              item={item}
              isActive={currentView === item.id}
              onClick={() => onViewChange(item.id)}
            />
          ))}
        </nav>
      </div>

      {/* تذييل الشريط الجانبي */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {!isCollapsed ? (
          <div className="space-y-2">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Instagram Extractor Professional
            </div>
            <div className="text-xs text-gray-400 dark:text-gray-500 text-center">
              الإصدار 4.0 - 2024
            </div>
            <div className="flex items-center justify-center space-x-1 text-xs">
              <span className="text-gray-500 dark:text-gray-400">مطور بـ</span>
              <span className="text-red-500">❤️</span>
              <span className="text-gray-500 dark:text-gray-400">للمطورين العرب</span>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-xs text-gray-400 dark:text-gray-500">v4.0</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
