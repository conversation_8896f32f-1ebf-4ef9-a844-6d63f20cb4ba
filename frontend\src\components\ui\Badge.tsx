import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'gray';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  icon?: React.ReactNode;
  pulse?: boolean;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  icon,
  pulse = false,
}) => {
  const baseClasses = `
    inline-flex items-center font-medium rounded-full
    transition-all duration-200 ease-in-out
    ${pulse ? 'animate-pulse' : ''}
  `;

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  const variantClasses = {
    primary: `
      bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
      border border-blue-200 dark:border-blue-800
    `,
    secondary: `
      bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400
      border border-purple-200 dark:border-purple-800
    `,
    success: `
      bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
      border border-green-200 dark:border-green-800
    `,
    warning: `
      bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
      border border-yellow-200 dark:border-yellow-800
    `,
    error: `
      bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
      border border-red-200 dark:border-red-800
    `,
    info: `
      bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400
      border border-cyan-200 dark:border-cyan-800
    `,
    gray: `
      bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
      border border-gray-200 dark:border-gray-800
    `,
  };

  const combinedClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <span className={combinedClasses}>
      {icon && <span className="mr-1">{icon}</span>}
      {children}
    </span>
  );
};

// مكونات شارات متخصصة
export const StatusBadge: React.FC<{
  status: 'online' | 'offline' | 'busy' | 'away';
  showText?: boolean;
}> = ({ status, showText = true }) => {
  const statusConfig = {
    online: { color: 'success', icon: '🟢', text: 'متصل' },
    offline: { color: 'gray', icon: '⚫', text: 'غير متصل' },
    busy: { color: 'error', icon: '🔴', text: 'مشغول' },
    away: { color: 'warning', icon: '🟡', text: 'غائب' },
  };

  const config = statusConfig[status];

  return (
    <Badge variant={config.color as any} icon={config.icon}>
      {showText ? config.text : ''}
    </Badge>
  );
};

export const CountBadge: React.FC<{
  count: number;
  max?: number;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}> = ({ count, max = 99, variant = 'primary' }) => {
  const displayCount = count > max ? `${max}+` : count.toString();
  
  return (
    <Badge variant={variant} size="sm">
      {displayCount}
    </Badge>
  );
};

export const ProgressBadge: React.FC<{
  current: number;
  total: number;
  variant?: 'primary' | 'success' | 'warning';
}> = ({ current, total, variant = 'primary' }) => {
  const percentage = Math.round((current / total) * 100);
  
  return (
    <Badge variant={variant}>
      {current}/{total} ({percentage}%)
    </Badge>
  );
};

export default Badge;
