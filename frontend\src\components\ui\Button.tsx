import React from 'react';
import { designTokens } from '../../styles/theme';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  gradient?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  gradient = true,
  className = '',
  disabled,
  ...props
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-semibold rounded-xl
    transition-all duration-200 ease-in-out transform
    focus:outline-none focus:ring-4 active:scale-95
    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
    ${fullWidth ? 'w-full' : ''}
  `;

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  };

  const variantClasses = {
    primary: gradient 
      ? `bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 
         text-white shadow-lg hover:shadow-xl hover:scale-105 focus:ring-blue-300`
      : `bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl 
         hover:scale-105 focus:ring-blue-300`,
    
    secondary: gradient
      ? `bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 
         text-white shadow-lg hover:shadow-xl hover:scale-105 focus:ring-purple-300`
      : `bg-purple-600 hover:bg-purple-700 text-white shadow-lg hover:shadow-xl 
         hover:scale-105 focus:ring-purple-300`,
    
    success: gradient
      ? `bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 
         text-white shadow-lg hover:shadow-xl hover:scale-105 focus:ring-green-300`
      : `bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl 
         hover:scale-105 focus:ring-green-300`,
    
    warning: gradient
      ? `bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 
         text-white shadow-lg hover:shadow-xl hover:scale-105 focus:ring-yellow-300`
      : `bg-yellow-600 hover:bg-yellow-700 text-white shadow-lg hover:shadow-xl 
         hover:scale-105 focus:ring-yellow-300`,
    
    error: gradient
      ? `bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 
         text-white shadow-lg hover:shadow-xl hover:scale-105 focus:ring-red-300`
      : `bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl 
         hover:scale-105 focus:ring-red-300`,
    
    ghost: `bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 
           text-gray-700 dark:text-gray-200 hover:scale-105 focus:ring-gray-300`,
    
    outline: `border-2 border-gray-300 dark:border-gray-600 bg-transparent 
             hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200 
             hover:scale-105 focus:ring-gray-300`,
  };

  const combinedClasses = `
    ${baseClasses}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <button
      className={combinedClasses}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          جارٍ التحميل...
        </>
      ) : (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      )}
    </button>
  );
};

export default Button;
