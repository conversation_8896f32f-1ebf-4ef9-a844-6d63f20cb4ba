import React from 'react';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'gradient' | 'bordered' | 'elevated';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  hover?: boolean;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  hover = false,
  onClick,
}) => {
  const baseClasses = `
    rounded-2xl transition-all duration-300 ease-in-out
    ${onClick ? 'cursor-pointer' : ''}
    ${hover ? 'hover:scale-[1.02] hover:shadow-2xl' : ''}
  `;

  const variantClasses = {
    default: `
      bg-white dark:bg-gray-800 shadow-xl border border-gray-200 
      dark:border-gray-700 backdrop-blur-sm
    `,
    glass: `
      bg-white/10 dark:bg-gray-800/10 backdrop-blur-md 
      border border-white/20 shadow-xl
    `,
    gradient: `
      bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 
      dark:to-gray-900 shadow-xl border border-gray-200 dark:border-gray-700
    `,
    bordered: `
      bg-white dark:bg-gray-800 border-2 border-gray-200 
      dark:border-gray-700 shadow-lg
    `,
    elevated: `
      bg-white dark:bg-gray-800 shadow-2xl border border-gray-200 
      dark:border-gray-700 transform translate-y-0 hover:-translate-y-1
    `,
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const combinedClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${paddingClasses[padding]}
    ${className}
  `;

  return (
    <div className={combinedClasses} onClick={onClick}>
      {children}
    </div>
  );
};

// مكونات فرعية للبطاقة
export const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`mb-6 ${className}`}>
    {children}
  </div>
);

export const CardTitle: React.FC<{
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ children, className = '', size = 'lg' }) => {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl',
  };

  return (
    <h3 className={`font-bold text-gray-900 dark:text-white ${sizeClasses[size]} ${className}`}>
      {children}
    </h3>
  );
};

export const CardDescription: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <p className={`text-gray-600 dark:text-gray-400 mt-2 ${className}`}>
    {children}
  </p>
);

export const CardContent: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`${className}`}>
    {children}
  </div>
);

export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => (
  <div className={`mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${className}`}>
    {children}
  </div>
);

export default Card;
