import React, { useEffect, useState } from 'react';

interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

const Toast: React.FC<ToastProps> = ({
  message,
  type,
  duration = 5000,
  onClose,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // إظهار التوست
    setIsVisible(true);

    // إخفاء التوست تلقائياً
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-500',
          border: 'border-green-600',
          text: 'text-white',
          icon: '✅',
          gradient: 'from-green-500 to-green-600'
        };
      case 'error':
        return {
          bg: 'bg-red-500',
          border: 'border-red-600',
          text: 'text-white',
          icon: '❌',
          gradient: 'from-red-500 to-red-600'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-500',
          border: 'border-yellow-600',
          text: 'text-white',
          icon: '⚠️',
          gradient: 'from-yellow-500 to-yellow-600'
        };
      case 'info':
        return {
          bg: 'bg-blue-500',
          border: 'border-blue-600',
          text: 'text-white',
          icon: 'ℹ️',
          gradient: 'from-blue-500 to-blue-600'
        };
      default:
        return {
          bg: 'bg-gray-500',
          border: 'border-gray-600',
          text: 'text-white',
          icon: '📝',
          gradient: 'from-gray-500 to-gray-600'
        };
    }
  };

  const getPositionStyles = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      default:
        return 'top-4 right-4';
    }
  };

  const styles = getTypeStyles();

  return (
    <div
      className={`
        fixed z-50 ${getPositionStyles()}
        transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving 
          ? 'opacity-100 translate-y-0 scale-100' 
          : 'opacity-0 translate-y-2 scale-95'
        }
      `}
    >
      <div className={`
        bg-gradient-to-r ${styles.gradient}
        ${styles.text} rounded-xl shadow-2xl border ${styles.border}
        p-4 min-w-80 max-w-md backdrop-blur-sm
        transform transition-all duration-300 ease-in-out
        hover:scale-105 hover:shadow-3xl
      `}>
        <div className="flex items-start space-x-3">
          {/* أيقونة */}
          <div className="flex-shrink-0">
            <span className="text-2xl">{styles.icon}</span>
          </div>
          
          {/* المحتوى */}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium leading-relaxed">
              {message}
            </p>
          </div>
          
          {/* زر الإغلاق */}
          <button
            onClick={handleClose}
            className="flex-shrink-0 ml-2 text-white/80 hover:text-white transition-colors duration-200"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* شريط التقدم */}
        <div className="mt-3 bg-white/20 rounded-full h-1 overflow-hidden">
          <div 
            className="h-full bg-white/60 rounded-full transition-all duration-linear"
            style={{
              animation: `shrink ${duration}ms linear forwards`
            }}
          />
        </div>
      </div>
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

// مدير الإشعارات المتعددة
interface ToastManagerProps {
  toasts: Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
  }>;
  onRemove: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export const ToastManager: React.FC<ToastManagerProps> = ({
  toasts,
  onRemove,
  position = 'top-right'
}) => {
  return (
    <div className={`fixed z-50 ${position.includes('top') ? 'top-4' : 'bottom-4'} ${
      position.includes('right') ? 'right-4' : 
      position.includes('left') ? 'left-4' : 
      'left-1/2 transform -translate-x-1/2'
    }`}>
      <div className="space-y-3">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            style={{
              animationDelay: `${index * 100}ms`
            }}
            className="animate-slideIn"
          >
            <Toast
              message={toast.message}
              type={toast.type}
              duration={toast.duration}
              onClose={() => onRemove(toast.id)}
              position={position}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

// Hook لاستخدام الإشعارات
export const useToast = () => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    duration?: number;
  }>>([]);

  const addToast = (
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    duration: number = 5000
  ) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { id, message, type, duration }]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearAllToasts = () => {
    setToasts([]);
  };

  return {
    toasts,
    addToast,
    removeToast,
    clearAllToasts,
    success: (message: string, duration?: number) => addToast(message, 'success', duration),
    error: (message: string, duration?: number) => addToast(message, 'error', duration),
    warning: (message: string, duration?: number) => addToast(message, 'warning', duration),
    info: (message: string, duration?: number) => addToast(message, 'info', duration),
  };
};

export default Toast;
