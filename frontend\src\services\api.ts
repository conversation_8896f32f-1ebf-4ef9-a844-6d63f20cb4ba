/**
 * خدمة API للتواصل مع Backend
 */

const API_BASE_URL = 'http://localhost:8000/api';

// أنواع البيانات الجديدة
export interface DatabaseStats {
  total_accounts: number;
  active_accounts: number;
  total_extractions: number;
  total_extracted_users: number;
  today_extractions: number;
  success_rate: number;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  category?: string;
  is_read: boolean;
  created_at: string;
}

export interface Activity {
  id: number;
  action: string;
  description: string;
  created_at: string;
  username?: string;
}

// أنواع البيانات
export interface InstagramAccount {
  id: number;
  name: string;
  username: string;
  email?: string;
  phone?: string;
  is_active: boolean;
  is_verified: boolean;
  last_used?: string;
  created_at: string;
  updated_at: string;
  notes?: string;
}

export interface InstagramAccountCreate {
  name: string;
  username: string;
  password: string;
  email?: string;
  phone?: string;
  is_active?: boolean;
  notes?: string;
}

export interface ExtractionJob {
  id: number;
  account_id: number;
  post_url: string;
  status: string;
  total_likes: number;
  extracted_count: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  created_at: string;
}

export interface ExtractedUser {
  id: number;
  job_id: number;
  username: string;
  full_name?: string;
  profile_pic_url?: string;
  profile_url?: string;
  is_verified: boolean;
  follower_count?: number;
  following_count?: number;
  post_count?: number;
  bio?: string;
  threads_url?: string;
  facebook_url?: string;
  extracted_at: string;
}

export interface SystemStats {
  total_accounts: number;
  active_accounts: number;
  total_jobs: number;
  total_extracted_users: number;
  jobs_today: number;
}

export interface ApiResponse<T> {
  data?: T;
  message?: string;
  success: boolean;
  error?: string;
}

// فئة خدمة API
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  // دالة مساعدة للطلبات
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, finalOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // ==================== إدارة الحسابات ====================

  async getAccounts(): Promise<InstagramAccount[]> {
    return this.request<InstagramAccount[]>('/accounts');
  }

  async getAccount(id: number): Promise<InstagramAccount> {
    return this.request<InstagramAccount>(`/accounts/${id}`);
  }

  async createAccount(account: InstagramAccountCreate): Promise<InstagramAccount> {
    return this.request<InstagramAccount>('/accounts', {
      method: 'POST',
      body: JSON.stringify(account),
    });
  }

  async updateAccount(id: number, updates: Partial<InstagramAccountCreate>): Promise<InstagramAccount> {
    return this.request<InstagramAccount>(`/accounts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteAccount(id: number): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/accounts/${id}`, {
      method: 'DELETE',
    });
  }

  // ==================== مهام الاستخراج ====================

  async startExtraction(accountId: number, postUrl: string): Promise<ExtractionJob> {
    return this.request<ExtractionJob>('/extract', {
      method: 'POST',
      body: JSON.stringify({
        account_id: accountId,
        post_url: postUrl,
      }),
    });
  }

  async getJobs(): Promise<ExtractionJob[]> {
    return this.request<ExtractionJob[]>('/jobs');
  }

  async getJob(id: number): Promise<ExtractionJob> {
    return this.request<ExtractionJob>(`/jobs/${id}`);
  }

  async getJobUsers(jobId: number): Promise<ExtractedUser[]> {
    return this.request<ExtractedUser[]>(`/jobs/${jobId}/users`);
  }

  // ==================== التصدير ====================

  async exportData(jobId: number, format: 'csv' | 'json' | 'excel'): Promise<{
    download_url: string;
    filename: string;
    file_size: number;
    format: string;
  }> {
    return this.request('/export', {
      method: 'POST',
      body: JSON.stringify({
        job_id: jobId,
        format: format,
      }),
    });
  }

  // ==================== الإحصائيات ====================

  async getSystemStats(): Promise<SystemStats> {
    return this.request<SystemStats>('/stats/system');
  }

  // تحويل إحصائيات النظام إلى إحصائيات قاعدة البيانات
  async getDashboardStatsFromSystem(): Promise<DatabaseStats> {
    try {
      const systemStats = await this.getSystemStats();
      return {
        total_accounts: systemStats.total_accounts,
        active_accounts: systemStats.active_accounts,
        total_extractions: systemStats.total_jobs,
        total_extracted_users: systemStats.total_extracted_users,
        today_extractions: systemStats.jobs_today,
        success_rate: systemStats.total_jobs > 0 ? 85 : 0 // معدل نجاح افتراضي
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات النظام:', error);
      return {
        total_accounts: 0,
        active_accounts: 0,
        total_extractions: 0,
        total_extracted_users: 0,
        today_extractions: 0,
        success_rate: 0
      };
    }
  }

  // ==================== WebSocket للتحديثات المباشرة ====================

  createWebSocket(jobId: number): WebSocket {
    const wsUrl = `ws://localhost:8000/ws/jobs/${jobId}`;
    return new WebSocket(wsUrl);
  }

  // ==================== دوال مساعدة ====================

  async testConnection(): Promise<boolean> {
    try {
      await this.request('/test');
      return true;
    } catch {
      return false;
    }
  }

  // الحصول على إحصائيات قاعدة البيانات
  async getDashboardStats(): Promise<DatabaseStats> {
    try {
      const response = await this.request<{ status: string, stats: DatabaseStats }>('/dashboard/stats');
      return response.stats;
    } catch (error) {
      console.error('خطأ في جلب إحصائيات قاعدة البيانات:', error);
      throw error;
    }
  }

  // الحصول على الإشعارات
  async getNotifications(unreadOnly: boolean = false): Promise<Notification[]> {
    try {
      const endpoint = `/notifications${unreadOnly ? '?unread_only=true' : ''}`;
      const response = await this.request<{ status: string, notifications: Notification[] }>(`${endpoint}`);
      return response.notifications;
    } catch (error) {
      console.error('خطأ في جلب الإشعارات:', error);
      throw error;
    }
  }

  // تحديد إشعار كمقروء
  async markNotificationRead(notificationId: number): Promise<void> {
    try {
      await this.request(`/notifications/${notificationId}/read`, {
        method: 'POST',
      });
    } catch (error) {
      console.error('خطأ في تحديث الإشعار:', error);
      throw error;
    }
  }

  // الحصول على النشاط الأخير
  async getRecentActivity(limit: number = 20): Promise<Activity[]> {
    try {
      const response = await this.request<{ status: string, activities: Activity[] }>(`/activity?limit=${limit}`);
      return response.activities;
    } catch (error) {
      console.error('خطأ في جلب النشاط الأخير:', error);
      throw error;
    }
  }

  // محاكاة البيانات للاختبار
  async simulateExtraction(postUrl: string): Promise<ExtractedUser[]> {
    // محاكاة تأخير
    await new Promise(resolve => setTimeout(resolve, 2000));

    return [
      {
        id: 1,
        job_id: 1,
        username: 'user_example_1',
        full_name: 'مستخدم تجريبي 1',
        profile_pic_url: 'https://via.placeholder.com/150x150/e1306c/ffffff?text=U1',
        profile_url: 'https://www.instagram.com/user_example_1/',
        is_verified: false,
        follower_count: 1250,
        following_count: 890,
        post_count: 45,
        bio: 'مستخدم تجريبي للاختبار 📸',
        threads_url: 'https://www.threads.net/@user_example_1',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      },
      {
        id: 2,
        job_id: 1,
        username: 'verified_user_2',
        full_name: 'مستخدم موثق',
        profile_pic_url: 'https://via.placeholder.com/150x150/833ab4/ffffff?text=V2',
        profile_url: 'https://www.instagram.com/verified_user_2/',
        is_verified: true,
        follower_count: 125000,
        following_count: 450,
        post_count: 320,
        bio: 'حساب موثق ⭐ | مؤثر رقمي',
        threads_url: 'https://www.threads.net/@verified_user_2',
        facebook_url: 'https://www.facebook.com/verified_user_2',
        extracted_at: new Date().toISOString()
      },
      {
        id: 3,
        job_id: 1,
        username: 'content_creator_3',
        full_name: 'صانع محتوى',
        profile_pic_url: 'https://via.placeholder.com/150x150/fd1d1d/ffffff?text=C3',
        profile_url: 'https://www.instagram.com/content_creator_3/',
        is_verified: false,
        follower_count: 25600,
        following_count: 1200,
        post_count: 156,
        bio: 'صانع محتوى | مصور فوتوغرافي 📷',
        threads_url: 'https://www.threads.net/@content_creator_3',
        facebook_url: null,
        extracted_at: new Date().toISOString()
      }
    ];
  }
}

// إنشاء مثيل واحد من الخدمة
export const apiService = new ApiService();

// دوال مساعدة للتصدير
export const exportToCSV = (data: ExtractedUser[], filename: string = 'instagram_likes') => {
  const headers = ['اسم المستخدم', 'الاسم الكامل', 'موثق', 'المتابعون', 'يتابع', 'المنشورات', 'البايو', 'رابط إنستجرام', 'رابط Threads'];
  const csvContent = [
    headers.join(','),
    ...data.map(user => [
      user.username,
      user.full_name || '',
      user.is_verified ? 'نعم' : 'لا',
      user.follower_count || 0,
      user.following_count || 0,
      user.post_count || 0,
      (user.bio || '').replace(/,/g, ';'),
      user.profile_url,
      user.threads_url || ''
    ].join(','))
  ].join('\n');

  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
};

export const exportToJSON = (data: ExtractedUser[], filename: string = 'instagram_likes') => {
  const jsonContent = JSON.stringify({
    exported_at: new Date().toISOString(),
    total_users: data.length,
    users: data
  }, null, 2);

  const blob = new Blob([jsonContent], { type: 'application/json' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${filename}_${new Date().toISOString().split('T')[0]}.json`;
  link.click();
};

export default apiService;
