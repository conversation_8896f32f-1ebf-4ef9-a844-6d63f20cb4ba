@tailwind base;
@tailwind components;
@tailwind utilities;

/* خطوط مخصصة */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* متغيرات CSS مخصصة */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --error-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
  --shadow-instagram: 0 8px 32px rgba(131, 58, 180, 0.3);
}

/* تحسينات الخط */
* {
  font-family: 'Inter', 'Cairo', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* تحسينات التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* تأثيرات الحركة المخصصة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* فئات مساعدة مخصصة */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.5s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.4s ease-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* تأثيرات الزجاج */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* تدرجات مخصصة */
.gradient-primary {
  background: var(--primary-gradient);
}

.gradient-secondary {
  background: var(--secondary-gradient);
}

.gradient-success {
  background: var(--success-gradient);
}

.gradient-warning {
  background: var(--warning-gradient);
}

.gradient-error {
  background: var(--error-gradient);
}

.gradient-instagram {
  background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
}

/* ظلال مخصصة */
.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.shadow-instagram {
  box-shadow: var(--shadow-instagram);
}

.shadow-soft {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-medium {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shadow-strong {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* تأثيرات التمرير */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* تأثيرات النص */
.text-gradient {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-instagram {
  background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-text {
    font-size: 0.875rem;
  }
}

/* تحسينات الوضع المظلم */
.dark {
  color-scheme: dark;
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-strong {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات التركيز */
.focus-ring {
  @apply focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50;
}

.focus-ring-purple {
  @apply focus:outline-none focus:ring-4 focus:ring-purple-300 focus:ring-opacity-50;
}

/* تحسينات الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-friendly {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
