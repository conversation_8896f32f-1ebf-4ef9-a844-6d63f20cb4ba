// نظام الألوان والتصميم الاحترافي
export const theme = {
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
    },
    secondary: {
      50: '#fdf4ff',
      100: '#fae8ff',
      200: '#f5d0fe',
      300: '#f0abfc',
      400: '#e879f9',
      500: '#d946ef',
      600: '#c026d3',
      700: '#a21caf',
      800: '#86198f',
      900: '#701a75',
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    instagram: {
      gradient: 'linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045)',
      purple: '#833ab4',
      pink: '#fd1d1d',
      orange: '#fcb045',
    }
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    glow: '0 0 20px rgb(59 130 246 / 0.5)',
    instagram: '0 8px 32px rgb(131 58 180 / 0.3)',
  },
  
  animations: {
    fadeIn: 'fadeIn 0.5s ease-in-out',
    slideUp: 'slideUp 0.3s ease-out',
    slideDown: 'slideDown 0.3s ease-out',
    pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    bounce: 'bounce 1s infinite',
    spin: 'spin 1s linear infinite',
    ping: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
  },
  
  gradients: {
    primary: 'bg-gradient-to-r from-blue-500 to-purple-600',
    secondary: 'bg-gradient-to-r from-purple-500 to-pink-500',
    success: 'bg-gradient-to-r from-green-400 to-blue-500',
    warning: 'bg-gradient-to-r from-yellow-400 to-orange-500',
    error: 'bg-gradient-to-r from-red-400 to-pink-500',
    instagram: 'bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400',
    glass: 'bg-gradient-to-br from-white/20 to-white/5',
    dark: 'bg-gradient-to-br from-gray-900 to-gray-800',
  },
  
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem',
    '2xl': '4rem',
    '3xl': '6rem',
  },
  
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.5rem',
    full: '9999px',
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
      arabic: ['Cairo', 'Tajawal', 'system-ui'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
};

// مكونات التصميم القابلة لإعادة الاستخدام
export const designTokens = {
  // أزرار
  button: {
    primary: `
      bg-gradient-to-r from-blue-500 to-purple-600 
      hover:from-blue-600 hover:to-purple-700 
      text-white font-semibold py-3 px-6 rounded-xl 
      shadow-lg hover:shadow-xl transform hover:scale-105 
      transition-all duration-200 ease-in-out
      focus:ring-4 focus:ring-blue-300 focus:outline-none
    `,
    secondary: `
      bg-gradient-to-r from-purple-500 to-pink-500 
      hover:from-purple-600 hover:to-pink-600 
      text-white font-semibold py-3 px-6 rounded-xl 
      shadow-lg hover:shadow-xl transform hover:scale-105 
      transition-all duration-200 ease-in-out
      focus:ring-4 focus:ring-purple-300 focus:outline-none
    `,
    success: `
      bg-gradient-to-r from-green-500 to-emerald-500 
      hover:from-green-600 hover:to-emerald-600 
      text-white font-semibold py-3 px-6 rounded-xl 
      shadow-lg hover:shadow-xl transform hover:scale-105 
      transition-all duration-200 ease-in-out
      focus:ring-4 focus:ring-green-300 focus:outline-none
    `,
    ghost: `
      bg-white/10 backdrop-blur-sm border border-white/20 
      hover:bg-white/20 text-gray-700 dark:text-gray-200 
      font-semibold py-3 px-6 rounded-xl 
      transition-all duration-200 ease-in-out
      focus:ring-4 focus:ring-gray-300 focus:outline-none
    `,
  },
  
  // بطاقات
  card: {
    default: `
      bg-white dark:bg-gray-800 rounded-2xl shadow-xl 
      border border-gray-200 dark:border-gray-700 
      backdrop-blur-sm transition-all duration-300 
      hover:shadow-2xl hover:scale-[1.02]
    `,
    glass: `
      bg-white/10 dark:bg-gray-800/10 rounded-2xl 
      backdrop-blur-md border border-white/20 
      shadow-xl transition-all duration-300 
      hover:bg-white/20 dark:hover:bg-gray-800/20
    `,
    gradient: `
      bg-gradient-to-br from-white to-gray-50 
      dark:from-gray-800 dark:to-gray-900 
      rounded-2xl shadow-xl border border-gray-200 
      dark:border-gray-700 transition-all duration-300 
      hover:shadow-2xl
    `,
  },
  
  // مدخلات
  input: {
    default: `
      w-full px-4 py-3 rounded-xl border border-gray-300 
      dark:border-gray-600 bg-white dark:bg-gray-800 
      text-gray-900 dark:text-white placeholder-gray-500 
      focus:ring-4 focus:ring-blue-300 focus:border-blue-500 
      transition-all duration-200 outline-none
    `,
    search: `
      w-full px-4 py-3 pl-12 rounded-xl border border-gray-300 
      dark:border-gray-600 bg-white dark:bg-gray-800 
      text-gray-900 dark:text-white placeholder-gray-500 
      focus:ring-4 focus:ring-blue-300 focus:border-blue-500 
      transition-all duration-200 outline-none
    `,
  },
  
  // شارات
  badge: {
    primary: `
      inline-flex items-center px-3 py-1 rounded-full text-xs 
      font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 
      dark:text-blue-400
    `,
    success: `
      inline-flex items-center px-3 py-1 rounded-full text-xs 
      font-medium bg-green-100 text-green-800 dark:bg-green-900/20 
      dark:text-green-400
    `,
    warning: `
      inline-flex items-center px-3 py-1 rounded-full text-xs 
      font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 
      dark:text-yellow-400
    `,
    error: `
      inline-flex items-center px-3 py-1 rounded-full text-xs 
      font-medium bg-red-100 text-red-800 dark:bg-red-900/20 
      dark:text-red-400
    `,
  },
};

export default theme;
