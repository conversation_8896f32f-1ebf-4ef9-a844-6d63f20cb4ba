@echo off
title تثبيت مكتبات المصادقة الثنائية (2FA)
color 0A

echo.
echo ========================================
echo 🔐 تثبيت مكتبات المصادقة الثنائية
echo ========================================
echo 📦 pyotp - TOTP/HOTP implementation
echo 🔒 cryptography - تشفير البيانات
echo 📱 qrcode - إنشاء QR codes
echo 🖼️ Pillow - معالجة الصور
echo ========================================
echo.

echo 📦 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🔐 تثبيت مكتبات المصادقة الثنائية...
echo.

echo 📦 تثبيت pyotp (TOTP/HOTP)...
python -m pip install pyotp
if errorlevel 1 (
    echo ❌ فشل في تثبيت pyotp
    pause
    exit /b 1
)
echo ✅ تم تثبيت pyotp

echo 🔒 تثبيت cryptography (التشفير)...
python -m pip install cryptography
if errorlevel 1 (
    echo ❌ فشل في تثبيت cryptography
    pause
    exit /b 1
)
echo ✅ تم تثبيت cryptography

echo 📱 تثبيت qrcode (QR codes)...
python -m pip install qrcode[pil]
if errorlevel 1 (
    echo ❌ فشل في تثبيت qrcode
    pause
    exit /b 1
)
echo ✅ تم تثبيت qrcode

echo 🖼️ تثبيت Pillow (معالجة الصور)...
python -m pip install Pillow
if errorlevel 1 (
    echo ❌ فشل في تثبيت Pillow
    pause
    exit /b 1
)
echo ✅ تم تثبيت Pillow

echo.
echo ========================================
echo ✅ تم تثبيت جميع مكتبات 2FA بنجاح!
echo ========================================
echo 🔐 pyotp - لإنشاء والتحقق من TOTP codes
echo 🔒 cryptography - لتشفير البيانات الحساسة
echo 📱 qrcode - لإنشاء QR codes للمصادقة
echo 🖼️ Pillow - لمعالجة صور QR codes
echo ========================================
echo.

echo 🧪 اختبار المكتبات...
echo.

echo 📝 اختبار pyotp...
python -c "import pyotp; print('✅ pyotp يعمل بشكل صحيح')"
if errorlevel 1 (
    echo ❌ مشكلة في pyotp
) else (
    echo ✅ pyotp يعمل بشكل صحيح
)

echo 📝 اختبار cryptography...
python -c "from cryptography.fernet import Fernet; print('✅ cryptography يعمل بشكل صحيح')"
if errorlevel 1 (
    echo ❌ مشكلة في cryptography
) else (
    echo ✅ cryptography يعمل بشكل صحيح
)

echo 📝 اختبار qrcode...
python -c "import qrcode; print('✅ qrcode يعمل بشكل صحيح')"
if errorlevel 1 (
    echo ❌ مشكلة في qrcode
) else (
    echo ✅ qrcode يعمل بشكل صحيح
)

echo 📝 اختبار Pillow...
python -c "from PIL import Image; print('✅ Pillow يعمل بشكل صحيح')"
if errorlevel 1 (
    echo ❌ مشكلة في Pillow
) else (
    echo ✅ Pillow يعمل بشكل صحيح
)

echo.
echo ========================================
echo 🧪 اختبار خدمة 2FA...
echo ========================================

echo 📝 اختبار إنشاء TOTP secret...
python -c "
import pyotp
secret = pyotp.random_base32()
print(f'✅ تم إنشاء secret: {secret[:10]}...')
"

echo 📝 اختبار إنشاء QR code...
python -c "
import pyotp
import qrcode
import io
import base64

secret = pyotp.random_base32()
totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
    name='test_user',
    issuer_name='Instagram Extractor'
)

qr = qrcode.QRCode(version=1, box_size=10, border=5)
qr.add_data(totp_uri)
qr.make(fit=True)

print('✅ تم إنشاء QR code بنجاح')
"

echo 📝 اختبار التشفير...
python -c "
from cryptography.fernet import Fernet
import json

key = Fernet.generate_key()
cipher = Fernet(key)

test_data = 'test_secret_data'
encrypted = cipher.encrypt(test_data.encode())
decrypted = cipher.decrypt(encrypted).decode()

if test_data == decrypted:
    print('✅ التشفير وفك التشفير يعمل بشكل صحيح')
else:
    print('❌ مشكلة في التشفير')
"

echo.
echo ========================================
echo 🎉 جميع الاختبارات نجحت!
echo ========================================
echo.

echo 🔧 تشغيل اختبار شامل لخدمة 2FA...
echo.

cd backend
python two_factor_auth.py
if errorlevel 1 (
    echo ❌ مشكلة في خدمة 2FA
    cd ..
    pause
    exit /b 1
) else (
    echo ✅ خدمة 2FA تعمل بشكل مثالي
)
cd ..

echo.
echo ========================================
echo 🎊 تم إعداد المصادقة الثنائية بنجاح!
echo ========================================
echo.

echo 🔐 الميزات المتاحة الآن:
echo ========================================
echo ✅ إنشاء TOTP secrets للحسابات
echo ✅ إنشاء QR codes للمصادقة
echo ✅ إنشاء backup codes احتياطية
echo ✅ تشفير البيانات الحساسة
echo ✅ التحقق من رموز المصادقة
echo ✅ إدارة متقدمة للحسابات مع 2FA
echo ========================================
echo.

echo 📱 تطبيقات المصادقة المدعومة:
echo ========================================
echo ✅ Google Authenticator
echo ✅ Microsoft Authenticator
echo ✅ Authy
echo ✅ 1Password
echo ✅ LastPass Authenticator
echo ✅ أي تطبيق TOTP آخر
echo ========================================
echo.

echo 🚀 الخطوات التالية:
echo ========================================
echo 1. شغل النظام: start_improved_system.bat
echo 2. افتح: http://localhost:3002
echo 3. اذهب إلى "إدارة الحسابات المتقدمة"
echo 4. أضف حساب جديد مع تفعيل 2FA
echo 5. امسح QR code بتطبيق المصادقة
echo 6. احفظ backup codes في مكان آمن
echo 7. استخدم "فتح Instagram مع دعم 2FA"
echo ========================================
echo.

echo 💡 نصائح مهمة:
echo ========================================
echo • احفظ backup codes في مكان آمن
echo • لا تشارك QR codes أو secrets مع أحد
echo • استخدم تطبيق مصادقة موثوق
echo • احتفظ بنسخة احتياطية من مفتاح التشفير
echo • راجع الحسابات المقفلة دورياً
echo ========================================
echo.

echo اضغط أي مفتاح للمتابعة...
pause >nul
