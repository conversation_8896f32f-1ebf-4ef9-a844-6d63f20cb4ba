# 🐛 سجل المشاكل المعروفة - Instagram Likes Extractor

## 📅 تاريخ آخر تحديث: 2024-06-01

---

## ✅ المشاكل المحلولة

### 1. مشكلة TailwindCSS PostCSS Plugin
**التاريخ**: 2024-06-01
**الوصف**: خطأ في تكوين TailwindCSS مع PostCSS
**الخطأ**:
```
[postcss] It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin.
The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS
with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.
```

**الحل النهائي**:
1. إلغاء تثبيت TailwindCSS v4 (غير مستقر):
   ```bash
   npm uninstall tailwindcss
   ```

2. تثبيت TailwindCSS v3 المستقر:
   ```bash
   npm install -D tailwindcss@^3.4.0 postcss autoprefixer
   ```

3. تحديث `tailwind.config.js` لاستخدام CommonJS:
   ```javascript
   module.exports = {
     content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
     // ... rest of config
   }
   ```

4. تحديث `postcss.config.js` لاستخدام CommonJS:
   ```javascript
   module.exports = {
     plugins: {
       tailwindcss: {},
       autoprefixer: {},
     },
   }
   ```

5. تغيير المنفذ في `vite.config.ts` (إذا كان 1420 مشغولاً):
   ```typescript
   server: {
     port: 1421,
     // ...
   }
   ```

**تحديث المشكلة**: ظهرت مرة أخرى بسبب ES modules vs CommonJS

**الحل النهائي الصحيح**:
1. المشروع يستخدم `"type": "module"` في package.json
2. يجب استخدام ES modules في جميع ملفات التكوين:

```javascript
// tailwind.config.js
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  // ... rest of config
}

// postcss.config.js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

**الحالة**: ✅ محلولة نهائياً ومؤكدة

---

## ⚠️ المشاكل الحالية

### 1. Rust غير مثبت
**التاريخ**: 2024-06-01
**الوصف**: Tauri يحتاج إلى Rust لتشغيل التطبيق المكتبي الكامل
**التأثير**: الواجهة الأمامية تعمل، لكن التطبيق المكتبي لا يعمل
**الحل المؤقت**: تشغيل الواجهة الأمامية فقط مع `npm run dev`
**الحل الدائم**: تثبيت Rust من https://rustup.rs/
**الحالة**: ⚠️ قيد الحل

### 2. pandas مطلوب لتصدير Excel
**التاريخ**: 2024-06-01
**الوصف**: تصدير Excel يحتاج إلى pandas و openpyxl
**التأثير**: تصدير CSV و JSON يعمل، Excel لا يعمل
**الحل**:
```bash
python -m pip install pandas openpyxl
```
**الحالة**: ⚠️ قيد الحل

---

## 🔄 مشاكل متوقعة

### 1. حظر إنستجرام
**الوصف**: إنستجرام قد يحظر الحسابات التي تستخدم الأتمتة
**الوقاية**:
- استخدام تأخير عشوائي
- عدم الإفراط في الاستخدام
- استخدام undetected-chromedriver
- تغيير User-Agent

### 2. تغييرات في واجهة إنستجرام
**الوصف**: إنستجرام يغير واجهته باستمرار
**الحل**: تحديث selectors في `instagram_extractor.py`

### 3. مشاكل Chrome Driver
**الوصف**: Chrome Driver قد يحتاج تحديث
**الحل**:
```bash
pip install --upgrade undetected-chromedriver
```

---

## 📊 إحصائيات المشاكل

| النوع | العدد | الحالة |
|-------|-------|---------|
| محلولة | 1 | ✅ |
| قيد الحل | 2 | ⚠️ |
| متوقعة | 3 | 🔄 |

---

## 🛠️ دليل استكشاف الأخطاء

### خطوات التشخيص الأساسية:

1. **تحقق من المتطلبات**:
   ```bash
   python --version
   node --version
   rustc --version  # اختياري
   ```

2. **تحقق من التبعيات**:
   ```bash
   npm list
   pip list
   ```

3. **تشغيل الاختبارات**:
   ```bash
   python test_extractor.py
   ```

4. **تحقق من اللوجات**:
   ```bash
   # تحقق من مجلد logs/
   dir logs  # Windows
   ls logs   # Linux/Mac
   ```

### رسائل الخطأ الشائعة:

#### "Module not found"
**السبب**: تبعية غير مثبتة
**الحل**: `pip install [package_name]`

#### "Command not found"
**السبب**: برنامج غير مثبت أو غير موجود في PATH
**الحل**: تثبيت البرنامج وإضافته للـ PATH

#### "Permission denied"
**السبب**: صلاحيات غير كافية
**الحل**: تشغيل كمدير أو تغيير الصلاحيات

---

## 📝 تسجيل مشكلة جديدة

عند مواجهة مشكلة جديدة، يرجى تسجيلها هنا مع:

1. **التاريخ والوقت**
2. **وصف المشكلة**
3. **رسالة الخطأ الكاملة**
4. **الخطوات المتبعة**
5. **البيئة** (نظام التشغيل، إصدارات البرامج)
6. **الحل المجرب**
7. **النتيجة**

### قالب تسجيل المشكلة:
```markdown
### [رقم المشكلة]. [عنوان المشكلة]
**التاريخ**: YYYY-MM-DD
**الوصف**: [وصف مفصل]
**الخطأ**:
```
[رسالة الخطأ]
```
**البيئة**:
- نظام التشغيل:
- Python:
- Node.js:
- المتصفح:

**الخطوات المجربة**:
1.
2.
3.

**الحل**: [إذا تم إيجاده]
**الحالة**: [🔄 قيد البحث / ⚠️ قيد الحل / ✅ محلولة]
```

---

## 🔗 روابط مفيدة

- [Tauri Documentation](https://tauri.app/)
- [TailwindCSS Documentation](https://tailwindcss.com/)
- [Selenium Documentation](https://selenium-python.readthedocs.io/)
- [undetected-chromedriver](https://github.com/ultrafunkamsterdam/undetected-chromedriver)
- [Instagram Terms of Service](https://help.instagram.com/581066165581870)

---

**ملاحظة**: هذا الملف يتم تحديثه باستمرار. يرجى مراجعته قبل الإبلاغ عن مشكلة جديدة.
