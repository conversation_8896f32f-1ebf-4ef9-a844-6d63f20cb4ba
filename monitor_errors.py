#!/usr/bin/env python3
"""
Error Monitor for Instagram Likes Extractor
مراقب الأخطاء التلقائي للتطبيق
"""

import os
import sys
import time
import json
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ErrorMonitor(FileSystemEventHandler):
    """مراقب الأخطاء التلقائي"""
    
    def __init__(self):
        self.log_dir = "logs"
        self.error_patterns = [
            "ERROR",
            "CRITICAL", 
            "FATAL",
            "Exception",
            "Traceback",
            "خطأ",
            "فشل",
            "❌"
        ]
        self.ensure_log_dir()
        self.error_count = 0
        self.last_errors = []
    
    def ensure_log_dir(self):
        """إنشاء مجلد اللوجات"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def on_modified(self, event):
        """عند تعديل ملف"""
        if event.is_directory:
            return
        
        if event.src_path.endswith('.log'):
            self.check_for_errors(event.src_path)
    
    def check_for_errors(self, file_path):
        """فحص الملف للبحث عن أخطاء"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # فحص آخر 10 أسطر فقط
            recent_lines = lines[-10:]
            
            for line in recent_lines:
                if any(pattern in line for pattern in self.error_patterns):
                    self.handle_error(line.strip(), file_path)
                    
        except Exception as e:
            print(f"خطأ في فحص الملف {file_path}: {e}")
    
    def handle_error(self, error_line, file_path):
        """معالجة خطأ تم اكتشافه"""
        self.error_count += 1
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        error_info = {
            "timestamp": timestamp,
            "file": os.path.basename(file_path),
            "error": error_line,
            "count": self.error_count
        }
        
        self.last_errors.append(error_info)
        
        # الاحتفاظ بآخر 50 خطأ فقط
        if len(self.last_errors) > 50:
            self.last_errors = self.last_errors[-50:]
        
        # طباعة الخطأ
        print(f"\n🚨 خطأ جديد تم اكتشافه #{self.error_count}")
        print(f"⏰ الوقت: {timestamp}")
        print(f"📁 الملف: {error_info['file']}")
        print(f"❌ الخطأ: {error_line}")
        print("-" * 50)
        
        # حفظ في ملف منفصل
        self.save_error_summary()
    
    def save_error_summary(self):
        """حفظ ملخص الأخطاء"""
        summary_file = os.path.join(self.log_dir, "error_summary.json")
        
        summary = {
            "last_updated": datetime.now().isoformat(),
            "total_errors": self.error_count,
            "recent_errors": self.last_errors[-10:],  # آخر 10 أخطاء
            "error_patterns": self.error_patterns
        }
        
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ ملخص الأخطاء: {e}")
    
    def get_stats(self):
        """إحصائيات الأخطاء"""
        return {
            "total_errors": self.error_count,
            "recent_errors_count": len(self.last_errors),
            "monitoring_since": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

def main():
    """الوظيفة الرئيسية"""
    print("🔍 بدء مراقبة الأخطاء...")
    print("📁 مراقبة مجلد: logs/")
    print("⏹️  للإيقاف: Ctrl+C")
    print("-" * 50)
    
    # إنشاء مراقب الأخطاء
    error_monitor = ErrorMonitor()
    
    # إعداد مراقب الملفات
    observer = Observer()
    observer.schedule(error_monitor, "logs", recursive=True)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
            
            # عرض إحصائيات كل دقيقة
            if int(time.time()) % 60 == 0:
                stats = error_monitor.get_stats()
                print(f"📊 إحصائيات: {stats['total_errors']} خطأ إجمالي")
                
    except KeyboardInterrupt:
        print("\n⏹️  إيقاف مراقبة الأخطاء...")
        observer.stop()
        
        # عرض الإحصائيات النهائية
        stats = error_monitor.get_stats()
        print(f"\n📊 الإحصائيات النهائية:")
        print(f"   - إجمالي الأخطاء: {stats['total_errors']}")
        print(f"   - الأخطاء الحديثة: {stats['recent_errors_count']}")
        
        if error_monitor.last_errors:
            print(f"\n🔍 آخر الأخطاء:")
            for error in error_monitor.last_errors[-5:]:
                print(f"   - [{error['timestamp']}] {error['error'][:100]}...")
    
    observer.join()

if __name__ == "__main__":
    # التحقق من وجود watchdog
    try:
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
    except ImportError:
        print("❌ مكتبة watchdog غير مثبتة")
        print("💡 لتثبيتها: pip install watchdog")
        sys.exit(1)
    
    main()
