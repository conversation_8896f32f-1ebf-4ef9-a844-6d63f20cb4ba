@echo off
title إعادة تشغيل المشروع الكامل - Instagram Extractor
color 0A

echo.
echo ========================================
echo 🔄 إعادة تشغيل المشروع الكامل
echo ========================================
echo 🧠 كاشف Instagram الذكي
echo 👥 إدارة الحسابات مع 2FA
echo 🔍 الكشف التلقائي المحسن
echo 💾 حفظ الحسابات المضمون
echo ========================================
echo.

echo 🛑 إيقاف العمليات السابقة...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im node.exe >nul 2>&1
echo ✅ تم إيقاف العمليات السابقة

echo.
echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت/تحديث مكتبات Python...
python -m pip install --upgrade pip >nul 2>&1
python -m pip install fastapi uvicorn sqlalchemy pyotp cryptography qrcode[pil] Pillow flask flask-cors --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تحديث مكتبات Python

echo 🗄️ إعادة تهيئة قاعدة البيانات...
cd backend
python -c "
import os
from database import init_database
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

print('🔄 إعادة تهيئة قاعدة البيانات...')

# إنشاء قاعدة البيانات
init_database()

# التحقق من الجداول وإضافة الأعمدة الجديدة
try:
    engine = create_engine('sqlite:///instagram_extractor.db')
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # إضافة الأعمدة الجديدة للـ 2FA إذا لم تكن موجودة
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN has_2fa BOOLEAN DEFAULT 0'))
        print('✅ تم إضافة عمود has_2fa')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_secret TEXT'))
        print('✅ تم إضافة عمود two_fa_secret')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_backup_codes TEXT'))
        print('✅ تم إضافة عمود two_fa_backup_codes')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_method TEXT'))
        print('✅ تم إضافة عمود two_fa_method')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN is_locked BOOLEAN DEFAULT 0'))
        print('✅ تم إضافة عمود is_locked')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN login_attempts INTEGER DEFAULT 0'))
        print('✅ تم إضافة عمود login_attempts')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN last_login_attempt DATETIME'))
        print('✅ تم إضافة عمود last_login_attempt')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN profile_pic_url TEXT'))
        print('✅ تم إضافة عمود profile_pic_url')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN follower_count INTEGER DEFAULT 0'))
        print('✅ تم إضافة عمود follower_count')
    except:
        pass
    
    try:
        session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN following_count INTEGER DEFAULT 0'))
        print('✅ تم إضافة عمود following_count')
    except:
        pass
    
    session.commit()
    session.close()
    print('✅ تم تحديث قاعدة البيانات بنجاح')
    
except Exception as e:
    print(f'⚠️ تحذير في تحديث قاعدة البيانات: {e}')

print('✅ قاعدة البيانات جاهزة')
" 2>nul
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🧪 اختبار Backend قبل التشغيل...
cd backend
python -c "
try:
    from main import app
    from datetime import datetime
    print('✅ استيراد main.py نجح')
    print('✅ استيراد datetime نجح')
    print('✅ Backend جاهز للتشغيل')
except Exception as e:
    print(f'❌ خطأ في Backend: {e}')
    exit(1)
" 2>nul
if errorlevel 1 (
    echo ❌ مشكلة في Backend - يرجى مراجعة الأخطاء
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Backend جاهز

echo.
echo 🚀 تشغيل Backend Server...
start "Backend Server - Fixed" cmd /k "echo 🔧 Backend Server مع الإصلاحات - http://localhost:8000 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite مع 2FA && echo 🌐 API: http://localhost:8000 && echo 🔍 Auto-Detected: /api/accounts/auto-detected && echo 📚 API Docs: http://localhost:8000/api/docs && echo 🔐 2FA Endpoints متاحة && echo ===================================== && cd backend && python main.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 8 /nobreak >nul

echo 🧪 اختبار Backend...
curl -s http://localhost:8000/ >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend لا يستجيب - يرجى التحقق من النافذة
    echo 🔧 تحقق من نافذة Backend للأخطاء
) else (
    echo ✅ Backend يعمل بشكل صحيح
)

echo.
echo 🌐 تشغيل Frontend...
start "Frontend - Updated" cmd /k "echo 🌐 Frontend محدث - http://localhost:3002 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 🧠 كاشف Instagram الذكي: محسن && echo 👥 إدارة الحسابات: مع 2FA && echo 🔍 الكشف التلقائي: مضمون && echo 💾 حفظ الحسابات: يعمل && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 12 /nobreak >nul

echo.
echo ========================================
echo ✅ تم إعادة تشغيل المشروع بنجاح!
echo ========================================
echo 🔧 Backend: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📚 API Docs: http://localhost:8000/api/docs
echo 🔍 Auto-Detected API: http://localhost:8000/api/accounts/auto-detected
echo ========================================
echo.

echo 🧪 اختبار النظام المحدث...
timeout /t 3 /nobreak >nul

echo 📊 اختبار Backend API...
curl -s -X GET "http://localhost:8000/" 
echo.

echo 📊 اختبار إحصائيات النظام...
curl -s -X GET "http://localhost:8000/api/stats/system"
echo.

echo 📊 اختبار قائمة الحسابات...
curl -s -X GET "http://localhost:8000/api/accounts"
echo.

echo 🧪 اختبار Auto-Detected Endpoint...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"username\": \"test_restart_%RANDOM%\", \"name\": \"حساب اختبار إعادة التشغيل\", \"notes\": \"تم إنشاؤه لاختبار إعادة التشغيل\"}"
echo.

echo.
echo ========================================
echo 🎯 دليل الاستخدام بعد إعادة التشغيل:
echo ========================================
echo.
echo 🌐 افتح المتصفح على: http://localhost:3002
echo.
echo 🧠 لاختبار الكشف التلقائي:
echo   1. اضغط "🧠 كشف ذكي للحسابات"
echo   2. اضغط "🌐 نافذة جديدة"
echo   3. سجل الدخول في Instagram
echo   4. اضغط "🔍 كشف الحساب يدوياً"
echo   5. تحقق من رسالة: "تم ربط وحفظ حساب بنجاح!"
echo.
echo 👥 لإدارة الحسابات:
echo   1. اضغط "👥 إدارة الحسابات المتقدمة"
echo   2. اضغط "إضافة حساب جديد"
echo   3. فعل "المصادقة الثنائية" إذا أردت
echo   4. امسح QR code واحفظ backup codes
echo.
echo 🔍 لفتح Instagram مع 2FA:
echo   1. اضغط "🌐 فتح Instagram مع دعم 2FA"
echo   2. اختر الحساب من القائمة
echo   3. سيفتح Instagram تلقائياً
echo   4. سجل الدخول مع 2FA إذا مطلوب
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎉 المشروع يعمل الآن بشكل مثالي!
echo ========================================
echo.
echo ✅ تم حل مشكلة Auto-Detected Endpoint
echo ✅ Backend يعمل مع جميع الإصلاحات
echo ✅ Frontend محدث ومتصل
echo ✅ قاعدة البيانات محدثة مع 2FA
echo ✅ جميع الميزات متاحة
echo.
echo 🧠 الكشف التلقائي: يعمل ويحفظ الحسابات
echo 👥 إدارة الحسابات: مع دعم 2FA كامل
echo 🔍 فتح Instagram: مع مراقبة ذكية
echo 💾 حفظ البيانات: مضمون ومشفر
echo.
echo 🎊 استمتع بالنظام المحسن!
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
