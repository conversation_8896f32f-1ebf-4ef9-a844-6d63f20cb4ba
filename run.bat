@echo off
echo 🚀 Instagram Likes Extractor
echo ============================
echo.

echo 📋 التحقق من المتطلبات...

:: التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python متوفر

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: التحقق من Rust
rustc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Rust غير مثبت - سيتم تشغيل الواجهة الأمامية فقط
    echo لتشغيل التطبيق كاملاً، ثبت Rust من: https://rustup.rs/
    echo.
    echo 🌐 تشغيل الواجهة الأمامية...
    start http://localhost:1421
    npm run dev
    pause
    exit /b 0
)
echo ✅ Rust متوفر

echo.
echo 🎯 تشغيل التطبيق كاملاً...
start http://localhost:1421
npm run tauri dev

pause
