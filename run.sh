#!/bin/bash

echo "🚀 Instagram Likes Extractor"
echo "============================"
echo

echo "📋 التحقق من المتطلبات..."

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python غير مثبت"
    echo "يرجى تثبيت Python من: https://www.python.org/downloads/"
    exit 1
fi
echo "✅ Python متوفر"

# التحقق من Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت"
    echo "يرجى تثبيت Node.js من: https://nodejs.org/"
    exit 1
fi
echo "✅ Node.js متوفر"

# التحقق من Rust
if ! command -v rustc &> /dev/null; then
    echo "⚠️ Rust غير مثبت - سيتم تشغيل الواجهة الأمامية فقط"
    echo "لتشغيل التطبيق كاملاً، ثبت Rust من: https://rustup.rs/"
    echo
    echo "🌐 تشغيل الواجهة الأمامية..."

    # فتح المتصفح (حسب النظام)
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:1421 &
    elif command -v open &> /dev/null; then
        open http://localhost:1421 &
    fi

    npm run dev
    exit 0
fi
echo "✅ Rust متوفر"

echo
echo "🎯 تشغيل التطبيق كاملاً..."

# فتح المتصفح (حسب النظام)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:1421 &
elif command -v open &> /dev/null; then
    open http://localhost:1421 &
fi

npm run tauri dev
