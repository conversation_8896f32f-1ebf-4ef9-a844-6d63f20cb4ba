#!/usr/bin/env python3
"""
Data Exporter for Instagram Likes
تصدير بيانات المعجبين بصيغ متعددة
"""

import argparse
import json
import csv
import os
from datetime import datetime
from typing import List, Dict
import sys

# إضافة نظام التسجيل
from logger import get_logger

try:
    import pandas as pd
except ImportError:
    print("Warning: pandas not installed. Excel export will not be available.")
    print("Install with: pip install pandas openpyxl")
    pd = None

class DataExporter:
    def __init__(self):
        self.output_dir = "exports"
        self.logger = get_logger("data_exporter")
        self.ensure_output_dir()

    def ensure_output_dir(self):
        """إنشاء مجلد التصدير إذا لم يكن موجوداً"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def generate_filename(self, format_type: str) -> str:
        """إنشاء اسم ملف مع الوقت الحالي"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(self.output_dir, f"instagram_likes_{timestamp}.{format_type}")

    def export_csv(self, data: List[Dict]) -> str:
        """تصدير البيانات بصيغة CSV"""
        try:
            filename = self.generate_filename("csv")

            if not data:
                raise ValueError("No data to export")

            # تحديد الأعمدة
            fieldnames = [
                'username', 'full_name', 'profile_url', 'profile_pic_url',
                'is_verified', 'follower_count', 'following_count', 'post_count',
                'bio', 'threads_url', 'facebook_url', 'extracted_at'
            ]

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # كتابة العناوين
                writer.writeheader()

                # كتابة البيانات
                for user in data:
                    # تنظيف البيانات
                    clean_user = {}
                    for field in fieldnames:
                        value = user.get(field, '')
                        if value is None:
                            value = ''
                        clean_user[field] = value

                    writer.writerow(clean_user)

            self.logger.log_export_complete("CSV", filename, len(data))
            return filename

        except Exception as e:
            self.logger.log_export_error("CSV", str(e), e)
            raise Exception(f"خطأ في تصدير CSV: {e}")

    def export_excel(self, data: List[Dict]) -> str:
        """تصدير البيانات بصيغة Excel"""
        if pd is None:
            raise Exception("pandas غير مثبت. يرجى تثبيته لتصدير Excel")

        try:
            filename = self.generate_filename("xlsx")

            if not data:
                raise ValueError("No data to export")

            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(data)

            # إعادة ترتيب الأعمدة
            column_order = [
                'username', 'full_name', 'profile_url', 'profile_pic_url',
                'is_verified', 'follower_count', 'following_count', 'post_count',
                'bio', 'threads_url', 'facebook_url', 'extracted_at'
            ]

            # إعادة ترتيب الأعمدة الموجودة فقط
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # تنظيف البيانات
            df = df.fillna('')

            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Instagram Likes', index=False)

                # تنسيق الورقة
                worksheet = writer.sheets['Instagram Likes']

                # تعديل عرض الأعمدة
                column_widths = {
                    'username': 20,
                    'full_name': 25,
                    'profile_url': 40,
                    'profile_pic_url': 50,
                    'bio': 50,
                    'threads_url': 40,
                    'facebook_url': 40,
                    'extracted_at': 20
                }

                for column, width in column_widths.items():
                    if column in df.columns:
                        col_idx = df.columns.get_loc(column) + 1
                        worksheet.column_dimensions[worksheet.cell(row=1, column=col_idx).column_letter].width = width

            print(f"✅ تم تصدير {len(data)} حساب إلى: {filename}")
            return filename

        except Exception as e:
            raise Exception(f"خطأ في تصدير Excel: {e}")

    def export_json(self, data: List[Dict]) -> str:
        """تصدير البيانات بصيغة JSON"""
        try:
            filename = self.generate_filename("json")

            if not data:
                raise ValueError("No data to export")

            # إنشاء هيكل JSON منظم
            export_data = {
                "metadata": {
                    "total_users": len(data),
                    "exported_at": datetime.now().isoformat(),
                    "format_version": "1.0"
                },
                "users": data
            }

            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

            print(f"✅ تم تصدير {len(data)} حساب إلى: {filename}")
            return filename

        except Exception as e:
            raise Exception(f"خطأ في تصدير JSON: {e}")

    def export_data(self, data: List[Dict], format_type: str) -> str:
        """تصدير البيانات بالصيغة المحددة"""
        if not data:
            raise ValueError("لا توجد بيانات للتصدير")

        format_type = format_type.lower()

        if format_type == "csv":
            return self.export_csv(data)
        elif format_type == "excel" or format_type == "xlsx":
            return self.export_excel(data)
        elif format_type == "json":
            return self.export_json(data)
        else:
            raise ValueError(f"صيغة غير مدعومة: {format_type}")

def main():
    parser = argparse.ArgumentParser(description='Data Exporter for Instagram Likes')
    parser.add_argument('--format', required=True, choices=['csv', 'excel', 'xlsx', 'json'],
                       help='Export format')
    parser.add_argument('--data', required=True, help='JSON data to export')

    args = parser.parse_args()

    try:
        # تحليل البيانات من JSON
        data = json.loads(args.data)

        # إنشاء المصدر
        exporter = DataExporter()

        # تصدير البيانات
        filename = exporter.export_data(data, args.format)

        # طباعة النتيجة
        result = {
            "success": True,
            "filename": filename,
            "total_exported": len(data),
            "format": args.format
        }

        print(json.dumps(result, ensure_ascii=False))

    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e)
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
