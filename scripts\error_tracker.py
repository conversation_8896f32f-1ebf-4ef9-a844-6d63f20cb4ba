#!/usr/bin/env python3
"""
Error Tracker for Instagram Likes Extractor
نظام تتبع الأخطاء التلقائي
"""

import json
import os
import sys
import traceback
from datetime import datetime
from typing import Dict, List, Optional
import platform

class ErrorTracker:
    """نظام تتبع الأخطاء التلقائي"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.error_file = os.path.join(log_dir, "error_tracker.json")
        self.ensure_log_dir()
        self.errors = self.load_errors()
    
    def ensure_log_dir(self):
        """إنشاء مجلد اللوجات إذا لم يكن موجوداً"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def load_errors(self) -> List[Dict]:
        """تحميل الأخطاء المحفوظة"""
        if os.path.exists(self.error_file):
            try:
                with open(self.error_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return []
        return []
    
    def save_errors(self):
        """حفظ الأخطاء"""
        try:
            with open(self.error_file, 'w', encoding='utf-8') as f:
                json.dump(self.errors, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ ملف الأخطاء: {e}")
    
    def get_system_info(self) -> Dict:
        """الحصول على معلومات النظام"""
        try:
            import psutil
            memory_info = psutil.virtual_memory()
            disk_info = psutil.disk_usage('/')
            
            return {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "cpu_count": os.cpu_count(),
                "memory_total_gb": round(memory_info.total / (1024**3), 2),
                "memory_available_gb": round(memory_info.available / (1024**3), 2),
                "memory_percent": memory_info.percent,
                "disk_total_gb": round(disk_info.total / (1024**3), 2),
                "disk_free_gb": round(disk_info.free / (1024**3), 2),
                "disk_percent": round((disk_info.used / disk_info.total) * 100, 2)
            }
        except:
            return {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor() or "Unknown",
                "cpu_count": os.cpu_count()
            }
    
    def track_error(self, 
                   error_type: str,
                   error_message: str,
                   exception: Optional[Exception] = None,
                   context: Optional[Dict] = None,
                   severity: str = "error") -> str:
        """تتبع خطأ جديد"""
        
        error_id = f"ERR_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.errors) + 1}"
        
        error_data = {
            "id": error_id,
            "timestamp": datetime.now().isoformat(),
            "type": error_type,
            "message": error_message,
            "severity": severity,  # error, warning, critical
            "context": context or {},
            "system_info": self.get_system_info(),
            "traceback": None,
            "resolved": False,
            "resolution": None,
            "resolution_date": None
        }
        
        # إضافة معلومات الاستثناء
        if exception:
            error_data["exception_type"] = type(exception).__name__
            error_data["exception_args"] = str(exception.args)
            error_data["traceback"] = traceback.format_exc()
        
        self.errors.append(error_data)
        self.save_errors()
        
        # طباعة الخطأ
        print(f"🚨 تم تسجيل خطأ جديد: {error_id}")
        print(f"   النوع: {error_type}")
        print(f"   الرسالة: {error_message}")
        
        return error_id
    
    def mark_resolved(self, error_id: str, resolution: str):
        """تحديد خطأ كمحلول"""
        for error in self.errors:
            if error["id"] == error_id:
                error["resolved"] = True
                error["resolution"] = resolution
                error["resolution_date"] = datetime.now().isoformat()
                self.save_errors()
                print(f"✅ تم تحديد الخطأ {error_id} كمحلول")
                return True
        
        print(f"❌ لم يتم العثور على الخطأ {error_id}")
        return False
    
    def get_unresolved_errors(self) -> List[Dict]:
        """الحصول على الأخطاء غير المحلولة"""
        return [error for error in self.errors if not error.get("resolved", False)]
    
    def get_error_stats(self) -> Dict:
        """إحصائيات الأخطاء"""
        total = len(self.errors)
        resolved = len([e for e in self.errors if e.get("resolved", False)])
        unresolved = total - resolved
        
        # تصنيف حسب النوع
        by_type = {}
        for error in self.errors:
            error_type = error.get("type", "unknown")
            by_type[error_type] = by_type.get(error_type, 0) + 1
        
        # تصنيف حسب الشدة
        by_severity = {}
        for error in self.errors:
            severity = error.get("severity", "error")
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        return {
            "total_errors": total,
            "resolved_errors": resolved,
            "unresolved_errors": unresolved,
            "resolution_rate": round((resolved / total) * 100, 2) if total > 0 else 0,
            "by_type": by_type,
            "by_severity": by_severity,
            "last_error_date": self.errors[-1]["timestamp"] if self.errors else None
        }
    
    def generate_report(self) -> str:
        """إنشاء تقرير الأخطاء"""
        stats = self.get_error_stats()
        unresolved = self.get_unresolved_errors()
        
        report = f"""
# 📊 تقرير الأخطاء - Instagram Likes Extractor

## 📈 الإحصائيات العامة
- إجمالي الأخطاء: {stats['total_errors']}
- الأخطاء المحلولة: {stats['resolved_errors']}
- الأخطاء غير المحلولة: {stats['unresolved_errors']}
- معدل الحل: {stats['resolution_rate']}%

## 📋 تصنيف الأخطاء حسب النوع
"""
        
        for error_type, count in stats['by_type'].items():
            report += f"- {error_type}: {count}\n"
        
        report += "\n## ⚠️ تصنيف الأخطاء حسب الشدة\n"
        for severity, count in stats['by_severity'].items():
            report += f"- {severity}: {count}\n"
        
        if unresolved:
            report += f"\n## 🚨 الأخطاء غير المحلولة ({len(unresolved)})\n"
            for error in unresolved[-5:]:  # آخر 5 أخطاء
                report += f"- [{error['id']}] {error['type']}: {error['message']}\n"
        
        report += f"\n## 📅 آخر خطأ: {stats['last_error_date'] or 'لا يوجد'}\n"
        
        return report
    
    def export_errors(self, format_type: str = "json") -> str:
        """تصدير الأخطاء"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type == "json":
            filename = os.path.join(self.log_dir, f"errors_export_{timestamp}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    "export_date": datetime.now().isoformat(),
                    "stats": self.get_error_stats(),
                    "errors": self.errors
                }, f, ensure_ascii=False, indent=2)
        
        elif format_type == "txt":
            filename = os.path.join(self.log_dir, f"errors_report_{timestamp}.txt")
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.generate_report())
        
        print(f"✅ تم تصدير الأخطاء إلى: {filename}")
        return filename

# إنشاء متتبع أخطاء عام
error_tracker = ErrorTracker()

def track_error(error_type: str, 
               error_message: str, 
               exception: Optional[Exception] = None,
               context: Optional[Dict] = None,
               severity: str = "error") -> str:
    """دالة سريعة لتتبع الأخطاء"""
    return error_tracker.track_error(error_type, error_message, exception, context, severity)

def mark_resolved(error_id: str, resolution: str):
    """دالة سريعة لتحديد خطأ كمحلول"""
    return error_tracker.mark_resolved(error_id, resolution)

if __name__ == "__main__":
    # اختبار نظام تتبع الأخطاء
    print("🧪 اختبار نظام تتبع الأخطاء")
    
    # تسجيل أخطاء تجريبية
    error1 = track_error("test_error", "خطأ تجريبي للاختبار", context={"test": True})
    error2 = track_error("connection_error", "فشل في الاتصال", severity="critical")
    
    # عرض الإحصائيات
    stats = error_tracker.get_error_stats()
    print(f"📊 إجمالي الأخطاء: {stats['total_errors']}")
    
    # إنشاء تقرير
    report = error_tracker.generate_report()
    print(report)
    
    # تصدير الأخطاء
    error_tracker.export_errors("txt")
    error_tracker.export_errors("json")
    
    print("✅ تم اختبار نظام تتبع الأخطاء بنجاح!")
