#!/usr/bin/env python3
"""
Instagram Likes Extractor
استخراج قائمة المعجبين من منشورات إنستجرام باستخدام Selenium
"""

import argparse
import json
import time
import random
import re
from datetime import datetime
from typing import List, Dict, Optional
import sys
import os

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    import undetected_chromedriver as uc
    import requests
except ImportError as e:
    print(f"Error: Missing required package: {e}")
    print("Please install required packages:")
    print("pip install selenium undetected-chromedriver requests")
    sys.exit(1)

class InstagramExtractor:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.extracted_users = []
        
    def setup_driver(self):
        """إعداد متصفح Chrome مع الحماية من الحظر"""
        try:
            options = uc.ChromeOptions()
            
            # إعدادات الحماية من الحظر
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # إعدادات إضافية للأمان
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-plugins-discovery")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            
            # User agent عشوائي
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            options.add_argument(f"--user-agent={random.choice(user_agents)}")
            
            self.driver = uc.Chrome(options=options)
            self.wait = WebDriverWait(self.driver, 20)
            
            # إخفاء خصائص الأتمتة
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ تم إعداد المتصفح بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد المتصفح: {e}")
            return False
    
    def navigate_to_post(self, post_url: str) -> bool:
        """الانتقال إلى المنشور"""
        try:
            print(f"🔗 فتح المنشور: {post_url}")
            self.driver.get(post_url)
            
            # انتظار تحميل الصفحة
            time.sleep(random.uniform(3, 5))
            
            # التحقق من وجود المنشور
            try:
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "article")))
                print("✅ تم تحميل المنشور بنجاح")
                return True
            except TimeoutException:
                print("❌ لم يتم العثور على المنشور")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فتح المنشور: {e}")
            return False
    
    def click_likes_button(self) -> bool:
        """النقر على زر المعجبين"""
        try:
            # البحث عن زر المعجبين بطرق متعددة
            likes_selectors = [
                "a[href*='/liked_by/']",
                "button[aria-label*='like']",
                "span:contains('likes')",
                "a:contains('others')"
            ]
            
            for selector in likes_selectors:
                try:
                    if 'contains' in selector:
                        # استخدام XPath للنصوص
                        xpath = f"//*[contains(text(), 'like') or contains(text(), 'others')]"
                        likes_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        likes_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if likes_button.is_displayed():
                        self.driver.execute_script("arguments[0].click();", likes_button)
                        print("✅ تم النقر على زر المعجبين")
                        time.sleep(random.uniform(2, 4))
                        return True
                        
                except NoSuchElementException:
                    continue
            
            print("❌ لم يتم العثور على زر المعجبين")
            return False
            
        except Exception as e:
            print(f"❌ خطأ في النقر على زر المعجبين: {e}")
            return False
    
    def extract_users_from_modal(self) -> List[Dict]:
        """استخراج المستخدمين من نافذة المعجبين"""
        users = []
        seen_usernames = set()
        scroll_attempts = 0
        max_scrolls = 50
        
        try:
            print("🔄 بدء استخراج المعجبين...")
            
            while scroll_attempts < max_scrolls:
                # البحث عن عناصر المستخدمين
                user_elements = self.driver.find_elements(By.CSS_SELECTOR, "div[role='dialog'] a[href*='/']")
                
                new_users_found = False
                
                for element in user_elements:
                    try:
                        href = element.get_attribute('href')
                        if not href or '/p/' in href or '/reel/' in href:
                            continue
                            
                        username = href.split('/')[-2] if href.endswith('/') else href.split('/')[-1]
                        
                        if username in seen_usernames or not username:
                            continue
                            
                        seen_usernames.add(username)
                        new_users_found = True
                        
                        # استخراج صورة البروفايل
                        profile_pic = ""
                        try:
                            img_element = element.find_element(By.TAG_NAME, "img")
                            profile_pic = img_element.get_attribute('src') or ""
                        except:
                            pass
                        
                        # استخراج الاسم الكامل
                        full_name = ""
                        try:
                            name_element = element.find_element(By.CSS_SELECTOR, "div:nth-child(2)")
                            full_name = name_element.text.strip()
                        except:
                            pass
                        
                        user_data = {
                            'username': username,
                            'profile_pic_url': profile_pic,
                            'profile_url': f"https://www.instagram.com/{username}/",
                            'full_name': full_name if full_name else None,
                            'is_verified': False,  # سيتم تحديثه لاحقاً
                            'follower_count': None,
                            'following_count': None,
                            'post_count': None,
                            'bio': None,
                            'threads_url': f"https://www.threads.net/@{username}",
                            'facebook_url': None,
                            'extracted_at': datetime.now().isoformat()
                        }
                        
                        users.append(user_data)
                        print(f"✅ تم استخراج: @{username}")
                        
                    except Exception as e:
                        continue
                
                if not new_users_found:
                    print("⏹️ لم يتم العثور على مستخدمين جدد")
                    break
                
                # التمرير لأسفل
                modal = self.driver.find_element(By.CSS_SELECTOR, "div[role='dialog']")
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", modal)
                
                # انتظار عشوائي لتجنب الحظر
                time.sleep(random.uniform(1, 3))
                scroll_attempts += 1
                
                print(f"📊 تم استخراج {len(users)} حساب حتى الآن...")
            
            print(f"✅ تم الانتهاء من الاستخراج. إجمالي: {len(users)} حساب")
            return users
            
        except Exception as e:
            print(f"❌ خطأ في استخراج المستخدمين: {e}")
            return users
    
    def enhance_user_data(self, users: List[Dict]) -> List[Dict]:
        """تحسين بيانات المستخدمين بمعلومات إضافية"""
        enhanced_users = []
        
        for i, user in enumerate(users):
            try:
                print(f"🔍 تحسين بيانات {i+1}/{len(users)}: @{user['username']}")
                
                # زيارة صفحة المستخدم
                self.driver.get(user['profile_url'])
                time.sleep(random.uniform(2, 4))
                
                # استخراج معلومات إضافية
                try:
                    # التحقق من التوثيق
                    verified_elements = self.driver.find_elements(By.CSS_SELECTOR, "svg[aria-label*='Verified']")
                    user['is_verified'] = len(verified_elements) > 0
                    
                    # استخراج الإحصائيات
                    stats_elements = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/followers/'], a[href*='/following/']")
                    for element in stats_elements:
                        text = element.text.lower()
                        if 'followers' in text or 'متابع' in text:
                            user['follower_count'] = self.parse_number(element.text)
                        elif 'following' in text or 'يتابع' in text:
                            user['following_count'] = self.parse_number(element.text)
                    
                    # عدد المنشورات
                    posts_elements = self.driver.find_elements(By.CSS_SELECTOR, "div:contains('posts')")
                    if posts_elements:
                        user['post_count'] = self.parse_number(posts_elements[0].text)
                    
                    # البايو
                    bio_elements = self.driver.find_elements(By.CSS_SELECTOR, "div[data-testid='user-bio']")
                    if bio_elements:
                        user['bio'] = bio_elements[0].text.strip()
                        
                        # البحث عن رابط فيسبوك في البايو
                        if 'facebook.com' in user['bio'] or 'fb.com' in user['bio']:
                            fb_match = re.search(r'(?:facebook\.com|fb\.com)/([a-zA-Z0-9.]+)', user['bio'])
                            if fb_match:
                                user['facebook_url'] = f"https://www.facebook.com/{fb_match.group(1)}"
                
                except Exception as e:
                    print(f"⚠️ خطأ في تحسين بيانات @{user['username']}: {e}")
                
                enhanced_users.append(user)
                
                # تأخير عشوائي لتجنب الحظر
                if i % 10 == 0 and i > 0:
                    print("⏸️ استراحة قصيرة لتجنب الحظر...")
                    time.sleep(random.uniform(5, 10))
                
            except Exception as e:
                print(f"❌ خطأ في معالجة @{user['username']}: {e}")
                enhanced_users.append(user)
                continue
        
        return enhanced_users
    
    def parse_number(self, text: str) -> Optional[int]:
        """تحويل النص إلى رقم (مثل 1.2K -> 1200)"""
        try:
            # إزالة الفراغات والنصوص الإضافية
            clean_text = re.sub(r'[^\d.KMkm]', '', text)
            
            if 'K' in clean_text.upper():
                return int(float(clean_text.replace('K', '').replace('k', '')) * 1000)
            elif 'M' in clean_text.upper():
                return int(float(clean_text.replace('M', '').replace('m', '')) * 1000000)
            else:
                return int(clean_text) if clean_text.isdigit() else None
        except:
            return None
    
    def extract_likes(self, post_url: str) -> List[Dict]:
        """الوظيفة الرئيسية لاستخراج المعجبين"""
        try:
            if not self.setup_driver():
                return []
            
            if not self.navigate_to_post(post_url):
                return []
            
            if not self.click_likes_button():
                return []
            
            # انتظار ظهور نافذة المعجبين
            time.sleep(3)
            
            users = self.extract_users_from_modal()
            
            if users:
                print(f"🎯 تم استخراج {len(users)} مستخدم. بدء تحسين البيانات...")
                # تحسين البيانات (اختياري - يمكن تعطيله لتوفير الوقت)
                # users = self.enhance_user_data(users)
            
            return users
            
        except Exception as e:
            print(f"❌ خطأ عام في الاستخراج: {e}")
            return []
        
        finally:
            if self.driver:
                self.driver.quit()
                print("🔒 تم إغلاق المتصفح")

def main():
    parser = argparse.ArgumentParser(description='Instagram Likes Extractor')
    parser.add_argument('--url', required=True, help='Instagram post URL')
    args = parser.parse_args()
    
    extractor = InstagramExtractor()
    users = extractor.extract_likes(args.url)
    
    # طباعة النتائج بصيغة JSON
    print(json.dumps(users, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
