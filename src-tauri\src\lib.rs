use std::process::Command;
use serde::{Deserialize, Serialize};
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize)]
struct InstagramUser {
    username: String,
    profile_pic_url: String,
    profile_url: String,
    full_name: Option<String>,
    is_verified: Option<bool>,
    follower_count: Option<u32>,
    following_count: Option<u32>,
    post_count: Option<u32>,
    bio: Option<String>,
    threads_url: Option<String>,
    facebook_url: Option<String>,
    extracted_at: String,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn extract_instagram_likes(post_url: String) -> Result<Vec<InstagramUser>, String> {
    // تشغيل Python script لاستخراج البيانات
    let output = Command::new("python")
        .arg("scripts/instagram_extractor.py")
        .arg("--url")
        .arg(&post_url)
        .output()
        .map_err(|e| format!("Failed to execute Python script: {}", e))?;

    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Python script failed: {}", error));
    }

    let result = String::from_utf8_lossy(&output.stdout);
    let users: Vec<InstagramUser> = serde_json::from_str(&result)
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    Ok(users)
}

#[tauri::command]
async fn export_data(data: Vec<InstagramUser>, format: String) -> Result<String, String> {
    // تشغيل Python script للتصدير
    let json_data = serde_json::to_string(&data)
        .map_err(|e| format!("Failed to serialize data: {}", e))?;

    let output = Command::new("python")
        .arg("scripts/data_exporter.py")
        .arg("--format")
        .arg(&format)
        .arg("--data")
        .arg(&json_data)
        .output()
        .map_err(|e| format!("Failed to execute export script: {}", e))?;

    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Export script failed: {}", error));
    }

    let result = String::from_utf8_lossy(&output.stdout);
    Ok(result.to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet, extract_instagram_likes, export_data])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
