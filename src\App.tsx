import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import Header from './components/Header';
import UrlInput from './components/UrlInput';
import LoadingScreen from './components/LoadingScreen';
import ResultsTable from './components/ResultsTable';
import ExportButtons from './components/ExportButtons';
import { InstagramUser } from './types';

function App() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<InstagramUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [darkMode, setDarkMode] = useState(false);

  const handleExtract = async () => {
    if (!url.trim()) {
      setError('يرجى إدخال رابط منشور إنستجرام صحيح');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults([]);

    try {
      // استدعاء Python script عبر Tauri command
      const extractedUsers = await invoke<InstagramUser[]>('extract_instagram_likes', {
        postUrl: url
      });
      
      setResults(extractedUsers);
    } catch (err) {
      setError(err as string || 'حدث خطأ أثناء استخراج البيانات');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      darkMode ? 'dark bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="container mx-auto px-4 py-8">
        <Header darkMode={darkMode} onToggleDarkMode={toggleDarkMode} />
        
        <div className="max-w-4xl mx-auto space-y-8">
          <UrlInput 
            url={url}
            onUrlChange={setUrl}
            onExtract={handleExtract}
            isLoading={isLoading}
          />

          {error && (
            <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg">
              <p className="font-medium">خطأ:</p>
              <p>{error}</p>
            </div>
          )}

          {isLoading && <LoadingScreen />}

          {results.length > 0 && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  النتائج ({results.length} حساب)
                </h2>
                <ExportButtons data={results} />
              </div>
              <ResultsTable users={results} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
