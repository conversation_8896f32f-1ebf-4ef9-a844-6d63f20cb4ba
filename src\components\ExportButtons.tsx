import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { InstagramUser } from '../types';

interface ExportButtonsProps {
  data: InstagramUser[];
}

const ExportButtons: React.FC<ExportButtonsProps> = ({ data }) => {
  const [isExporting, setIsExporting] = useState<string | null>(null);

  const handleExport = async (format: 'csv' | 'excel' | 'json') => {
    setIsExporting(format);
    
    try {
      await invoke('export_data', {
        data: data,
        format: format
      });
      
      // إظهار رسالة نجاح
      alert(`تم تصدير البيانات بصيغة ${format.toUpperCase()} بنجاح!`);
    } catch (error) {
      console.error('Export error:', error);
      alert(`حدث خطأ أثناء التصدير: ${error}`);
    } finally {
      setIsExporting(null);
    }
  };

  const exportButtons = [
    {
      format: 'csv' as const,
      label: 'CSV',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      color: 'bg-green-500 hover:bg-green-600',
      description: 'ملف نصي مفصول بفواصل'
    },
    {
      format: 'excel' as const,
      label: 'Excel',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'جدول بيانات Excel'
    },
    {
      format: 'json' as const,
      label: 'JSON',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'ملف JSON للمطورين'
    }
  ];

  return (
    <div className="flex flex-wrap gap-3">
      {exportButtons.map(({ format, label, icon, color, description }) => (
        <button
          key={format}
          onClick={() => handleExport(format)}
          disabled={isExporting !== null}
          className={`
            flex items-center px-4 py-2 rounded-lg text-white font-medium transition-all duration-200
            ${isExporting === format ? 'opacity-75 cursor-not-allowed' : color}
            ${isExporting !== null && isExporting !== format ? 'opacity-50 cursor-not-allowed' : ''}
            hover:shadow-lg transform hover:scale-105 active:scale-95
            disabled:transform-none disabled:hover:shadow-none
          `}
          title={description}
        >
          {isExporting === format ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <span className="mr-2">{icon}</span>
          )}
          
          {isExporting === format ? 'جارٍ التصدير...' : `تصدير ${label}`}
        </button>
      ))}
      
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mr-4">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        {data.length} حساب جاهز للتصدير
      </div>
    </div>
  );
};

export default ExportButtons;
