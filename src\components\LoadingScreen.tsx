import React from 'react';

const LoadingScreen: React.FC = () => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-12">
      <div className="text-center">
        <div className="loading-dots mx-auto mb-6">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
        
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          جارٍ استخراج المعجبين...
        </h3>
        
        <div className="space-y-3 text-gray-600 dark:text-gray-400">
          <div className="flex items-center justify-center">
            <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse mr-2"></div>
            <span>فتح المنشور...</span>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse mr-2"></div>
            <span>تحميل قائمة المعجبين...</span>
          </div>
          <div className="flex items-center justify-center">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mr-2"></div>
            <span>استخراج معلومات الحسابات...</span>
          </div>
        </div>
        
        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            💡 <strong>نصيحة:</strong> قد تستغرق العملية عدة دقائق حسب عدد المعجبين
          </p>
        </div>
        
        <div className="mt-6 flex justify-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
          <span className="flex items-center">
            <svg className="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            حماية من الحظر
          </span>
          <span className="flex items-center">
            <svg className="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            تمرير تلقائي ذكي
          </span>
          <span className="flex items-center">
            <svg className="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            استخراج آمن
          </span>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
