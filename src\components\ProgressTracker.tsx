import React, { useEffect, useState } from 'react';
import { ExtractionProgress } from '../types';

interface ProgressTrackerProps {
  progress: ExtractionProgress;
  currentTask: string;
  logs: string[];
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({ progress, currentTask, logs }) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progress.percentage);
    }, 100);
    return () => clearTimeout(timer);
  }, [progress.percentage]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="space-y-6">
        {/* عنوان التقدم */}
        <div className="text-center">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            تتبع عملية الاستخراج
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            الخطوة {progress.current} من {progress.total}
          </p>
        </div>

        {/* شريط التقدم */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              التقدم الإجمالي
            </span>
            <span className="text-sm font-bold text-pink-600 dark:text-pink-400">
              {Math.round(animatedProgress)}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-pink-500 via-purple-500 to-orange-500 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${animatedProgress}%` }}
            >
              <div className="h-full bg-white/20 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* المهمة الحالية */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
            <div className="mr-3">
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                المهمة الحالية:
              </p>
              <p className="text-blue-700 dark:text-blue-300 font-semibold">
                {currentTask || progress.status}
              </p>
            </div>
          </div>
        </div>

        {/* خطوات العملية */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            خطوات العملية:
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            {[
              { step: 1, name: 'فتح المتصفح', icon: '🌐' },
              { step: 2, name: 'الانتقال للمنشور', icon: '🔗' },
              { step: 3, name: 'تسجيل الدخول', icon: '🔐' },
              { step: 4, name: 'البحث عن المعجبين', icon: '🔍' },
              { step: 5, name: 'فتح القائمة', icon: '📋' },
              { step: 6, name: 'بدء الاستخراج', icon: '⚡' },
              { step: 7, name: 'تمرير البيانات', icon: '📊' },
              { step: 8, name: 'تحسين البيانات', icon: '✨' },
              { step: 9, name: 'إنهاء العملية', icon: '✅' }
            ].map((item) => (
              <div
                key={item.step}
                className={`flex items-center p-2 rounded-lg text-xs transition-all duration-300 ${
                  progress.current >= item.step
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                    : progress.current === item.step - 1
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 animate-pulse'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                }`}
              >
                <span className="mr-2">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
                {progress.current >= item.step && (
                  <span className="mr-auto text-green-600 dark:text-green-400">✓</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* سجل العمليات */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              سجل العمليات:
            </h4>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {logs.length} عملية
            </span>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 max-h-32 overflow-y-auto custom-scrollbar">
            {logs.length > 0 ? (
              <div className="space-y-1">
                {logs.slice(-5).map((log, index) => (
                  <div
                    key={index}
                    className="text-xs text-gray-600 dark:text-gray-400 font-mono"
                  >
                    {log}
                  </div>
                ))}
                {logs.length > 5 && (
                  <div className="text-xs text-gray-500 dark:text-gray-500 italic">
                    ... و {logs.length - 5} عمليات أخرى
                  </div>
                )}
              </div>
            ) : (
              <div className="text-xs text-gray-500 dark:text-gray-500 italic">
                لا توجد عمليات مسجلة بعد...
              </div>
            )}
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {progress.current}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              خطوة حالية
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {progress.total}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              إجمالي الخطوات
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-pink-600 dark:text-pink-400">
              {Math.round(animatedProgress)}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              مكتمل
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
