export interface InstagramUser {
  username: string;
  profile_pic_url: string;
  profile_url: string;
  full_name?: string;
  is_verified?: boolean;
  follower_count?: number;
  following_count?: number;
  post_count?: number;
  bio?: string;
  threads_url?: string;
  facebook_url?: string;
  extracted_at: string;
}

export interface ExtractionProgress {
  current: number;
  total: number;
  status: string;
  percentage: number;
}

export interface ExtractionResult {
  success: boolean;
  users: InstagramUser[];
  error?: string;
  total_extracted: number;
}
