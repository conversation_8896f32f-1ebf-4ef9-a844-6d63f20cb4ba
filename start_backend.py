#!/usr/bin/env python3
"""
تشغيل Backend API
Instagram Likes Extractor - Backend Server
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """التحقق من متطلبات Python"""
    print("🔍 التحقق من متطلبات Python...")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت متطلبات Backend...")
    
    backend_dir = Path(__file__).parent / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True, cwd=backend_dir)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # تشغيل إعداد قاعدة البيانات
        subprocess.run([
            sys.executable, "-c", 
            "from database import init_database; init_database()"
        ], check=True, cwd=backend_dir)
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إعداد قاعدة البيانات: {e}")
        return False

def start_server():
    """تشغيل خادم FastAPI"""
    print("🚀 تشغيل خادم Backend...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # تشغيل uvicorn
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ], cwd=backend_dir)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 Instagram Likes Extractor - Backend Server")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return
    
    print("\n" + "=" * 60)
    print("✅ Backend جاهز للعمل!")
    print("🌐 API Docs: http://localhost:8000/api/docs")
    print("📊 ReDoc: http://localhost:8000/api/redoc")
    print("🗄️ Database: SQLite")
    print("=" * 60)
    print("اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    # تشغيل الخادم
    start_server()

if __name__ == "__main__":
    main()
