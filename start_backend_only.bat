@echo off
title Backend Server Only - اختبار سريع
color 0A

echo.
echo ========================================
echo 🔧 تشغيل Backend Server فقط
echo ========================================
echo 🚀 للاختبار السريع والتطوير
echo 📊 API: http://localhost:8000
echo 📚 Docs: http://localhost:8000/api/docs
echo ========================================
echo.

echo 📦 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🔧 تثبيت مكتبات أساسية...
python -m pip install fastapi uvicorn sqlalchemy --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python -c "
try:
    from database import init_database
    init_database()
    print('✅ تم تهيئة قاعدة البيانات')
except Exception as e:
    print(f'⚠️ تحذير: {e}')
" 2>nul

echo.
echo 🚀 تشغيل Backend Server...
echo.
echo ========================================
echo 🔧 Backend Server يعمل الآن على:
echo 📊 API: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/api/docs
echo 🔍 Auto-Detected Endpoint: http://localhost:8000/api/accounts/auto-detected
echo ========================================
echo.

python main.py

cd ..

echo.
echo ========================================
echo 🛑 تم إيقاف Backend Server
echo ========================================

pause
