@echo off
title Instagram Extractor مع المصادقة الثنائية - النظام الكامل
color 0A

echo.
echo ========================================
echo 🔐 Instagram Extractor مع 2FA
echo ========================================
echo 🧠 كاشف Instagram الذكي
echo 👥 إدارة الحسابات المتقدمة
echo 🔐 المصادقة الثنائية (2FA)
echo 🌐 فتح Instagram مع دعم 2FA
echo 📊 لوحة تحكم شاملة
echo ========================================
echo.

echo 📦 التحقق من المتطلبات الأساسية...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات الأساسية متوفرة
echo.

echo 🔐 التحقق من مكتبات المصادقة الثنائية...
python -c "import pyotp, cryptography, qrcode" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ مكتبات 2FA غير مثبتة - سيتم تثبيتها الآن
    echo.
    call install_2fa_requirements.bat
    if errorlevel 1 (
        echo ❌ فشل في تثبيت مكتبات 2FA
        pause
        exit /b 1
    )
) else (
    echo ✅ مكتبات 2FA متوفرة
)

echo.
echo 🔧 تثبيت مكتبات Python الأساسية...
python -m pip install flask flask-cors sqlalchemy uvicorn --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات مع دعم 2FA...
cd backend
python -c "
from database import init_database
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# إنشاء قاعدة البيانات
init_database()

# التحقق من الجداول الجديدة
engine = create_engine('sqlite:///instagram_extractor.db')
Session = sessionmaker(bind=engine)
session = Session()

# إضافة الأعمدة الجديدة إذا لم تكن موجودة
try:
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN has_2fa BOOLEAN DEFAULT 0'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_secret TEXT'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_backup_codes TEXT'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN two_fa_method TEXT'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN is_locked BOOLEAN DEFAULT 0'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN login_attempts INTEGER DEFAULT 0'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN last_login_attempt DATETIME'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN profile_pic_url TEXT'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN follower_count INTEGER DEFAULT 0'))
    session.execute(text('ALTER TABLE instagram_accounts ADD COLUMN following_count INTEGER DEFAULT 0'))
    session.commit()
    print('✅ تم تحديث قاعدة البيانات لدعم 2FA')
except:
    print('✅ قاعدة البيانات محدثة مسبقاً')
finally:
    session.close()
" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون قاعدة البيانات مهيأة مسبقاً
)
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🚀 تشغيل النظام الكامل مع 2FA...

echo 🔧 تشغيل Backend Server مع دعم 2FA...
start "Backend Server with 2FA" cmd /k "echo 🔧 Backend Server مع 2FA - http://localhost:8000 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite مع 2FA && echo 🌐 API: http://localhost:8000 && echo 🔐 2FA Endpoints: && echo   • POST /api/accounts/{id}/setup-2fa && echo   • POST /api/accounts/{id}/verify-2fa-setup && echo   • POST /api/accounts/{id}/verify-2fa-login && echo   • POST /api/accounts/{id}/disable-2fa && echo   • POST /api/accounts/{id}/regenerate-backup-codes && echo 📈 الإحصائيات: /api/stats/system && echo 👥 الحسابات: /api/accounts && echo ===================================== && cd backend && python main.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 8 /nobreak >nul

echo 🌐 تشغيل Frontend مع مكونات 2FA...
start "Frontend with 2FA" cmd /k "echo 🌐 Frontend مع 2FA - http://localhost:3002 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 🧠 كاشف Instagram الذكي: متاح && echo 👥 إدارة الحسابات المتقدمة: متاح && echo 🔐 المصادقة الثنائية: متاح && echo 🌐 فتح Instagram مع 2FA: متاح && echo 📊 لوحة التحكم: متاح && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 12 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام الكامل مع 2FA جاهز!
echo ========================================
echo 🔧 Backend: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 إحصائيات النظام: http://localhost:8000/api/stats/system
echo 🔐 API Documentation: http://localhost:8000/docs
echo ========================================
echo.

echo 🔐 ميزات المصادقة الثنائية المتاحة:
echo ========================================
echo ✅ إدارة الحسابات المتقدمة
echo   • إضافة حسابات مع تفعيل 2FA
echo   • إنشاء QR codes للمصادقة
echo   • إنشاء backup codes احتياطية
echo   • تشفير البيانات الحساسة
echo   • إدارة حالة الحسابات (نشط/مقفل)
echo.
echo ✅ فتح Instagram مع دعم 2FA
echo   • اختيار الحساب من القائمة
echo   • فتح نافذة Instagram مخصصة
echo   • مراقبة تسجيل الدخول التلقائي
echo   • التحقق من 2FA تلقائياً
echo   • دعم backup codes
echo.
echo ✅ تسجيل الدخول المتقدم
echo   • واجهة تسجيل دخول متقدمة
echo   • دعم TOTP codes
echo   • دعم backup codes
echo   • حماية من محاولات الاختراق
echo   • قفل الحساب بعد محاولات فاشلة
echo.
echo ✅ الأمان والحماية
echo   • تشفير البيانات الحساسة
echo   • حفظ آمن للـ secrets
echo   • backup codes مشفرة
echo   • مراقبة محاولات تسجيل الدخول
echo   • قفل تلقائي للحسابات المشبوهة
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام الشامل:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات مع/بدون 2FA
echo   • معدل النجاح ونشاط النظام
echo   • مراقبة الحسابات المقفلة
echo.
echo 👥 إدارة الحسابات المتقدمة:
echo   1. اضغط على "إدارة الحسابات المتقدمة"
echo   2. اضغط "إضافة حساب جديد"
echo   3. املأ البيانات وفعل "تفعيل المصادقة الثنائية"
echo   4. امسح QR code بتطبيق المصادقة
echo   5. احفظ backup codes في مكان آمن
echo   6. أدخل رمز التحقق لتأكيد الإعداد
echo.
echo 🌐 فتح Instagram مع 2FA:
echo   1. اضغط على "فتح Instagram مع دعم 2FA"
echo   2. اختر الحساب من القائمة
echo   3. ستفتح نافذة Instagram تلقائياً
echo   4. سجل الدخول بكلمة المرور
echo   5. أدخل رمز 2FA من التطبيق
echo   6. سيتم فتح الحساب بنجاح
echo.
echo 🧠 كاشف Instagram الذكي:
echo   • يعمل مع الحسابات المحمية بـ 2FA
echo   • كشف تلقائي للحسابات المفتوحة
echo   • ربط آمن مع التحقق من الهوية
echo   • حفظ في قاعدة البيانات مع التشفير
echo ========================================
echo.

echo 🧪 اختبار النظام الكامل:
echo ========================================
echo 📊 اختبار Backend مع 2FA:
echo    curl http://localhost:8000/api/stats/system
echo.
echo 👥 اختبار الحسابات:
echo    curl http://localhost:8000/api/accounts
echo.
echo 🔐 اختبار 2FA API:
echo    curl -X POST http://localhost:8000/api/accounts/1/setup-2fa
echo.
echo 📈 اختبار الإحصائيات:
echo    curl http://localhost:8000/api/stats/dashboard
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل مع 2FA:
echo ========================================
echo 🔐 للمصادقة الثنائية:
echo   • استخدم تطبيق مصادقة موثوق (Google Authenticator)
echo   • احفظ backup codes في مكان آمن ومنفصل
echo   • لا تشارك QR codes أو secrets مع أحد
echo   • احتفظ بنسخة احتياطية من مفتاح التشفير
echo.
echo 🌐 للنوافذ:
echo   • اسمح بالنوافذ المنبثقة لموقع localhost
echo   • استخدم Chrome أو Firefox للحصول على أفضل أداء
echo   • لا تغلق النافذة قبل اكتمال تسجيل الدخول
echo.
echo 👥 للحسابات:
echo   • راجع الحسابات المقفلة دورياً
echo   • احذف الحسابات غير المستخدمة
echo   • حدث معلومات الحسابات بانتظام
echo   • راقب محاولات تسجيل الدخول الفاشلة
echo.
echo 💾 لقاعدة البيانات:
echo   • احفظ نسخ احتياطية من قاعدة البيانات
echo   • راقب حجم قاعدة البيانات بانتظام
echo   • نظف البيانات القديمة عند الحاجة
echo   • احتفظ بنسخة من مفتاح التشفير منفصلة
echo ========================================
echo.

echo 🛠️ استكشاف الأخطاء مع 2FA:
echo ========================================
echo ❓ مشاكل في إعداد 2FA:
echo   ✅ تأكد من تثبيت مكتبات 2FA
echo   ✅ تحقق من صحة QR code
echo   ✅ تأكد من ضبط الوقت في الجهاز
echo   ✅ جرب backup code إذا فشل TOTP
echo.
echo ❓ مشاكل في تسجيل الدخول:
echo   ✅ تحقق من كلمة المرور
echo   ✅ تأكد من رمز 2FA الصحيح
echo   ✅ تحقق من عدم قفل الحساب
echo   ✅ جرب backup code كبديل
echo.
echo ❓ مشاكل في فتح Instagram:
echo   ✅ تأكد من تسجيل الدخول كاملاً
echo   ✅ تحقق من 2FA إذا كان مطلوب
echo   ✅ راقب رسائل التقدم في الواجهة
echo   ✅ استخدم "التركيز على النافذة"
echo ========================================
echo.

echo 🎉 النظام الكامل مع 2FA جاهز للاستخدام!
echo 🔐 أمان متقدم مع المصادقة الثنائية!
echo 🧠 كاشف ذكي مع حماية إضافية!
echo 👥 إدارة حسابات متقدمة وآمنة!
echo 🌐 فتح Instagram مع دعم 2FA كامل!
echo.

echo 📝 ملاحظات مهمة للنسخة مع 2FA:
echo ========================================
echo • جميع البيانات الحساسة مشفرة
echo • backup codes تُعرض مرة واحدة فقط
echo • الحسابات تُقفل بعد 5 محاولات فاشلة
echo • مفتاح التشفير محفوظ في ملف منفصل
echo • يمكن تعطيل 2FA برمز تحقق صحيح
echo ========================================
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
