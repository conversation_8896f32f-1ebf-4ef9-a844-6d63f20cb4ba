@echo off
title Instagram Extractor Database System v4.0 - نظام قاعدة البيانات الشامل
color 0A

echo.
echo ========================================
echo 🗄️ Instagram Extractor Database System v4.0
echo ========================================
echo 📊 قاعدة بيانات SQLite شاملة
echo 🔧 خادم قاعدة البيانات المتقدم
echo 📱 واجهة احترافية مع بيانات حقيقية
echo 🎯 إحصائيات مباشرة من قاعدة البيانات
echo 🔔 إشعارات وسجل نشاط متكامل
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py
if errorlevel 1 (
    echo ❌ فشل في تهيئة قاعدة البيانات
    pause
    exit /b 1
)
cd ..

echo ✅ تم تهيئة قاعدة البيانات بنجاح
echo.

echo 🔧 بدء تشغيل خادم قاعدة البيانات...
start "Database Server Professional" cmd /k "echo 🔧 Database Server Professional && echo ================================ && cd backend && python database_server.py"

echo ⏳ انتظار تشغيل خادم قاعدة البيانات...
timeout /t 5 /nobreak >nul

echo 🌐 بدء تشغيل Frontend مع قاعدة البيانات...
start "Frontend Database UI" cmd /k "echo 🌐 Frontend Database UI && echo ======================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo ✅ نظام قاعدة البيانات جاهز!
echo ========================================
echo 🗄️ Database: SQLite (backend/database/instagram_extractor.db)
echo 🔧 Backend API: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 API Status: http://localhost:8000/api/test
echo ========================================
echo.

echo 🗄️ مكونات قاعدة البيانات:
echo ========================================
echo 📋 الجداول الرئيسية:
echo   • users - المستخدمين
echo   • instagram_accounts - حسابات Instagram
echo   • extractions - عمليات الاستخراج
echo   • extracted_users - المستخدمين المستخرجين
echo   • notifications - الإشعارات
echo   • activity_logs - سجل النشاط
echo   • sessions - الجلسات
echo   • settings - الإعدادات
echo   • statistics - الإحصائيات
echo   • exported_files - الملفات المصدرة
echo ========================================
echo.

echo 📊 البيانات التجريبية المدرجة:
echo ========================================
echo 👤 المستخدمين:
echo   • admin (مدير النظام)
echo   • developer (المطور العربي)
echo.
echo 📱 حسابات Instagram:
echo   • business_account (حساب العمل)
echo   • personal_verified (حساب شخصي موثق)
echo   • creative_content (حساب المحتوى الإبداعي)
echo.
echo 🔍 عمليات الاستخراج:
echo   • 4 عمليات استخراج مكتملة
echo   • أكثر من 1000 مستخدم مستخرج
echo   • إحصائيات متنوعة للاختبار
echo.
echo 🔔 الإشعارات:
echo   • إشعارات ترحيب ونصائح
echo   • إشعارات العمليات والأنشطة
echo   • تصنيفات متنوعة (نجاح، معلومات، تحذير)
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 2 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل استخدام قاعدة البيانات:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات والاستخراجات الفعلي
echo   • معدل النجاح المحسوب
echo   • النشاط الأخير من سجل قاعدة البيانات
echo.
echo 🔔 الإشعارات:
echo   • إشعارات مباشرة من قاعدة البيانات
echo   • تصنيف حسب النوع والفئة
echo   • تحديد كمقروءة مع حفظ في قاعدة البيانات
echo   • عداد الإشعارات غير المقروءة
echo.
echo 👥 إدارة الحسابات:
echo   • إضافة حسابات جديدة لقاعدة البيانات
echo   • تحديث معلومات الحسابات
echo   • تتبع حالة الحسابات (نشط/غير نشط)
echo   • إحصائيات المتابعين والمنشورات
echo.
echo 🔍 عمليات الاستخراج:
echo   • إنشاء عمليات استخراج جديدة
echo   • تتبع التقدم والحالة
echo   • حفظ المستخدمين المستخرجين
echo   • إحصائيات مفصلة لكل عملية
echo ========================================
echo.

echo 🔧 API Endpoints المتاحة:
echo ========================================
echo GET  /api/test - اختبار الاتصال
echo GET  /api/accounts - جلب جميع الحسابات
echo POST /api/accounts - إضافة حساب جديد
echo GET  /api/extractions - جلب عمليات الاستخراج
echo POST /api/extractions - بدء عملية استخراج
echo GET  /api/dashboard/stats - إحصائيات لوحة التحكم
echo GET  /api/notifications - جلب الإشعارات
echo POST /api/notifications/{id}/read - تحديد إشعار كمقروء
echo GET  /api/activity - جلب النشاط الأخير
echo ========================================
echo.

echo 📊 ميزات قاعدة البيانات المتقدمة:
echo ========================================
echo ✅ فهارس محسنة للأداء
echo ✅ مفاتيح خارجية للتكامل
echo ✅ تشفير كلمات المرور
echo ✅ تسجيل جميع الأنشطة
echo ✅ إدارة الجلسات والأمان
echo ✅ نظام إعدادات مرن
echo ✅ إحصائيات تاريخية
echo ✅ تنظيف البيانات القديمة
echo ✅ نسخ احتياطي تلقائي
echo ✅ استعلامات محسنة
echo ========================================
echo.

echo 🔒 الأمان والخصوصية:
echo ========================================
echo ✅ تشفير كلمات المرور بـ SHA256
echo ✅ حماية من SQL Injection
echo ✅ تسجيل جميع العمليات
echo ✅ إدارة الجلسات الآمنة
echo ✅ تنظيف البيانات الحساسة
echo ✅ صلاحيات المستخدمين
echo ✅ تشفير بيانات الحسابات
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo • راقب الإحصائيات في لوحة التحكم
echo • تحقق من الإشعارات بانتظام
echo • استخدم سجل النشاط للمراجعة
echo • احفظ نسخ احتياطية من قاعدة البيانات
echo • راقب أداء الاستعلامات
echo • نظف البيانات القديمة دورياً
echo • استخدم الفهارس للاستعلامات السريعة
echo ========================================
echo.

echo 🛠️ إدارة قاعدة البيانات:
echo ========================================
echo 📁 موقع قاعدة البيانات:
echo    backend/database/instagram_extractor.db
echo.
echo 🔧 أدوات الإدارة:
echo    python init_database.py - إعادة تهيئة
echo    python database_manager.py - إدارة مباشرة
echo.
echo 📊 مراقبة الأداء:
echo    تحقق من حجم الملف وعدد السجلات
echo    راقب سرعة الاستعلامات
echo    تابع استخدام الذاكرة
echo ========================================
echo.

echo 🎉 نظام قاعدة البيانات جاهز للاستخدام!
echo 📊 جميع البيانات محفوظة ومنظمة!
echo 🔧 API متكامل مع قاعدة البيانات!
echo 🌐 واجهة تعرض البيانات الحقيقية!
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
