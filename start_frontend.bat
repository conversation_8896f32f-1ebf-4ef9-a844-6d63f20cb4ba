@echo off
chcp 65001 >nul
title Instagram Likes Extractor - Frontend

echo ============================================================
echo 🌐 Instagram Likes Extractor - Frontend Server
echo ============================================================

cd /d "%~dp0frontend"

echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

echo 📦 تثبيت المتطلبات...
call npm install
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo ============================================================
echo ✅ Frontend جاهز للعمل!
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000/api/docs
echo ============================================================
echo اضغط Ctrl+C لإيقاف الخادم
echo ============================================================

echo 🚀 تشغيل خادم Frontend...
call npm run dev

pause
