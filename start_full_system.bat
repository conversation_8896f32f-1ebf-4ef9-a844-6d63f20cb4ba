@echo off
chcp 65001 >nul
title Instagram Likes Extractor - Full System

echo ============================================================
echo 🚀 Instagram Likes Extractor - النظام الكامل
echo ============================================================
echo.
echo سيتم تشغيل:
echo 🔧 Backend API Server (Port 8000)
echo 🌐 Frontend React App (Port 3000)
echo.
echo ============================================================

echo 🔧 تشغيل Backend Server...
start "Backend Server" cmd /c "python start_backend.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

echo 🌐 تشغيل Frontend Server...
start "Frontend Server" cmd /c "start_frontend.bat"

echo.
echo ============================================================
echo ✅ تم تشغيل النظام الكامل!
echo.
echo 🔧 Backend API: http://localhost:8000/api/docs
echo 🌐 Frontend App: http://localhost:3000
echo.
echo ملاحظة: قد يستغرق تشغيل الخوادم بضع ثوان
echo ============================================================

echo 🌐 فتح التطبيق في المتصفح...
timeout /t 3 /nobreak >nul

if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" "http://localhost:3000"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" "http://localhost:3000"
) else (
    start "" "http://localhost:3000"
)

echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
echo (ستبقى الخوادم تعمل في النوافذ الأخرى)
pause >nul
