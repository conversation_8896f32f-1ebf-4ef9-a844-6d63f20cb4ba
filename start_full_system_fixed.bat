@echo off
title Instagram Likes Extractor - النظام الكامل
color 0A

echo ========================================
echo 🚀 Instagram Likes Extractor v2.0
echo ========================================
echo 🔧 النظام الكامل - Backend + Frontend
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    pause
    exit /b 1
)

echo ✅ Python و Node.js متوفران
echo.

echo 🔧 بدء تشغيل Backend...
start "Backend Server" cmd /k "cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 3 /nobreak >nul

echo 🌐 بدء تشغيل Frontend...
start "Frontend Server" cmd /k "cd frontend && npm run dev -- --port 3002"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام جاهز للاستخدام!
echo ========================================
echo 🔧 Backend API: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 API Docs: http://localhost:8000/api/accounts
echo ========================================
echo.

echo 🌐 فتح المتصفح...
start http://localhost:3002

echo.
echo ========================================
echo 🎯 كيفية الاستخدام:
echo ========================================
echo 1. انتقل إلى "👥 إدارة الحسابات"
echo 2. انقر "🤖 إضافة تلقائي" لتجربة الأتمتة
echo 3. أو انقر "+ إضافة يدوي" لإضافة حساب يدوياً
echo 4. انتقل إلى "🌐 تصفح Instagram" للتصفح
echo ========================================
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
