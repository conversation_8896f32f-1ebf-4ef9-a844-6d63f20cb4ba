@echo off
title Instagram Smart Detector v4.1 - النظام المحسن
color 0A

echo.
echo ========================================
echo 🧠 Instagram Smart Detector v4.1
echo ========================================
echo 🔍 كشف الحسابات المفتوحة - محسن
echo 🌐 فتح نوافذ Instagram - مع تحكم كامل
echo 🔗 ربط تلقائي للحسابات - مع كشف يدوي
echo 💾 حفظ في قاعدة البيانات - محسن
echo ========================================
echo.

echo 🆕 التحسينات الجديدة في v4.1:
echo ========================================
echo ✅ مراقبة محسنة مع عداد وقت واضح (3 دقائق)
echo ✅ رسائل تقدم مفصلة تعرض حالة المراقبة
echo ✅ أزرار تحكم إضافية لإدارة النافذة
echo ✅ كشف يدوي للحساب عند الحاجة
echo ✅ معالجة أخطاء محسنة مع رسائل واضحة
echo ✅ حل مشكلة إغلاق النافذة مبكراً
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت مكتبات Python الأساسية...
python -m pip install flask flask-cors --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون قاعدة البيانات مهيأة مسبقاً
)
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🚀 تشغيل النظام المحسن...

echo 🔧 تشغيل Backend Server...
start "Backend Server" cmd /k "echo 🔧 Backend Server v4.1 - http://localhost:8000 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite && echo 🌐 API: http://localhost:8000 && echo 📈 الإحصائيات: /api/stats/system && echo 👥 الحسابات: /api/accounts && echo ===================================== && cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

echo 🌐 تشغيل Frontend المحسن...
start "Frontend v4.1" cmd /k "echo 🌐 Frontend v4.1 - http://localhost:3002 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 🧠 كاشف Instagram الذكي: محسن && echo 🔍 كشف الحسابات: تلقائي + يدوي && echo 🌐 فتح نوافذ جديدة: مع تحكم كامل && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام المحسن جاهز للاستخدام!
echo ========================================
echo 🔧 Backend: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 إحصائيات النظام: http://localhost:8000/api/stats/system
echo ========================================
echo.

echo 🧠 ميزات كاشف Instagram الذكي المحسن:
echo ========================================
echo ✅ مراقبة محسنة للنوافذ
echo   • مراقبة لمدة 3 دقائق مع عداد واضح
echo   • رسائل تقدم مفصلة كل 3 ثوان
echo   • عرض حالة المراقبة: (X/60)
echo   • شريط تقدم ديناميكي من 80% إلى 95%
echo.
echo ✅ أزرار تحكم جديدة
echo   • 🔴 إغلاق النافذة: لإنهاء المراقبة يدوياً
echo   • 🔵 التركيز على النافذة: لجعل النافذة في المقدمة
echo   • 🔍 كشف الحساب يدوياً: لفرض الكشف عند الحاجة
echo   • 🔄 إعادة المحاولة: لبدء العملية من جديد
echo.
echo ✅ كشف محسن للحسابات
echo   • كشف تلقائي كل 3 ثوان
echo   • كشف يدوي احتياطي
echo   • استخراج معلومات من URL
echo   • إنشاء حساب افتراضي عند الحاجة
echo.
echo ✅ معالجة أخطاء محسنة
echo   • رسائل خطأ واضحة ومفيدة
echo   • حلول مقترحة لكل مشكلة
echo   • إشعارات نجاح شاملة
echo   • تتبع مفصل للعمليات
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام المحسن:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات والاستخراجات
echo   • معدل النجاح ونشاط النظام
echo.
echo 🧠 كاشف Instagram الذكي المحسن:
echo   1. اضغط على "🧠 كشف ذكي للحسابات" في الشريط الجانبي
echo   2. سيبدأ الكشف التلقائي أو يفتح نافذة جديدة
echo   3. راقب رسائل التقدم: "مراقبة النافذة... (X/60)"
echo   4. سجل الدخول في النافذة المفتوحة
echo   5. انتقل للصفحة الرئيسية في Instagram
echo   6. انتظر الكشف التلقائي أو اضغط "🔍 كشف الحساب يدوياً"
echo   7. تحقق من إشعار النجاح: "تم ربط وحفظ حساب @username بنجاح!"
echo.
echo 🎛️ أزرار التحكم الجديدة:
echo   • 🔴 إغلاق النافذة: لإنهاء المراقبة
echo   • 🔵 التركيز على النافذة: لجعل النافذة نشطة
echo   • 🔍 كشف الحساب يدوياً: لفرض الكشف
echo   • 🔄 إعادة المحاولة: لبدء العملية من جديد
echo ========================================
echo.

echo 🧪 اختبار النظام المحسن:
echo ========================================
echo 📊 اختبار Backend:
echo    curl http://localhost:8000/api/stats/system
echo.
echo 👥 اختبار الحسابات:
echo    curl http://localhost:8000/api/accounts
echo.
echo 📈 اختبار الإحصائيات:
echo    curl http://localhost:8000/api/stats/dashboard
echo ========================================
echo.

echo 🛠️ حل المشاكل الشائعة:
echo ========================================
echo ❓ النافذة تُغلق قبل اكتشاف الحساب:
echo   ✅ لا تغلق النافذة بسرعة
echo   ✅ انتظر حتى تسجل الدخول كاملاً
echo   ✅ انتقل لصفحة Instagram الرئيسية
echo   ✅ استخدم "🔍 كشف الحساب يدوياً"
echo   ✅ راقب رسائل التقدم في الواجهة
echo.
echo ❓ المراقبة تستمر طويلاً:
echo   ✅ راقب العداد: (X/60) - لديك 3 دقائق
echo   ✅ تأكد من تسجيل الدخول في Instagram
echo   ✅ انتقل من صفحة تسجيل الدخول
echo   ✅ استخدم "🔍 كشف الحساب يدوياً"
echo   ✅ استخدم "🔵 التركيز على النافذة"
echo.
echo ❓ فشل في كشف الحساب:
echo   ✅ تأكد من تسجيل الدخول في Instagram
echo   ✅ انتقل لصفحة ملفك الشخصي
echo   ✅ اضغط "🔍 كشف الحساب يدوياً"
echo   ✅ جرب "🔄 إعادة المحاولة"
echo   ✅ أغلق النافذة وافتح نافذة جديدة
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo 🌐 للنافذة:
echo   • لا تغلق النافذة بسرعة - انتظر تسجيل الدخول
echo   • انتقل لصفحة Instagram الرئيسية بعد تسجيل الدخول
echo   • استخدم "التركيز على النافذة" إذا اختفت
echo   • راقب رسائل التقدم في الواجهة الرئيسية
echo.
echo 🔍 للكشف:
echo   • انتظر الكشف التلقائي - يحدث كل 3 ثوان
echo   • استخدم الكشف اليدوي إذا لم يحدث تلقائياً
echo   • تأكد من تسجيل الدخول كاملاً قبل الكشف
echo   • جرب إعادة المحاولة إذا فشل الكشف
echo.
echo 💾 للحفظ:
echo   • تحقق من إشعار النجاح بعد الكشف
echo   • راجع قائمة الحسابات للتأكد من الحفظ
echo   • احفظ نسخ احتياطية من قاعدة البيانات
echo   • نظف الحسابات القديمة دورياً
echo ========================================
echo.

echo 🔧 الميزات المتاحة في v4.1:
echo ========================================
echo ✅ كاشف Instagram الذكي المحسن
echo   • مراقبة محسنة مع عداد وقت
echo   • أزرار تحكم شاملة
echo   • كشف تلقائي + يدوي
echo   • معالجة أخطاء متقدمة
echo.
echo ✅ إدارة الحسابات
echo   • عرض وتعديل الحسابات
echo   • إضافة حسابات يدوياً
echo   • حذف الحسابات
echo   • إحصائيات مفصلة
echo.
echo ✅ لوحة التحكم
echo   • إحصائيات حقيقية
echo   • مراقبة النشاط
echo   • تتبع الأداء
echo   • تقارير شاملة
echo ========================================
echo.

echo 🎉 كاشف Instagram الذكي v4.1 جاهز للاستخدام!
echo 🧠 النظام محسن ويحل مشكلة إغلاق النافذة!
echo 🌐 أزرار تحكم جديدة لإدارة النوافذ!
echo 🔍 كشف تلقائي + يدوي للحسابات!
echo 💾 حفظ محسن في قاعدة البيانات!
echo.

echo 📝 ملاحظات مهمة للنسخة المحسنة:
echo ========================================
echo • النظام يراقب النافذة لمدة 3 دقائق كاملة
echo • رسائل التقدم تعرض العداد: (X/60)
echo • يمكنك استخدام الكشف اليدوي في أي وقت
echo • أزرار التحكم تساعدك في إدارة النافذة
echo • لا تغلق النافذة قبل اكتمال تسجيل الدخول
echo ========================================
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
