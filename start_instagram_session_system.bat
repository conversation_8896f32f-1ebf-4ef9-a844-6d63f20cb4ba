@echo off
title Instagram Session Manager v4.0 - نظام إدارة الجلسات المتقدم
color 0A

echo.
echo ========================================
echo 🔗 Instagram Session Manager v4.0
echo ========================================
echo 🔍 كشف الحسابات المفتوحة تلقائياً
echo 🍪 استخراج الكوكيز والجلسات
echo 🔗 ربط البرنامج بالحسابات المفتوحة
echo 🌐 تشغيل Instagram داخل البرنامج
echo ⚡ استخراج سريع بدون تسجيل دخول
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت مكتبات Python المطلوبة...
python -m pip install flask flask-cors selenium undetected-chromedriver cryptography --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py >nul 2>&1
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo ========================================
echo 🚀 بدء تشغيل نظام إدارة الجلسات...
echo ========================================

echo 🔧 تشغيل Backend Server الرئيسي...
start "Backend Server" cmd /k "echo 🔧 Backend Server Professional v4.0 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite && echo 🌐 API: http://localhost:8000 && echo 📈 الإحصائيات: /api/stats/system && echo 👥 الحسابات: /api/accounts && echo ===================================== && cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

echo 🔗 تشغيل Session API Server...
start "Session API Server" cmd /k "echo 🔗 Session API Server v4.0 && echo ================================ && echo 🔍 كشف الجلسات: POST /api/sessions/detect && echo 📱 الجلسات النشطة: GET /api/sessions/active && echo 🔗 الاتصال بجلسة: POST /api/sessions/connect && echo 🧹 تنظيف الجلسات: POST /api/sessions/cleanup && echo ================================ && cd backend && python session_api.py"

echo ⏳ انتظار تشغيل Session API...
timeout /t 5 /nobreak >nul

echo 🌐 تشغيل Frontend...
start "Frontend Professional" cmd /k "echo 🌐 Frontend Professional v4.0 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 📊 لوحة التحكم: إحصائيات حقيقية && echo 🔗 إدارة Instagram المتقدمة: متاحة && echo 🔍 كشف الجلسات: تلقائي && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo ✅ نظام إدارة الجلسات جاهز!
echo ========================================
echo 🔧 Backend API: http://localhost:8000
echo 🔗 Session API: http://localhost:8001
echo 🌐 Frontend: http://localhost:3002
echo 📊 إحصائيات النظام: http://localhost:8000/api/stats/system
echo 🔍 كشف الجلسات: http://localhost:8001/api/sessions/detect
echo ========================================
echo.

echo 🔗 ميزات إدارة Instagram المتقدمة:
echo ========================================
echo ✅ كشف الحسابات المفتوحة تلقائياً
echo   • فحص Chrome, Firefox, Edge
echo   • كشف تبويبات Instagram النشطة
echo   • استخراج معلومات المستخدم
echo   • التحقق من صحة الجلسات
echo.
echo ✅ استخراج وحفظ الكوكيز
echo   • استخراج جميع الكوكيز المطلوبة
echo   • تشفير البيانات الحساسة
echo   • حفظ localStorage و sessionStorage
echo   • حماية البيانات بـ AES-256
echo.
echo ✅ ربط البرنامج بالحسابات
echo   • إنشاء متصفح مع الجلسة المحفوظة
echo   • تطبيق الكوكيز تلقائياً
echo   • استعادة حالة المتصفح
echo   • التحقق من نجاح تسجيل الدخول
echo.
echo ✅ استخراج سريع بدون تسجيل دخول
echo   • استخدام الجلسات المحفوظة
echo   • تجاوز عملية تسجيل الدخول
echo   • استخراج مباشر من الحسابات المفتوحة
echo   • سرعة أعلى وأمان أكبر
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام السريع:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات والجلسات النشطة
echo   • معدل النجاح ونشاط النظام
echo.
echo 🔗 إدارة Instagram المتقدمة:
echo   1. اضغط على "🔗 إدارة Instagram المتقدمة" في الشريط الجانبي
echo   2. اضغط على "بدء الكشف" للبحث عن حسابات مفتوحة
echo   3. انتظر انتهاء عملية الكشف وظهور النتائج
echo   4. اضغط على "🔗 فتح الجلسة" للاتصال بحساب
echo   5. استخدم "⚡ استخراج سريع" للاستخراج المباشر
echo.
echo 👥 إدارة الحسابات:
echo   • عرض الحسابات المكتشفة والمحفوظة
echo   • معلومات مفصلة عن كل حساب
echo   • حالة الجلسة وآخر استخدام
echo   • إمكانية تنظيف الجلسات المنتهية
echo ========================================
echo.

echo 🧪 اختبار النظام:
echo ========================================
echo 📊 اختبار Backend الرئيسي:
echo    curl http://localhost:8000/api/stats/system
echo.
echo 🔗 اختبار Session API:
echo    curl http://localhost:8001/api/sessions/test
echo.
echo 🔍 كشف الجلسات:
echo    curl -X POST http://localhost:8001/api/sessions/detect
echo.
echo 📱 الجلسات النشطة:
echo    curl http://localhost:8001/api/sessions/active
echo ========================================
echo.

echo 🔧 API Endpoints الجديدة:
echo ========================================
echo Session Management API (Port 8001):
echo   POST /api/sessions/detect - كشف الجلسات المفتوحة
echo   GET  /api/sessions/active - الجلسات النشطة المحفوظة
echo   POST /api/sessions/connect - الاتصال بجلسة محفوظة
echo   POST /api/sessions/cleanup - تنظيف الجلسات المنتهية
echo   GET  /api/sessions/info - معلومات عامة عن الجلسات
echo   POST /api/sessions/browser/{id}/navigate - التنقل في المتصفح
echo   POST /api/sessions/browser/{id}/extract - الاستخراج من المتصفح
echo ========================================
echo.

echo 🔒 الأمان والخصوصية:
echo ========================================
echo ✅ تشفير الكوكيز بـ AES-256
echo ✅ حماية البيانات الحساسة
echo ✅ تشفير localStorage و sessionStorage
echo ✅ مفاتيح تشفير فريدة لكل تثبيت
echo ✅ عدم تخزين كلمات المرور
echo ✅ تنظيف تلقائي للجلسات المنتهية
echo ✅ حماية من الوصول غير المصرح
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo • تأكد من فتح Instagram في متصفحك قبل الكشف
echo • استخدم حسابات مختلفة في متصفحات مختلفة
echo • راقب حالة الجلسات في لوحة التحكم
echo • نظف الجلسات المنتهية الصلاحية دورياً
echo • استخدم الاستخراج السريع للحسابات المفتوحة
echo • احفظ نسخ احتياطية من قاعدة البيانات
echo ========================================
echo.

echo 🛠️ استكشاف الأخطاء:
echo ========================================
echo ❓ لا يتم كشف الجلسات:
echo   • تأكد من فتح Instagram في المتصفح
echo   • تأكد من تسجيل الدخول في Instagram
echo   • جرب إغلاق وإعادة فتح المتصفح
echo.
echo ❓ فشل في الاتصال بالجلسة:
echo   • تحقق من صحة الكوكيز المحفوظة
echo   • جرب إعادة كشف الجلسة
echo   • تأكد من عدم انتهاء صلاحية الجلسة
echo.
echo ❓ مشاكل في الاستخراج:
echo   • تحقق من صحة رابط المنشور
echo   • تأكد من أن الحساب لديه صلاحية الوصول
echo   • جرب استخدام حساب آخر
echo ========================================
echo.

echo 🎉 نظام إدارة الجلسات جاهز للاستخدام!
echo 🔗 يمكنك الآن ربط البرنامج بحساباتك المفتوحة!
echo 🍪 جميع الكوكيز محفوظة ومشفرة بأمان!
echo ⚡ استخراج سريع بدون تسجيل دخول!
echo 🌐 تشغيل Instagram داخل البرنامج!
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
