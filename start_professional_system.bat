@echo off
title Instagram Extractor Professional v4.0 - النظام الاحترافي
color 0A

echo.
echo ========================================
echo 🚀 Instagram Extractor Professional v4.0
echo ========================================
echo 🎨 واجهة احترافية متطورة
echo 🧠 نظام ذكي للكشف عن الحسابات
echo 📊 لوحة تحكم تفاعلية
echo 🔍 استخراج متقدم للمعجبين
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 بدء تشغيل Backend الاحترافي...
start "Backend Professional Server" cmd /k "echo 🔧 Backend Server && echo ================== && cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 3 /nobreak >nul

echo 🌐 بدء تشغيل Frontend الاحترافي...
start "Frontend Professional Server" cmd /k "echo 🌐 Frontend Server && echo ================== && cd frontend && npm run dev -- --port 3002"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام الاحترافي جاهز للاستخدام!
echo ========================================
echo 🔧 Backend API: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 API Status: http://localhost:8000/api/accounts
echo ========================================
echo.

echo 🎨 الميزات الجديدة:
echo ========================================
echo 🧠 كشف ذكي للحسابات المفتوحة
echo 📱 واجهة مستخدم احترافية
echo 🎯 لوحة تحكم تفاعلية
echo 📊 إحصائيات مفصلة ومباشرة
echo 🔍 استخراج متقدم مع تأثيرات بصرية
echo 🌙 وضع مظلم/فاتح تلقائي
echo 📱 تصميم متجاوب لجميع الأجهزة
echo 🎨 تأثيرات حركية متطورة
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 2 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام السريع:
echo ========================================
echo 1. 📊 لوحة التحكم: نظرة عامة شاملة
echo 2. 🧠 النظام الذكي: كشف الحسابات المفتوحة
echo 3. 👥 إدارة الحسابات: إضافة وإدارة الحسابات
echo 4. 🔍 استخراج المعجبين: استخراج متقدم
echo 5. 🌐 تصفح Instagram: تصفح مدمج
echo ========================================
echo.

echo 🧠 للكشف الذكي:
echo ========================================
echo 1. افتح Instagram في متصفحك وسجل دخولك
echo 2. انتقل إلى "🧠 النظام الذكي"
echo 3. انقر "🧠 كشف ذكي للحسابات"
echo 4. انقر "🔍 فحص الجلسات"
echo 5. سيكتشف حسابك تلقائياً!
echo 6. انقر "🔍 تفعيل الاستخراج"
echo 7. ارجع لنافذة Instagram واستخرج المعجبين
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo • استخدم الكشف الذكي لتوفير الوقت
echo • راقب الإحصائيات في لوحة التحكم
echo • استخدم الوضع المظلم للراحة البصرية
echo • جرب الاستخراج السريع من الروابط
echo • احفظ النتائج بصيغ متعددة
echo ========================================
echo.

echo 🔒 ملاحظات الأمان:
echo ========================================
echo • لا تشارك بيانات الحسابات مع أحد
echo • استخدم حسابات آمنة فقط
echo • تأكد من صحة الروابط قبل الاستخراج
echo • احترم قوانين Instagram وشروط الاستخدام
echo ========================================
echo.

echo 📞 الدعم والمساعدة:
echo ========================================
echo • راجع ملف SMART_DETECTION_GUIDE.md
echo • تحقق من سجل العمليات في النظام
echo • استخدم أدوات التشخيص المدمجة
echo ========================================
echo.

echo 🎉 استمتع بالنظام الاحترافي الجديد!
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
