@echo off
title Instagram Smart Detector - Simple Start
color 0A

echo.
echo ========================================
echo 🧠 Instagram Smart Detector
echo ========================================
echo 🔍 كشف الحسابات المفتوحة تلقائياً
echo 🌐 فتح نوافذ Instagram جديدة
echo 🔗 ربط تلقائي للحسابات
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت مكتبات Python...
python -m pip install flask flask-cors selenium undetected-chromedriver cryptography --quiet
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py >nul 2>&1
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🚀 تشغيل الخوادم...

echo 🔧 تشغيل Backend Server...
start "Backend Server" cmd /k "echo Backend Server - http://localhost:8000 && cd backend && python minimal_server.py"

echo ⏳ انتظار Backend...
timeout /t 3 /nobreak >nul

echo 🔗 تشغيل Session API Server...
start "Session API" cmd /k "echo Session API - http://localhost:8001 && cd backend && python session_api.py"

echo ⏳ انتظار Session API...
timeout /t 3 /nobreak >nul

echo 🌐 تشغيل Frontend...
start "Frontend" cmd /k "echo Frontend - http://localhost:3002 && cd frontend && npm run dev"

echo ⏳ انتظار Frontend...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام جاهز!
echo ========================================
echo 🔧 Backend: http://localhost:8000
echo 🔗 Session API: http://localhost:8001
echo 🌐 Frontend: http://localhost:3002
echo ========================================
echo.

echo 🌐 فتح المتصفح...
start http://localhost:3002

echo.
echo 🎯 للاستخدام:
echo 1. اذهب إلى "🧠 كشف ذكي للحسابات" في الشريط الجانبي
echo 2. سيبدأ الكشف التلقائي
echo 3. إذا لم توجد حسابات، ستفتح نافذة Instagram
echo 4. سجل الدخول وسيتم ربط الحساب تلقائياً
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
