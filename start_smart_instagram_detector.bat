@echo off
title Smart Instagram Detector v4.0 - كاشف Instagram الذكي
color 0A

echo.
echo ========================================
echo 🧠 Smart Instagram Detector v4.0
echo ========================================
echo 🔍 كشف الحسابات المفتوحة تلقائياً
echo 🌐 فتح نافذة Instagram جديدة
echo 🔗 ربط البرنامج بالحسابات المكتشفة
echo 💾 حفظ الحسابات في قاعدة البيانات
echo ⚡ استخراج سريع بدون تسجيل دخول
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت مكتبات Python المطلوبة...
python -m pip install flask flask-cors selenium undetected-chromedriver cryptography --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py >nul 2>&1
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo ========================================
echo 🚀 بدء تشغيل كاشف Instagram الذكي...
echo ========================================

echo 🔧 تشغيل Backend Server الرئيسي...
start "Backend Server" cmd /k "echo 🔧 Backend Server Professional v4.0 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite && echo 🌐 API: http://localhost:8000 && echo 📈 الإحصائيات: /api/stats/system && echo 👥 الحسابات: /api/accounts && echo ===================================== && cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

echo 🔗 تشغيل Session API Server...
start "Session API Server" cmd /k "echo 🔗 Session API Server v4.0 && echo ================================ && echo 🔍 كشف الجلسات: POST /api/sessions/detect && echo 📱 الجلسات النشطة: GET /api/sessions/active && echo 🔗 الاتصال بجلسة: POST /api/sessions/connect && echo 🧹 تنظيف الجلسات: POST /api/sessions/cleanup && echo ================================ && cd backend && python session_api.py"

echo ⏳ انتظار تشغيل Session API...
timeout /t 5 /nobreak >nul

echo 🌐 تشغيل Frontend...
start "Frontend Professional" cmd /k "echo 🌐 Frontend Professional v4.0 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 🧠 كاشف Instagram الذكي: متاح && echo 🔍 كشف تلقائي للحسابات: مفعل && echo 🌐 فتح نوافذ جديدة: متاح && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo ✅ كاشف Instagram الذكي جاهز!
echo ========================================
echo 🔧 Backend API: http://localhost:8000
echo 🔗 Session API: http://localhost:8001
echo 🌐 Frontend: http://localhost:3002
echo 📊 إحصائيات النظام: http://localhost:8000/api/stats/system
echo 🔍 كشف الجلسات: http://localhost:8001/api/sessions/detect
echo ========================================
echo.

echo 🧠 ميزات كاشف Instagram الذكي:
echo ========================================
echo ✅ كشف تلقائي للحسابات المفتوحة
echo   • فحص Chrome, Firefox, Edge
echo   • كشف تبويبات Instagram النشطة
echo   • استخراج معلومات المستخدم
echo   • التحقق من صحة الجلسات
echo.
echo ✅ فتح نوافذ Instagram جديدة
echo   • فتح نافذة Instagram تلقائياً
echo   • مراقبة تسجيل الدخول
echo   • كشف الحساب بعد تسجيل الدخول
echo   • ربط الحساب بالبرنامج تلقائياً
echo.
echo ✅ ربط البرنامج بالحسابات
echo   • حفظ الحسابات في قاعدة البيانات
echo   • ربط تلقائي بدون تدخل المستخدم
echo   • استخراج معلومات الحساب
echo   • حفظ الكوكيز والجلسات
echo.
echo ✅ استخراج سريع ومباشر
echo   • استخدام الحسابات المحفوظة
echo   • تجاوز عملية تسجيل الدخول
echo   • استخراج مباشر من الحسابات المفتوحة
echo   • سرعة أعلى وأمان أكبر
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام السريع:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات والجلسات النشطة
echo   • معدل النجاح ونشاط النظام
echo.
echo 🧠 كاشف Instagram الذكي:
echo   1. اضغط على "🧠 كشف ذكي للحسابات" في الشريط الجانبي
echo   2. سيبدأ الكشف التلقائي عن الحسابات المفتوحة
echo   3. إذا لم توجد حسابات، ستفتح نافذة Instagram جديدة
echo   4. سجل الدخول في النافذة الجديدة
echo   5. سيتم كشف الحساب وربطه تلقائياً
echo.
echo 👥 إدارة الحسابات:
echo   • عرض الحسابات المكتشفة والمحفوظة
echo   • معلومات مفصلة عن كل حساب
echo   • حالة الجلسة وآخر استخدام
echo   • إمكانية تنظيف الجلسات المنتهية
echo ========================================
echo.

echo 🧪 اختبار النظام:
echo ========================================
echo 📊 اختبار Backend الرئيسي:
echo    curl http://localhost:8000/api/stats/system
echo.
echo 🔗 اختبار Session API:
echo    curl http://localhost:8001/api/sessions/test
echo.
echo 🔍 كشف الجلسات:
echo    curl -X POST http://localhost:8001/api/sessions/detect
echo.
echo 📱 الجلسات النشطة:
echo    curl http://localhost:8001/api/sessions/active
echo ========================================
echo.

echo 🔧 API Endpoints الجديدة:
echo ========================================
echo Session Management API (Port 8001):
echo   POST /api/sessions/detect - كشف الجلسات المفتوحة
echo   GET  /api/sessions/active - الجلسات النشطة المحفوظة
echo   POST /api/sessions/connect - الاتصال بجلسة محفوظة
echo   POST /api/sessions/cleanup - تنظيف الجلسات المنتهية
echo   GET  /api/sessions/info - معلومات عامة عن الجلسات
echo   POST /api/sessions/browser/{id}/navigate - التنقل في المتصفح
echo   POST /api/sessions/browser/{id}/extract - الاستخراج من المتصفح
echo ========================================
echo.

echo 🔒 الأمان والخصوصية:
echo ========================================
echo ✅ تشفير الكوكيز بـ AES-256
echo ✅ حماية البيانات الحساسة
echo ✅ تشفير localStorage و sessionStorage
echo ✅ مفاتيح تشفير فريدة لكل تثبيت
echo ✅ عدم تخزين كلمات المرور
echo ✅ تنظيف تلقائي للجلسات المنتهية
echo ✅ حماية من الوصول غير المصرح
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo • تأكد من السماح بالنوافذ المنبثقة في المتصفح
echo • استخدم حسابات مختلفة في متصفحات مختلفة
echo • راقب حالة الجلسات في لوحة التحكم
echo • نظف الجلسات المنتهية الصلاحية دورياً
echo • استخدم الاستخراج السريع للحسابات المفتوحة
echo • احفظ نسخ احتياطية من قاعدة البيانات
echo ========================================
echo.

echo 🛠️ استكشاف الأخطاء:
echo ========================================
echo ❓ لا يتم كشف الجلسات:
echo   • تأكد من فتح Instagram في المتصفح
echo   • تأكد من تسجيل الدخول في Instagram
echo   • جرب إغلاق وإعادة فتح المتصفح
echo.
echo ❓ فشل في فتح نافذة جديدة:
echo   • تأكد من السماح بالنوافذ المنبثقة
echo   • جرب تعطيل مانع النوافذ المنبثقة
echo   • تأكد من عدم وجود برامج حماية تمنع النوافذ
echo.
echo ❓ مشاكل في ربط الحساب:
echo   • تحقق من صحة الكوكيز المحفوظة
echo   • جرب إعادة كشف الجلسة
echo   • تأكد من عدم انتهاء صلاحية الجلسة
echo ========================================
echo.

echo 🎉 كاشف Instagram الذكي جاهز للاستخدام!
echo 🧠 يمكنك الآن كشف الحسابات المفتوحة تلقائياً!
echo 🌐 فتح نوافذ Instagram جديدة وربطها بالبرنامج!
echo 🔗 ربط تلقائي للحسابات بدون تدخل المستخدم!
echo 💾 حفظ جميع الحسابات في قاعدة البيانات!
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
