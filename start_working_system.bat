@echo off
title Instagram Smart Detector - Working System
color 0A

echo.
echo ========================================
echo 🧠 Instagram Smart Detector - Working
echo ========================================
echo 🔍 كشف الحسابات المفتوحة
echo 🌐 فتح نوافذ Instagram جديدة
echo 🔗 ربط تلقائي للحسابات
echo 💾 حفظ في قاعدة البيانات
echo ========================================
echo.

echo 📦 التحقق من المتطلبات...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo 🔧 تثبيت مكتبات Python الأساسية...
python -m pip install flask flask-cors --quiet
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
)
echo ✅ تم تثبيت مكتبات Python

echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون قاعدة البيانات مهيأة مسبقاً
)
cd ..
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🚀 تشغيل النظام...

echo 🔧 تشغيل Backend Server...
start "Backend Server" cmd /k "echo 🔧 Backend Server - http://localhost:8000 && echo ===================================== && echo 📊 قاعدة البيانات: SQLite && echo 🌐 API: http://localhost:8000 && echo 📈 الإحصائيات: /api/stats/system && echo 👥 الحسابات: /api/accounts && echo ===================================== && cd backend && python minimal_server.py"

echo ⏳ انتظار تشغيل Backend...
timeout /t 5 /nobreak >nul

echo 🌐 تشغيل Frontend...
start "Frontend" cmd /k "echo 🌐 Frontend - http://localhost:3002 && echo ============================== && echo 📱 الواجهة: http://localhost:3002 && echo 🧠 كاشف Instagram الذكي: متاح && echo 🔍 كشف الحسابات: متاح && echo 🌐 فتح نوافذ جديدة: متاح && echo ============================== && cd frontend && npm run dev"

echo ⏳ انتظار تشغيل Frontend...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo ✅ النظام جاهز للاستخدام!
echo ========================================
echo 🔧 Backend: http://localhost:8000
echo 🌐 Frontend: http://localhost:3002
echo 📊 إحصائيات النظام: http://localhost:8000/api/stats/system
echo ========================================
echo.

echo 🧠 ميزات كاشف Instagram الذكي:
echo ========================================
echo ✅ فتح نوافذ Instagram جديدة
echo   • فتح نافذة Instagram تلقائياً
echo   • مراقبة تسجيل الدخول
echo   • كشف الحساب بعد تسجيل الدخول
echo   • ربط الحساب بالبرنامج تلقائياً
echo.
echo ✅ ربط البرنامج بالحسابات
echo   • حفظ الحسابات في قاعدة البيانات
echo   • ربط تلقائي بدون تدخل المستخدم
echo   • استخراج معلومات الحساب
echo   • حفظ معلومات المستخدم
echo.
echo ✅ إدارة الحسابات
echo   • عرض الحسابات المحفوظة
echo   • معلومات مفصلة عن كل حساب
echo   • إمكانية تعديل وحذف الحسابات
echo   • إحصائيات شاملة
echo ========================================
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:3002

echo.
echo ========================================
echo 🎯 دليل الاستخدام السريع:
echo ========================================
echo 📊 لوحة التحكم:
echo   • إحصائيات حقيقية من قاعدة البيانات
echo   • عدد الحسابات والاستخراجات
echo   • معدل النجاح ونشاط النظام
echo.
echo 🧠 كاشف Instagram الذكي:
echo   1. اضغط على "🧠 كشف ذكي للحسابات" في الشريط الجانبي
echo   2. سيبدأ الكشف التلقائي (أو يفتح نافذة جديدة)
echo   3. سجل الدخول في النافذة المفتوحة
echo   4. سيتم كشف الحساب وربطه تلقائياً
echo   5. ستجد الحساب محفوظ في قائمة الحسابات
echo.
echo 👥 إدارة الحسابات:
echo   • عرض الحسابات المحفوظة
echo   • إضافة حسابات جديدة يدوياً
echo   • تعديل معلومات الحسابات
echo   • حذف الحسابات غير المرغوبة
echo ========================================
echo.

echo 🧪 اختبار النظام:
echo ========================================
echo 📊 اختبار Backend:
echo    curl http://localhost:8000/api/stats/system
echo.
echo 👥 اختبار الحسابات:
echo    curl http://localhost:8000/api/accounts
echo.
echo 📈 اختبار الإحصائيات:
echo    curl http://localhost:8000/api/stats/dashboard
echo ========================================
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo ========================================
echo • تأكد من السماح بالنوافذ المنبثقة في المتصفح
echo • استخدم حسابات مختلفة للاختبار
echo • راقب الحسابات المحفوظة في لوحة التحكم
echo • استخدم إدارة الحسابات لتنظيم حساباتك
echo • احفظ نسخ احتياطية من قاعدة البيانات
echo ========================================
echo.

echo 🛠️ استكشاف الأخطاء:
echo ========================================
echo ❓ فشل في فتح نافذة جديدة:
echo   • تأكد من السماح بالنوافذ المنبثقة
echo   • جرب تعطيل مانع النوافذ المنبثقة
echo   • تأكد من عدم وجود برامج حماية تمنع النوافذ
echo.
echo ❓ مشاكل في ربط الحساب:
echo   • تأكد من تسجيل الدخول في Instagram
echo   • جرب إعادة تحميل الصفحة
echo   • تحقق من اتصال الإنترنت
echo.
echo ❓ مشاكل في Backend:
echo   • تحقق من أن Python مثبت بشكل صحيح
echo   • تأكد من أن المنفذ 8000 غير مستخدم
echo   • جرب إعادة تشغيل Backend Server
echo ========================================
echo.

echo 🔧 الميزات المتاحة:
echo ========================================
echo ✅ كاشف Instagram الذكي
echo   • كشف تلقائي للحسابات المفتوحة
echo   • فتح نوافذ Instagram جديدة
echo   • ربط تلقائي للحسابات
echo   • حفظ في قاعدة البيانات
echo.
echo ✅ إدارة الحسابات
echo   • عرض وتعديل الحسابات
echo   • إضافة حسابات يدوياً
echo   • حذف الحسابات
echo   • إحصائيات مفصلة
echo.
echo ✅ لوحة التحكم
echo   • إحصائيات حقيقية
echo   • مراقبة النشاط
echo   • تتبع الأداء
echo   • تقارير شاملة
echo ========================================
echo.

echo 🎉 كاشف Instagram الذكي جاهز للاستخدام!
echo 🧠 يمكنك الآن كشف وربط الحسابات بسهولة!
echo 🌐 فتح نوافذ Instagram جديدة وربطها بالبرنامج!
echo 🔗 ربط تلقائي للحسابات بدون تدخل المستخدم!
echo 💾 حفظ جميع الحسابات في قاعدة البيانات!
echo.

echo 📝 ملاحظة مهمة:
echo ========================================
echo • النظام يعمل في الوضع البديل (بدون Session API)
echo • سيتم فتح نافذة Instagram جديدة مباشرة
echo • تأكد من السماح بالنوافذ المنبثقة
echo • سجل الدخول في النافذة المفتوحة
echo • سيتم كشف الحساب وربطه تلقائياً
echo ========================================
echo.

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
