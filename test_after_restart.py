#!/usr/bin/env python3
"""
اختبار شامل بعد إعادة تشغيل المشروع
"""

import requests
import json
import time
import random

def test_backend_connection():
    """اختبار الاتصال بـ Backend"""
    print("🔧 اختبار الاتصال بـ Backend...")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend متصل: {data.get('message', 'OK')}")
            return True
        else:
            print(f"❌ Backend يرجع خطأ: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend غير متاح على المنفذ 8000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_system_stats():
    """اختبار إحصائيات النظام"""
    print("\n📊 اختبار إحصائيات النظام...")
    
    try:
        response = requests.get("http://localhost:8000/api/stats/system", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ إجمالي الحسابات: {stats.get('total_accounts', 0)}")
            print(f"✅ الحسابات النشطة: {stats.get('active_accounts', 0)}")
            print(f"✅ إجمالي المهام: {stats.get('total_jobs', 0)}")
            return True
        else:
            print(f"❌ فشل في الحصول على الإحصائيات: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في الإحصائيات: {e}")
        return False

def test_accounts_list():
    """اختبار قائمة الحسابات"""
    print("\n👥 اختبار قائمة الحسابات...")
    
    try:
        response = requests.get("http://localhost:8000/api/accounts", timeout=10)
        if response.status_code == 200:
            accounts = response.json()
            print(f"✅ عدد الحسابات الموجودة: {len(accounts)}")
            
            if accounts:
                print("📄 أول حساب:")
                account = accounts[0]
                print(f"   ID: {account.get('id')}")
                print(f"   اسم المستخدم: {account.get('username')}")
                print(f"   الاسم: {account.get('name')}")
                print(f"   نشط: {account.get('is_active')}")
                print(f"   2FA: {account.get('has_2fa', False)}")
            
            return True
        else:
            print(f"❌ فشل في الحصول على الحسابات: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في قائمة الحسابات: {e}")
        return False

def test_auto_detected_endpoint():
    """اختبار endpoint الكشف التلقائي"""
    print("\n🔍 اختبار Auto-Detected Endpoint...")
    
    # بيانات اختبار عشوائية
    test_username = f"test_user_{random.randint(1000, 9999)}"
    test_data = {
        "username": test_username,
        "name": f"حساب اختبار {test_username}",
        "is_verified": random.choice([True, False]),
        "follower_count": random.randint(100, 5000),
        "following_count": random.randint(50, 1000),
        "notes": f"تم إنشاؤه لاختبار إعادة التشغيل في {time.strftime('%Y-%m-%d %H:%M:%S')}"
    }
    
    print(f"📝 بيانات الاختبار: {test_username}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/accounts/auto-detected",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ تم إنشاء الحساب بنجاح!")
            print(f"   ID: {result.get('id')}")
            print(f"   اسم المستخدم: {result.get('username')}")
            print(f"   الاسم: {result.get('name')}")
            print(f"   آخر استخدام: {result.get('last_used')}")
            print(f"   الملاحظات: {result.get('notes', '')[:50]}...")
            return True, result.get('id')
        else:
            print(f"❌ فشل في إنشاء الحساب: {response.status_code}")
            try:
                error = response.json()
                print(f"📄 رسالة الخطأ: {error}")
            except:
                print(f"📄 رسالة الخطأ: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ خطأ في Auto-Detected Endpoint: {e}")
        return False, None

def test_duplicate_account():
    """اختبار إضافة حساب مكرر"""
    print("\n🔄 اختبار الحساب المكرر...")
    
    # استخدام نفس البيانات مرتين
    test_data = {
        "username": "duplicate_test_user",
        "name": "حساب اختبار مكرر",
        "notes": "اختبار الحساب المكرر - المرة الأولى"
    }
    
    try:
        # المحاولة الأولى
        response1 = requests.post(
            "http://localhost:8000/api/accounts/auto-detected",
            json=test_data,
            timeout=10
        )
        
        # المحاولة الثانية
        test_data["notes"] = "اختبار الحساب المكرر - المرة الثانية"
        response2 = requests.post(
            "http://localhost:8000/api/accounts/auto-detected",
            json=test_data,
            timeout=10
        )
        
        if response1.status_code == 200 and response2.status_code == 200:
            result1 = response1.json()
            result2 = response2.json()
            
            if result1.get('id') == result2.get('id'):
                print("✅ الحساب المكرر تم تحديثه بدلاً من إنشاء جديد")
                print(f"   ID: {result2.get('id')}")
                print(f"   الملاحظات المحدثة: {result2.get('notes', '')[:50]}...")
                return True
            else:
                print("⚠️ تم إنشاء حسابين منفصلين للمستخدم نفسه")
                return False
        else:
            print(f"❌ فشل في اختبار الحساب المكرر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحساب المكرر: {e}")
        return False

def test_frontend_connection():
    """اختبار الاتصال بـ Frontend"""
    print("\n🌐 اختبار الاتصال بـ Frontend...")
    
    try:
        response = requests.get("http://localhost:3002/", timeout=10)
        if response.status_code == 200:
            print("✅ Frontend متاح على المنفذ 3002")
            return True
        else:
            print(f"❌ Frontend يرجع خطأ: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend غير متاح على المنفذ 3002")
        print("🔧 تأكد من تشغيل: npm run dev في مجلد frontend")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Frontend: {e}")
        return False

def test_api_docs():
    """اختبار صفحة API Docs"""
    print("\n📚 اختبار API Documentation...")
    
    try:
        response = requests.get("http://localhost:8000/api/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API Docs متاحة على: http://localhost:8000/api/docs")
            return True
        else:
            print(f"❌ API Docs غير متاحة: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في API Docs: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    
    print("🚀 بدء اختبار شامل بعد إعادة التشغيل")
    print("=" * 60)
    
    tests = [
        ("الاتصال بـ Backend", test_backend_connection),
        ("إحصائيات النظام", test_system_stats),
        ("قائمة الحسابات", test_accounts_list),
        ("Auto-Detected Endpoint", test_auto_detected_endpoint),
        ("الحساب المكرر", test_duplicate_account),
        ("الاتصال بـ Frontend", test_frontend_connection),
        ("API Documentation", test_api_docs),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_name == "Auto-Detected Endpoint":
                result, account_id = test_func()
                results.append((test_name, result))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ المشروع يعمل بشكل مثالي بعد إعادة التشغيل")
        print("\n🌐 يمكنك الآن استخدام:")
        print("   Frontend: http://localhost:3002")
        print("   Backend API: http://localhost:8000")
        print("   API Docs: http://localhost:8000/api/docs")
        print("\n🧠 جرب الكشف التلقائي في Frontend!")
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإعادة تشغيل المشروع")
        
        if passed >= total * 0.7:  # إذا نجح 70% أو أكثر
            print("\n💡 معظم الاختبارات نجحت - المشروع قابل للاستخدام")

if __name__ == "__main__":
    main()
