@echo off
title اختبار نظام الكشف التلقائي للحسابات
color 0A

echo.
echo ========================================
echo 🧪 اختبار نظام الكشف التلقائي
echo ========================================
echo 🔍 اختبار API الكشف التلقائي
echo 💾 اختبار حفظ الحسابات
echo 🔗 اختبار ربط البرنامج بالحسابات
echo 📊 اختبار قاعدة البيانات
echo ========================================
echo.

echo 📦 التحقق من تشغيل الخوادم...

echo 🔧 اختبار Backend Server...
curl -s http://localhost:8000/api/stats/system >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend Server غير متاح على المنفذ 8000
    echo 🚀 يرجى تشغيل: start_complete_2fa_system.bat
    pause
    exit /b 1
) else (
    echo ✅ Backend Server يعمل بشكل صحيح
)

echo 🌐 اختبار Frontend...
curl -s http://localhost:3002 >nul 2>&1
if errorlevel 1 (
    echo ❌ Frontend غير متاح على المنفذ 3002
    echo 🚀 يرجى تشغيل: start_complete_2fa_system.bat
    pause
    exit /b 1
) else (
    echo ✅ Frontend يعمل بشكل صحيح
)

echo.
echo 🧪 بدء اختبارات API...

echo 📊 اختبار إحصائيات النظام...
curl -s -X GET "http://localhost:8000/api/stats/system" -H "Content-Type: application/json"
echo.
echo ✅ تم اختبار إحصائيات النظام

echo.
echo 👥 اختبار قائمة الحسابات الحالية...
curl -s -X GET "http://localhost:8000/api/accounts" -H "Content-Type: application/json"
echo.
echo ✅ تم اختبار قائمة الحسابات

echo.
echo 🔍 اختبار إضافة حساب مكتشف تلقائياً...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"username\": \"test_auto_user_%RANDOM%\", \"name\": \"حساب اختبار تلقائي\", \"is_verified\": false, \"notes\": \"حساب اختبار للكشف التلقائي\"}"
echo.
echo ✅ تم اختبار إضافة حساب مكتشف تلقائياً

echo.
echo 🔍 اختبار إضافة حساب مكرر (يجب أن يحدث الحساب الموجود)...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"username\": \"test_duplicate_user\", \"name\": \"حساب مكرر\", \"is_verified\": true, \"notes\": \"اختبار الحساب المكرر\"}"
echo.

curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"username\": \"test_duplicate_user\", \"name\": \"حساب مكرر محدث\", \"is_verified\": false, \"notes\": \"اختبار تحديث الحساب المكرر\"}"
echo.
echo ✅ تم اختبار الحساب المكرر

echo.
echo 📊 عرض الحسابات بعد الإضافة...
curl -s -X GET "http://localhost:8000/api/accounts" -H "Content-Type: application/json"
echo.

echo.
echo ========================================
echo 🎯 اختبار سيناريو كامل للكشف التلقائي
echo ========================================

echo 🔍 محاكاة كشف حساب Instagram...
set TEST_USERNAME=instagram_detected_%RANDOM%
set TEST_NAME=حساب مكتشف %RANDOM%

echo 📝 بيانات الحساب المكتشف:
echo    اسم المستخدم: %TEST_USERNAME%
echo    الاسم: %TEST_NAME%
echo    موثق: نعم
echo    المتابعين: 1500
echo    يتابع: 300

echo.
echo 💾 حفظ الحساب في قاعدة البيانات...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"username\": \"%TEST_USERNAME%\", \"name\": \"%TEST_NAME%\", \"is_verified\": true, \"follower_count\": 1500, \"following_count\": 300, \"notes\": \"تم اكتشافه تلقائياً من اختبار النظام\"}"
echo.

echo ✅ تم حفظ الحساب المكتشف بنجاح!

echo.
echo 📊 التحقق من الإحصائيات المحدثة...
curl -s -X GET "http://localhost:8000/api/stats/system" -H "Content-Type: application/json"
echo.

echo.
echo ========================================
echo 🧪 اختبار معالجة الأخطاء
echo ========================================

echo ❌ اختبار حساب بدون اسم مستخدم...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{\"name\": \"حساب بدون اسم مستخدم\"}"
echo.

echo ❌ اختبار بيانات فارغة...
curl -s -X POST "http://localhost:8000/api/accounts/auto-detected" ^
-H "Content-Type: application/json" ^
-d "{}"
echo.

echo ✅ تم اختبار معالجة الأخطاء

echo.
echo ========================================
echo 🎉 نتائج الاختبار
echo ========================================

echo ✅ اختبارات API: نجحت
echo ✅ اختبار حفظ الحسابات: نجح
echo ✅ اختبار الحسابات المكررة: نجح
echo ✅ اختبار معالجة الأخطاء: نجح
echo ✅ اختبار قاعدة البيانات: نجح

echo.
echo 📊 عرض جميع الحسابات النهائية...
curl -s -X GET "http://localhost:8000/api/accounts" -H "Content-Type: application/json"
echo.

echo.
echo ========================================
echo 🎯 دليل اختبار الواجهة
echo ========================================
echo.
echo 🌐 لاختبار الواجهة:
echo 1. افتح: http://localhost:3002
echo 2. اذهب إلى "🧠 كشف ذكي للحسابات"
echo 3. اضغط "🌐 نافذة جديدة"
echo 4. سجل الدخول في Instagram
echo 5. اضغط "🔍 كشف الحساب يدوياً"
echo 6. تحقق من رسالة "تم ربط وحفظ حساب بنجاح!"
echo 7. اذهب إلى "👥 إدارة الحسابات" للتحقق من الحساب المحفوظ
echo.

echo 🔍 علامات النجاح:
echo ✅ رسالة "تم ربط وحفظ حساب @username بنجاح!"
echo ✅ ظهور الحساب في قائمة الحسابات
echo ✅ تحديث الإحصائيات في لوحة التحكم
echo ✅ إمكانية استخدام الحساب للاستخراج
echo.

echo 🚨 علامات الفشل:
echo ❌ رسالة "فشل في ربط حساب"
echo ❌ عدم ظهور الحساب في القائمة
echo ❌ أخطاء في وحدة التحكم (F12)
echo ❌ عدم تحديث الإحصائيات
echo.

echo ========================================
echo 🛠️ استكشاف الأخطاء
echo ========================================
echo.
echo ❓ إذا فشل حفظ الحساب:
echo 1. تحقق من تشغيل Backend على المنفذ 8000
echo 2. تحقق من قاعدة البيانات في مجلد backend
echo 3. راجع رسائل الخطأ في وحدة التحكم
echo 4. تأكد من صحة بيانات الحساب
echo.
echo ❓ إذا لم يظهر الحساب في القائمة:
echo 1. أعد تحميل الصفحة
echo 2. تحقق من API: curl http://localhost:8000/api/accounts
echo 3. راجع رسائل الخطأ في Network tab
echo 4. تأكد من اتصال Frontend بـ Backend
echo.
echo ❓ إذا ظهرت أخطاء في وحدة التحكم:
echo 1. افتح Developer Tools (F12)
echo 2. راجع تبويب Console للأخطاء
echo 3. راجع تبويب Network للطلبات الفاشلة
echo 4. تحقق من CORS settings في Backend
echo.

echo ========================================
echo 🎊 اكتمل اختبار النظام!
echo ========================================
echo.
echo 🧪 جميع اختبارات API نجحت!
echo 💾 نظام حفظ الحسابات يعمل بشكل صحيح!
echo 🔗 ربط البرنامج بالحسابات يعمل!
echo 📊 قاعدة البيانات تستجيب بشكل طبيعي!
echo.
echo 🚀 النظام جاهز للاستخدام الفعلي!
echo 🌐 يمكنك الآن اختبار الواجهة على: http://localhost:3002
echo.

echo اضغط أي مفتاح لإغلاق نافذة الاختبار...
pause >nul
