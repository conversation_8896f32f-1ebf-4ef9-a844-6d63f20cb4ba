#!/usr/bin/env python3
"""
اختبار سريع للاتصال بين Frontend و Backend
"""

import requests
import json

def test_backend_root():
    """اختبار الصفحة الرئيسية للـ Backend"""
    print("🔧 اختبار Backend Root Endpoint...")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ استجابة Backend:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data.get('success') == True:
                print("✅ Backend متصل ويعمل بشكل صحيح")
                return True
            else:
                print("❌ Backend يرجع success=false")
                return False
        else:
            print(f"❌ Backend يرجع خطأ: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend غير متاح على المنفذ 8000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_frontend_connection():
    """اختبار الاتصال بـ Frontend"""
    print("\n🌐 اختبار Frontend...")
    
    try:
        response = requests.get("http://localhost:3002/", timeout=5)
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Frontend متاح ويعمل")
            return True
        else:
            print(f"❌ Frontend يرجع خطأ: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Frontend غير متاح على المنفذ 3002")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_auto_detected_endpoint():
    """اختبار Auto-Detected Endpoint"""
    print("\n🔍 اختبار Auto-Detected Endpoint...")
    
    test_data = {
        "username": "connection_test_user",
        "name": "حساب اختبار الاتصال",
        "notes": "تم إنشاؤه لاختبار الاتصال"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/accounts/auto-detected",
            json=test_data,
            timeout=10
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Auto-Detected Endpoint يعمل!")
            print(f"   ID: {result.get('id')}")
            print(f"   اسم المستخدم: {result.get('username')}")
            return True
        else:
            print(f"❌ Auto-Detected Endpoint فشل: {response.status_code}")
            try:
                error = response.json()
                print(f"📄 رسالة الخطأ: {error}")
            except:
                print(f"📄 رسالة الخطأ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Auto-Detected Endpoint: {e}")
        return False

def test_cors():
    """اختبار CORS"""
    print("\n🌐 اختبار CORS...")
    
    try:
        # محاكاة طلب من Frontend
        headers = {
            'Origin': 'http://localhost:3002',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            "http://localhost:8000/",
            headers=headers,
            timeout=5
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        print(f"📋 CORS Headers: {response.headers.get('Access-Control-Allow-Origin', 'غير موجود')}")
        
        if response.status_code == 200:
            print("✅ CORS يعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في CORS")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار CORS: {e}")
        return False

def main():
    """تشغيل جميع اختبارات الاتصال"""
    
    print("🚀 اختبار الاتصال بين Frontend و Backend")
    print("=" * 60)
    
    tests = [
        ("Backend Root", test_backend_root),
        ("Frontend", test_frontend_connection),
        ("Auto-Detected Endpoint", test_auto_detected_endpoint),
        ("CORS", test_cors),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 40)
        
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: نجح")
        else:
            print(f"❌ {test_name}: فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبارات الاتصال:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name:<25} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع اختبارات الاتصال نجحت!")
        print("✅ Frontend يجب أن يتصل بـ Backend الآن")
        print("\n🌐 افتح المتصفح على: http://localhost:3002")
        print("🔧 يجب أن ترى رسالة: 'تم الاتصال بالخادم بنجاح'")
        print("🧠 جرب الكشف التلقائي الآن!")
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
        
        if passed >= 2:  # إذا نجح Backend و Frontend على الأقل
            print("\n💡 Backend و Frontend يعملان - المشكلة قد تكون في التفاصيل")
            print("🔄 جرب إعادة تحميل الصفحة في المتصفح")

if __name__ == "__main__":
    main()
