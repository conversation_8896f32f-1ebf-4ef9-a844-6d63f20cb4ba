#!/usr/bin/env python3
"""
اختبار سريع للـ endpoint الجديد
"""

import requests
import json

def test_auto_detected_endpoint():
    """اختبار endpoint إضافة الحساب المكتشف تلقائياً"""
    
    base_url = "http://localhost:8000"
    endpoint = "/api/accounts/auto-detected"
    url = base_url + endpoint
    
    print("🧪 اختبار endpoint إضافة الحساب المكتشف تلقائياً")
    print(f"📍 URL: {url}")
    print("=" * 50)
    
    # بيانات الاختبار
    test_data = {
        "username": f"test_user_{hash('test') % 10000}",
        "name": "حساب اختبار تلقائي",
        "is_verified": False,
        "follower_count": 1500,
        "following_count": 300,
        "notes": "حساب اختبار للكشف التلقائي"
    }
    
    print("📝 بيانات الاختبار:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # إرسال الطلب
        print("📤 إرسال الطلب...")
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        print(f"📋 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ نجح الطلب!")
            result = response.json()
            print("📄 الاستجابة:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # التحقق من البيانات المطلوبة
            if 'id' in result and 'username' in result:
                print(f"✅ تم إنشاء الحساب بـ ID: {result['id']}")
                print(f"✅ اسم المستخدم: {result['username']}")
                return True
            else:
                print("❌ البيانات المرجعة غير مكتملة")
                return False
                
        else:
            print("❌ فشل الطلب!")
            try:
                error_data = response.json()
                print("📄 رسالة الخطأ:")
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
            except:
                print("📄 رسالة الخطأ (نص):")
                print(response.text)
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ فشل في الاتصال بالخادم")
        print("🔧 تأكد من تشغيل Backend على المنفذ 8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الطلب")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_get_accounts():
    """اختبار endpoint عرض الحسابات"""
    
    url = "http://localhost:8000/api/accounts"
    
    print("\n🧪 اختبار endpoint عرض الحسابات")
    print(f"📍 URL: {url}")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ نجح الطلب!")
            accounts = response.json()
            print(f"📊 عدد الحسابات: {len(accounts)}")
            
            if accounts:
                print("📄 أول حساب:")
                print(json.dumps(accounts[0], indent=2, ensure_ascii=False))
            else:
                print("📄 لا توجد حسابات")
            return True
        else:
            print("❌ فشل الطلب!")
            print(f"📄 رسالة الخطأ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_system_stats():
    """اختبار endpoint إحصائيات النظام"""
    
    url = "http://localhost:8000/api/stats/system"
    
    print("\n🧪 اختبار endpoint إحصائيات النظام")
    print(f"📍 URL: {url}")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"📊 رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ نجح الطلب!")
            stats = response.json()
            print("📄 الإحصائيات:")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
            return True
        else:
            print("❌ فشل الطلب!")
            print(f"📄 رسالة الخطأ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    
    print("🚀 بدء اختبار النظام")
    print("=" * 60)
    
    # اختبار الاتصال الأساسي
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend متصل ويعمل")
        else:
            print("❌ Backend لا يستجيب بشكل صحيح")
            return
    except:
        print("❌ Backend غير متاح - يرجى تشغيله أولاً")
        print("🔧 شغل: cd backend && python main.py")
        return
    
    # تشغيل الاختبارات
    tests = [
        ("إحصائيات النظام", test_system_stats),
        ("عرض الحسابات", test_get_accounts),
        ("إضافة حساب مكتشف تلقائياً", test_auto_detected_endpoint),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: نجح")
        else:
            print(f"❌ {test_name}: فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
