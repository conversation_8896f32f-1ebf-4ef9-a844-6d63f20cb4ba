#!/usr/bin/env python3
"""
Test script for Instagram Extractor
سكريبت اختبار لمستخرج إنستجرام
"""

import json
import sys
import os

# إضافة مجلد scripts إلى المسار
sys.path.append('scripts')

def test_extractor():
    """اختبار مستخرج إنستجرام"""
    print("🧪 اختبار مستخرج إنستجرام")
    print("=" * 50)
    
    # بيانات تجريبية
    sample_data = [
        {
            "username": "test_user_1",
            "profile_pic_url": "https://via.placeholder.com/150x150/e1306c/ffffff?text=IG",
            "profile_url": "https://www.instagram.com/test_user_1/",
            "full_name": "Test User One",
            "is_verified": False,
            "follower_count": 1500,
            "following_count": 300,
            "post_count": 45,
            "bio": "This is a test bio for user one",
            "threads_url": "https://www.threads.net/@test_user_1",
            "facebook_url": None,
            "extracted_at": "2024-01-01T12:00:00"
        },
        {
            "username": "verified_user",
            "profile_pic_url": "https://via.placeholder.com/150x150/833ab4/ffffff?text=VU",
            "profile_url": "https://www.instagram.com/verified_user/",
            "full_name": "Verified User",
            "is_verified": True,
            "follower_count": 50000,
            "following_count": 100,
            "post_count": 200,
            "bio": "Verified account with Facebook link: facebook.com/verifieduser",
            "threads_url": "https://www.threads.net/@verified_user",
            "facebook_url": "https://www.facebook.com/verifieduser",
            "extracted_at": "2024-01-01T12:00:00"
        },
        {
            "username": "influencer_account",
            "profile_pic_url": "https://via.placeholder.com/150x150/fd1d1d/ffffff?text=IA",
            "profile_url": "https://www.instagram.com/influencer_account/",
            "full_name": "Influencer Account",
            "is_verified": True,
            "follower_count": 1200000,
            "following_count": 500,
            "post_count": 800,
            "bio": "Content creator and influencer 🌟",
            "threads_url": "https://www.threads.net/@influencer_account",
            "facebook_url": None,
            "extracted_at": "2024-01-01T12:00:00"
        }
    ]
    
    print(f"✅ تم إنشاء {len(sample_data)} حساب تجريبي")
    
    # اختبار تصدير البيانات
    try:
        from data_exporter import DataExporter
        
        exporter = DataExporter()
        print("\n📤 اختبار تصدير البيانات...")
        
        # تصدير CSV
        try:
            csv_file = exporter.export_csv(sample_data)
            print(f"✅ تم تصدير CSV: {csv_file}")
        except Exception as e:
            print(f"❌ خطأ في تصدير CSV: {e}")
        
        # تصدير JSON
        try:
            json_file = exporter.export_json(sample_data)
            print(f"✅ تم تصدير JSON: {json_file}")
        except Exception as e:
            print(f"❌ خطأ في تصدير JSON: {e}")
        
        # تصدير Excel
        try:
            excel_file = exporter.export_excel(sample_data)
            print(f"✅ تم تصدير Excel: {excel_file}")
        except Exception as e:
            print(f"❌ خطأ في تصدير Excel: {e}")
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد data_exporter: {e}")
    
    # طباعة البيانات التجريبية
    print("\n📊 البيانات التجريبية:")
    print(json.dumps(sample_data, ensure_ascii=False, indent=2))
    
    return sample_data

def test_instagram_url_validation():
    """اختبار التحقق من روابط إنستجرام"""
    print("\n🔗 اختبار التحقق من الروابط")
    print("-" * 30)
    
    test_urls = [
        "https://www.instagram.com/p/ABC123/",
        "https://instagram.com/p/XYZ789/",
        "https://www.instagram.com/reel/DEF456/",
        "https://instagram.com/reel/GHI789/",
        "https://www.facebook.com/post/123",  # خطأ
        "not_a_url",  # خطأ
        "https://www.instagram.com/username/",  # خطأ
    ]
    
    import re
    instagram_post_regex = r'^https?://(www\.)?instagram\.com/(p|reel)/[A-Za-z0-9_-]+/?$'
    
    for url in test_urls:
        is_valid = bool(re.match(instagram_post_regex, url))
        status = "✅" if is_valid else "❌"
        print(f"{status} {url}")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 بدء اختبار تطبيق Instagram Likes Extractor")
    print("=" * 60)
    
    # اختبار التحقق من الروابط
    test_instagram_url_validation()
    
    # اختبار المستخرج
    sample_data = test_extractor()
    
    print("\n🎉 انتهى الاختبار بنجاح!")
    print("\n📝 ملاحظات:")
    print("- الواجهة الأمامية تعمل على: http://localhost:1420/")
    print("- ملفات التصدير في مجلد: exports/")
    print("- لتشغيل التطبيق كاملاً، تحتاج إلى تثبيت Rust أولاً")
    print("- يمكنك اختبار Python scripts بشكل منفصل")

if __name__ == "__main__":
    main()
