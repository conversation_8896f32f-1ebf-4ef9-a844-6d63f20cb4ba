@echo off
title Test Session API - اختبار Session API
color 0A

echo.
echo ========================================
echo 🧪 اختبار Session API
echo ========================================
echo.

echo 📦 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 🔧 تثبيت المكتبات المطلوبة...
python -m pip install flask flask-cors selenium undetected-chromedriver cryptography --quiet
echo ✅ تم تثبيت المكتبات

echo.
echo 🗄️ تهيئة قاعدة البيانات...
cd backend
python init_database.py
echo ✅ تم تهيئة قاعدة البيانات

echo.
echo 🚀 تشغيل Session API Server...
echo 🔗 Session API سيعمل على: http://localhost:8001
echo 🧪 اختبار API: http://localhost:8001/api/sessions/test
echo.

python session_api.py

pause
